{"name": "owd-client-portal", "type": "module", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "test": "vitest", "build": "vue-tsc -b && vite build", "preview": "vite preview", "lint": "eslint .", "lint:fix": "eslint . --fix", "generate-pwa-assets": "pwa-assets-generator"}, "dependencies": {"@azure/msal-browser": "^4.5.0", "@carbon/charts-vue": "^1.11.0", "@microsoft/clarity": "^1.0.0", "@vueuse/components": "^12.7.0", "@vueuse/core": "^12.7.0", "axios": "^1.8.1", "es6-promise": "^4.2.8", "html2canvas-pro": "^1.5.8", "html2pdf.js": "^0.10.3", "jspdf": "^3.0.1", "jwt-decode": "^4.0.0", "marked": "^15.0.8", "maska": "^3.1.0", "three": "^0.174.0", "three-globe": "^2.41.12", "vue": "^3.5.13", "vue-currency-input": "^3.2.1", "vue-i18n": "^11.1.1", "vue-router": "^4.5.0"}, "devDependencies": {"@antfu/eslint-config": "^4.3.0", "@stylistic/eslint-plugin-js": "^4.1.0", "@tailwindcss/vite": "^4.1.4", "@types/node": "^22.13.5", "@types/three": "^0.173.0", "@vite-pwa/assets-generator": "^0.2.6", "@vitejs/plugin-vue": "^5.2.1", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "@vue/typescript-plugin": "^2.2.4", "eslint": "^9.21.0", "eslint-plugin-format": "^1.0.1", "happy-dom": "^17.1.8", "tailwindcss": "^4.1.4", "typescript": "~5.7.2", "vite": "^6.2.0", "vite-plugin-pwa": "^0.21.1", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.0.7", "vue-tsc": "^2.2.4"}}