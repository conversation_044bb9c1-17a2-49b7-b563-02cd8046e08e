# OWD Client Portal

Client Portal for the OWD Warehouse management system.

----

#### Setup the project

Add this to your .vscode/settings.json file to enable the eslint formatter on save.

```
{
  // Enable the ESlint flat config support
  "eslint.experimental.useFlatConfig": true,

  // Disable the default formatter, use eslint instead
  "prettier.enable": false,
  "editor.formatOnSave": false,

  // Auto fix
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "never"
  },

  // Silent the stylistic rules in you IDE, but still auto fix them
  "eslint.rules.customizations": [
    { "rule": "style/*", "severity": "off" },
    { "rule": "format/*", "severity": "off" },
    { "rule": "*-indent", "severity": "off" },
    { "rule": "*-spacing", "severity": "off" },
    { "rule": "*-spaces", "severity": "off" },
    { "rule": "*-order", "severity": "off" },
    { "rule": "*-dangle", "severity": "off" },
    { "rule": "*-newline", "severity": "off" },
    { "rule": "*quotes", "severity": "off" },
    { "rule": "*semi", "severity": "off" }
  ],

  // Enable eslint for all supported languages
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue",
    "html",
    "markdown",
    "json",
    "jsonc",
    "yaml",
    "toml"
  ]
}
```

----

#### The Lib Submodule - [Global Library](https://github.com/Nerita-App/global-library)

When you clone this repo for the first time, you need to run ```git submodule update --init --recursive``` to populate the lib submodule.

----

#### Dependencies

[Vue](https://vuejs.org/)
[Vite](https://vitejs.dev/)
[Tailwind CSS](https://tailwindcss.com/)
[AntFu ESLint format config](https://github.com/antfu/eslint-config)
