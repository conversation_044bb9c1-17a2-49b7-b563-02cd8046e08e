import type { InputType } from '@lib/types/inputTypes'
import type { IdTokenClaims } from '@azure/msal-browser'

export type GuardScope = 'ADMIN' | 'CLIENT' | 'safe-route' | 'Order.Read' | 'Order.Write' | 'Inventory.Read' | 'Inventory.Write' | 'Asn.Read' | 'Asn.Write'

export interface Scope {
  id:          number
  key:         number
  value:       GuardScope
  isActive:    boolean
  createdBy:   string
  createdTs:   string
  modifiedBy:  any
  modifiedTs:  any
  description: string
}

export interface DecodedToken extends IdTokenClaims {
  scc:         string[]
  sci:         number
  signInName:  string
  given_name:  string
  family_name: string
}

export interface Facility {
  zip?:                  string
  code:                  string
  city?:                 string
  state?:                string
  display:               string
  address?:              string
  isDefault:             boolean
  description:           string
  operationalTitle:      string
  operationalPercentage: number
}

export interface AnnouncementType {
  id:    number
  title: {
    rendered: string
  }
  image:   string
  creator: string
  content: {
    rendered: string
  }
  dateCreated:     string
  isRead?:         boolean
  author?:         string
  visibility?:     string
  file?:           any
  status?:         any
  date?:           Date
  date_gmt?:       Date
  yoast_head_json: {
    og_image: {
      url: string
    }[]
  }
}

export interface ImportError<Model> {
  propertyName: keyof Model | 'record'
  errorMsg:     string
}

export type ImportStatus = 'Draft' | 'Pending' | 'Processing' | 'Failed' | 'Processed'

export interface ResolveSchema<Model> {
  key:       keyof Model
  type:      InputType
  label:     string
  error?:    string
  value?:    any
  valid?:    boolean
  anchor?:   string
  options?:  DropListOption[]
  required?: boolean
}
export interface Country {
  isDefault:    boolean
  displayName:  string
  referenceNum: string
}

export interface SearchFilter<T> {
  key:   keyof T
  label: string
  value: any
}

export interface DraftWidgets {
  totalDraftValid:       number
  totalFailedImport:     number
  totalPendingImport:    number
  totalValidationErrors: number
}

export interface Client {
  client_id:    number
  company_name: string
}

export interface LiveOrderWidgets {
  voidCount:        number
  onHoldCount:      number
  shippedCount:     number
  atWarehouseCount: number
  backorderedCount: number
}
