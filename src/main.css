@import '@lib/css/global.css';
@import '@carbon/charts-vue/styles.css';

@keyframes background-animation {
  0%,
  100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@utility animated-background {
  @apply bg-gradient-to-r from-core-20 via-core-30 to-core-20;
  background-size: 400%;
  animation: background-animation 2s ease infinite;
  -moz-animation: background-animation 2s ease infinite;
  -webkit-animation: background-animation 2s ease infinite;
}

@utility page-background-gradient { 
  @apply bg-gradient-to-t from-core-30 from-0% via-core-30 via-50% to-core-10 to-100%;
}

@utility custom-dashed-border {
  border-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='%23E0E0E0' stroke-width='3' stroke-dasharray='10 15' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e") 1;
}