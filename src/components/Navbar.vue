<script setup lang="ts">

import { Guard } from '@/plugins/guard'
import { useI18n } from 'vue-i18n'
import { confirm } from '@lib/store/confirm'
import { clientId } from '@/modules/auth/store'
import { searchModel } from '@lib/scripts/utils'
import { msalInstance } from '@lib/auth/scripts/authConfig'
import { computed, ref } from 'vue'
import { appMode, clientsList } from '@/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/button/Button.vue'
import NavbarItem from '@/components/NavbarItem.vue'

import type { NavbarSchemaItem } from '@/components/NavbarItem.vue'

defineProps<{ toggle?: boolean }>()

const { t }             = useI18n()
const isAdmin           = ref<boolean>( appMode === 'ADMIN' )
const underlined        = ref<boolean>( true )
const searchQuery       = ref<string>( null )
const toggleNavbar      = defineModel<boolean>( 'toggle' )
const activeClient      = computed(() => clientsList.value.find( c => c.id === clientId.value ))
const favoritesList     = ref<DropListOption[]>( localStorage.getItem( 'fav-clients-list' ) ? JSON.parse( localStorage.getItem( 'fav-clients-list' )! ) : [] )
const toggleClients     = ref<boolean>( isAdmin.value )
const searchClients     = ref<boolean>( false )
const showClientsBar    = computed(() => toggleClients.value && favoritesList.value.filter( f => f.id !== clientId.value ).length > 0 )
const regularClients    = computed(() => clientsList.value.filter( c => !favoritesList.value.map( f => f.id ).includes( c.id )))
const filteredClients   = computed(() => searchModel( regularClients.value, searchQuery.value, 'name' ))
const filteredFavorites = computed(() => searchModel( favoritesList.value, searchQuery.value, 'name' ))

const navBarSchema: NavbarSchemaItem[] = [
  {
    id:        1,
    name:      t( 'navigation.dashboardLabel' ),
    scope:     'safe-route',
    single:    true,
    routeName: [ 'Dashboard' ],
    path:      '/dashboard',
    icon:      {
      name: 'dashboard',
      size: 'm'
    }
  },
  {
    id:      2,
    name:    t( 'navigation.orders.label' ),
    scope:   'Order.Read',
    entries: [
      {
        id:    21,
        name:  t( 'navigation.orders.yourOrders' ),
        scope: 'Order.Read',
        icon:  {
          name: 'portal-orders',
          size: 'm'
        },
        entries: [
          {
            id:        211,
            name:      'Live Orders',
            scope:     'Order.Read',
            routeName: [ 'Live Orders' ],
            path:      '/orders/live',
          },
          {
            id:        212,
            name:      'Draft Orders',
            scope:     'Order.Read',
            routeName: [ 'Draft Orders' ],
            path:      '/orders/draft',
          },
        ]
      },
      {
        id:        22,
        name:      t( 'navigation.orders.summary' ),
        routeName: [ 'Orders Summary' ],
        path:      '/orders/summary',
        scope:     'Order.Read',
        icon:      {
          name: 'portal-orders-data',
          size: 'm'
        }
      },
      {
        id:        23,
        name:      t( 'navigation.orders.createOrder' ),
        scope:     [ 'Order.Write', 'Order.Read' ],
        routeName: [ 'Create Order' ],
        path:      '/orders/create-order',
        icon:      {
          name: 'portal-orders-create',
          size: 'm'
        }
      },
      {
        id:        24,
        name:      'Upload Bulk Orders',
        scope:     [ 'Order.Write', 'Order.Read' ],
        routeName: [ 'Upload Bulk Orders' ],
        path:      '/orders/draft/bulk-upload',
        icon:      {
          name: 'create-bulk-order',
          size: 'm'
        }
      }
    ]
  },
  {
    id:      3,
    name:    t( 'navigation.asn.label' ),
    scope:   'Asn.Read',
    entries: [
      {
        id:        31,
        name:      t( 'navigation.asn.yourASN' ),
        scope:     'Asn.Read',
        routeName: [ 'Asns' ],
        path:      '/asn',
        icon:      {
          name: 'portal-asn',
          size: 'm'
        }
      },
      {
        id:        32,
        name:      t( 'navigation.asn.createASN' ),
        scope:     [ 'Asn.Read', 'Asn.Write' ],
        routeName: [ 'Create Asn' ],
        path:      '/asn/create',
        icon:      {
          name: 'portal-asns-create',
          size: 'm'
        }
      }
    ]
  },
  {
    id:      4,
    name:    t( 'navigation.inventory.label' ),
    scope:   [ 'Inventory.Read' ],
    entries: [
      {
        id:    41,
        name:  t( 'navigation.inventory.yourInventory' ),
        scope: 'Inventory.Read',
        icon:  {
          name: 'portal-inventory',
          size: 'm'
        },
        entries: [
          {
            id:        411,
            name:      'Live Products',
            scope:     'Inventory.Read',
            routeName: [ 'Live Products' ],
            path:      '/inventory/live',
          },
          {
            id:        412,
            name:      'Draft Products',
            scope:     'Inventory.Read',
            routeName: [ 'Draft Products' ],
            path:      '/inventory/draft',
          }
        ]
      },
      {
        id:        42,
        name:      t( 'navigation.inventory.createProduct' ),
        scope:     [ 'Inventory.Read', 'Inventory.Write' ],
        routeName: [ 'Create Product' ],
        path:      '/inventory/draft/create-product',
        icon:      {
          name: 'portal-orders-create',
          size: 'm'
        }
      },
      {
        id:        43,
        name:      'Upload Bulk Products',
        scope:     [ 'Inventory.Read', 'Inventory.Write' ],
        routeName: [ 'Upload Bulk Products' ],
        path:      '/inventory/draft/bulk-upload',
        icon:      {
          name: 'create-bulk-asns',
          size: 'm'
        }
      }
    ]
  },
  {
    id:      5,
    name:    t( 'navigation.externalTools.label' ),
    scope:   'safe-route',
    entries: [
      {
        id:    51,
        name:  t( 'navigation.externalTools.reportsLabel' ),
        path:  'https://reports.owd.com/logiadhoc/',
        scope: 'safe-route',
        icon:  {
          name: 'portal-reports',
          size: 'l'
        }
      },
      {
        id:    52,
        name:  t( 'navigation.externalTools.extranetLabel' ),
        path:  'https://service.owd.com/josso/signon/login.do?josso_back_to=http://localhost:8080/webapps/josso_security_check',
        scope: 'safe-route',
        icon:  {
          name: 'portal-extranet',
          size: 'l'
        }
      }
    ]
  },
  {
    id:      6,
    name:    t( 'navigation.documentationLabel' ),
    scope:   'safe-route',
    entries: [
      {
        id:        61,
        name:      t( 'navigation.dashboardDocsLabel' ),
        scope:     'safe-route',
        routeName: [ 'DashboardDocs' ],
        path:      '/documentation/dashboard',
      },
      {
        id:        62,
        name:      t( 'navigation.ordersDocsLabel' ),
        scope:     'safe-route',
        routeName: [ 'ordersDocs' ],
        path:      '/documentation/orders',
      },
      {
        id:        63,
        name:      t( 'navigation.asnDocsLabel' ),
        scope:     'safe-route',
        routeName: [ 'ASNDocs' ],
        path:      '/documentation/asn',
      },
      {
        id:        64,
        name:      t( 'navigation.inventoryDocsLabel' ),
        scope:     'safe-route',
        routeName: [ 'InventoryDocs' ],
        path:      '/documentation/inventory',
      },
      {
        id:        66,
        name:      t( 'navigation.myOWDDocsLabel' ),
        scope:     'safe-route',
        routeName: [ 'MyOWDDocs' ],
        path:      '/documentation/my-owd'
      },
      {
        id:      65,
        name:    t( 'navigation.bulkInstructions' ),
        scope:   'safe-route',
        entries: [
          {
            id:        651,
            name:      t( 'navigation.bulkUploadOrders' ),
            scope:     'safe-route',
            routeName: [ 'BulkUploadOrders' ],
            path:      '/documentation/bulk-orders',
          },
          {
            id:        652,
            name:      t( 'navigation.bulkUploadInventory' ),
            scope:     'safe-route',
            routeName: [ 'BulkUploadInventory' ],
            path:      '/documentation/bulk-inventory',

          }
        ]
      }
    ]
  },
]

function setActiveClient( id: number ) {

  confirm({
    header:      'Switch Client',
    action:      () => { clientId.value = id },
    description: `Are you sure you want to switch the active client to <span class="text-main font-medium" >${clientsList.value.find( c => c.id === id )?.name}?</span>`
  })

}

function addFavorite( client: DropListOption ) {

  favoritesList.value.push( client )
  localStorage.setItem( 'fav-clients-list', JSON.stringify( favoritesList.value ))

}

function removeFavorite( client: DropListOption ) {

  const index = favoritesList.value.findIndex( fav => fav.id === client.id )

  if ( index > -1 ) {
    favoritesList.value.splice( index, 1 )
    localStorage.setItem( 'fav-clients-list', JSON.stringify( favoritesList.value ))
  }

}

function closeSearchClients() {

  searchQuery.value = null
  searchClients.value = false

}

async function logOut() {
  await msalInstance.logoutRedirect()
}

</script>

<template>

  <div
    class="h-full w-0 relative z-50 transition-[width]"
    :class="{
      'lg:w-72': showClientsBar,
      'lg:w-60': !showClientsBar,
    }"
  >

    <div
      class="h-full absolute top-0 grid grid-cols-[max-content_1fr] grid-rows-[1fr_max-content] bg-core-110 overflow-hidden transition-[width_left]"
      :class="{
        'w-72': showClientsBar,
        'w-60': !showClientsBar,
        'left-0 lg:left-0': toggleNavbar,
        '-left-72 lg:left-0': !toggleNavbar,
        'grid-rows-[1fr_max-content]': !isAdmin,
        'grid-rows-[max-content_1fr_max-content]': isAdmin,
      }"
    >

      <!-- Active Client -->

      <Guard :scope="appMode">

        <div class="w-full h-[3rem] col-span-2 grid grid-cols-[max-content_1fr]">

          <div
            class="w-12 h-[3rem] grid place-content-center"
            :class="{ ' border-r border-r-core-100': showClientsBar }"
          >

            <div class="size-[1.875rem] relative grid place-content-center bg-purple-600 border border-purple-300 rounded-full">

              <svg
                v-if="favoritesList.map(f => f.id).includes(activeClient?.id)"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                class="absolute -top-1 -left-1"
                width="12"
                height="11"
                viewBox="0 0 12 11"
              >
                <path d="M5.90767 1.22199C5.94183 1.13986 6.05817 1.13986 6.09233 1.22199L7.29906 4.12332C7.31346 4.15794 7.34602 4.1816 7.3834 4.18459L10.5156 4.4357C10.6043 4.44281 10.6402 4.55346 10.5727 4.61133L8.18626 6.65555C8.15779 6.67995 8.14535 6.71823 8.15405 6.7547L8.88314 9.81122C8.90378 9.89774 8.80965 9.96613 8.73374 9.91976L6.05213 8.28184C6.02012 8.26229 5.97988 8.26229 5.94787 8.28184L3.26626 9.91976C3.19035 9.96613 3.09622 9.89774 3.11686 9.81122L3.84595 6.7547C3.85465 6.71823 3.84221 6.67995 3.81374 6.65555L1.42731 4.61133C1.35976 4.55346 1.39571 4.44281 1.48438 4.4357L4.6166 4.18459C4.65398 4.1816 4.68654 4.15794 4.70094 4.12332L5.90767 1.22199Z" fill="#FDDC69" stroke="#FDDC69" />
              </svg>

              <p
                class="text-core-10 font-semibold"
                :class="{
                  'text-sm': activeClient?.description?.length < 3,
                  'text-xs': activeClient?.description?.length < 4,
                  'text-[0.5rem]': activeClient?.description?.length > 3,
                }"
              >
                {{ activeClient?.description }}
              </p>

            </div>

          </div>

          <div class="truncate w-full h-full flex">

            <div
              class="truncate h-full px-2 grow grid grid-rows-2 transition-[padding-left]"
              :class="{
                'pl-0': !showClientsBar,
                'pl-2': showClientsBar,
              }"
            >

              <p class="text-core-70 text-xs font-medium self-end">
                Client
              </p>

              <p class="truncate text-core-10 text-sm font-medium self-start -mt-1">
                {{ activeClient?.name }}
              </p>

            </div>

            <div class="px-4 flex items-center">

              <Button
                v-if="!searchClients && favoritesList.filter(f => f.id !== clientId).length > 0"
                type="box"
                size="auto"
                mode="ghost"
                class="size-6 text-text-on-color justify-center"
                @click="toggleClients = !toggleClients"
              >
                <Icon name="client-panel-open" size="s" />
              </Button>

              <Button
                v-if="!searchClients"
                type="box"
                size="auto"
                mode="ghost"
                class="size-6 text-text-on-color justify-center"
                @click="() => {
                  searchClients = true
                  toggleClients = true
                }"
              >
                <Icon name="clients" size="s" />
              </Button>

              <Button
                v-if="searchClients"
                type="box"
                size="auto"
                mode="ghost"
                icon="close"
                class="size-6 text-text-on-color"
                @click="closeSearchClients"
              />

            </div>

          </div>

        </div>

      </Guard>

      <!-- Clients Side Bar -->

      <div
        class="h-full row-span-2 py-1.5 grid place-content-start justify-center gap-y-3 overflow-hidden overflow-y-auto hide-scroll-bar transition-[width]"
        :class="{
          'w-0': !showClientsBar,
          'w-12 border-r border-r-core-100': showClientsBar && toggleNavbar,
          'lg:w-12 border-r border-r-core-100': showClientsBar,
        }"
      >

        <div
          v-for="fav in favoritesList.filter(f => f.id !== clientId)"
          :key="fav.id"
          v-tooltip.right="{ content: fav.name }"
          class="size-6 grid place-content-center bg-pink-600 border border-pink-300 rounded-full"
        >

          <Button
            mode="ghost"
            type="badge"
            @click="() => setActiveClient(fav.id)"
          >

            <p class="text-core-10 text-[0.5rem] font-semibold">
              {{ fav.description }}
            </p>

          </Button>

        </div>

      </div>

      <!-- Navigation Bar -->

      <div class="w-60 h-full overflow-hidden overflow-y-auto hide-scroll-bar">

        <div class="flex flex-col items-start gap-y-[0.25rem]">

          <Guard
            v-for="entry in navBarSchema"
            :key="entry.id"
            :scope="entry.scope"
          >

            <NavbarItem
              mode="naked"
              size="m"
              :scope="entry.scope"
              :label="entry.name"
              :options="entry.entries"
              :left-align="true"
              label-text-size="xs"
              :underlined="underlined"
              :entry="entry"
              :level="1"
            >

              <Guard
                v-for="item in entry.entries"
                :key="item.id"
                :scope="item.scope"
              >

                <div class="text-core-10 relative flex items-center">

                  <NavbarItem
                    :entry="item"
                    :level="2"
                  >

                    <Guard
                      v-for="entryItem in item.entries"
                      :key="entryItem.id"
                      :scope="entryItem.scope"
                    >

                      <NavbarItem
                        :entry="entryItem"
                        :level="3"
                      />

                    </Guard>

                  </NavbarItem>

                </div>

              </Guard>

            </NavbarItem>

          </Guard>

        </div>

      </div>

      <!-- Logout Button -->

      <div class="w-full grid">

        <div class="hover:bg-core-110 mb-4 mt-4">

          <Button
            size="s"
            mode="ghost"
            class="w-full gap-x-3"
            @click="logOut"
          >

            <Icon name="logout" size="m" class="text-text-on-color" />

            <p class="text-sm justify-self-start text-text-on-color">
              {{ $t('global.button.logout') }}
            </p>

          </Button>

        </div>

      </div>

      <!-- Clients List & Search -->

      <div
        class="w-full h-[calc(100%-3rem)] absolute top-[3rem] grid grid-rows-[max-content_1fr] bg-core-110 transition-[left]"
        :class="{
          'left-0': searchClients,
          '-left-full': !searchClients,
        }"
      >

        <!-- Search bar -->

        <div class="w-full h-8 flex items-center bg-core-100">

          <Icon name="search" class="absolute left-4 text-core-60 pointer-events-none" />

          <input
            v-model="searchQuery"
            class="text-core-50 text-sm w-full h-8 pl-11 bg-transparent outline-hidden outline-offset-0 focus-visible:outline-dashed focus-visible:outline-core-80 focus-visible:outline-1"
            placeholder="Search Clients"
          >

        </div>

        <!-- Client List -->

        <Transition name="route">

          <div v-if="searchClients" class="w-full h-full overflow-y-auto hide-scroll-bar">

            <!-- Favorites -->

            <div v-if="filteredFavorites.length > 0">

              <div class="w-full h-6 px-3 sticky top-0 z-1 flex items-center bg-core-110 border-b border-core-100">

                <p class="text-core-50 text-xs">
                  Favorites
                </p>

              </div>

              <button
                v-for="fav in filteredFavorites"
                :key="fav.id"
                class="group/client w-full h-9 relative flex items-center gap-x-2 px-3 hover:bg-core-100"
                @click="() => setActiveClient(fav.id)"
              >

                <div class="size-6 relative grid place-content-center bg-pink-600 border border-pink-300 rounded-full">

                  <svg
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    class="absolute -top-1 -left-1"
                    width="12"
                    height="11"
                    viewBox="0 0 12 11"
                  >
                    <path d="M5.90767 1.22199C5.94183 1.13986 6.05817 1.13986 6.09233 1.22199L7.29906 4.12332C7.31346 4.15794 7.34602 4.1816 7.3834 4.18459L10.5156 4.4357C10.6043 4.44281 10.6402 4.55346 10.5727 4.61133L8.18626 6.65555C8.15779 6.67995 8.14535 6.71823 8.15405 6.7547L8.88314 9.81122C8.90378 9.89774 8.80965 9.96613 8.73374 9.91976L6.05213 8.28184C6.02012 8.26229 5.97988 8.26229 5.94787 8.28184L3.26626 9.91976C3.19035 9.96613 3.09622 9.89774 3.11686 9.81122L3.84595 6.7547C3.85465 6.71823 3.84221 6.67995 3.81374 6.65555L1.42731 4.61133C1.35976 4.55346 1.39571 4.44281 1.48438 4.4357L4.6166 4.18459C4.65398 4.1816 4.68654 4.15794 4.70094 4.12332L5.90767 1.22199Z" fill="#FDDC69" stroke="#FDDC69" />
                  </svg>

                  <p class="text-core-10 text-[0.5rem] font-semibold">
                    {{ fav.description }}
                  </p>

                </div>

                <p class="text-sm text-core-50">
                  {{ fav.name }}
                </p>

                <div class="hidden group-hover/client:flex h-full absolute top-0 right-2 items-center">

                  <Button
                    size="auto"
                    type="badge"
                    icon="close"
                    mode="secondary"
                    class="size-6"
                    @click.stop="() => removeFavorite(fav)"
                  />

                </div>

              </button>

            </div>

            <!-- Rest of the clients -->

            <div>

              <div class="w-full h-6 px-3 sticky top-0 z-1 flex items-center bg-core-110 border-b border-core-100">

                <p class="text-core-50 text-xs">
                  All Clients
                </p>

              </div>

              <div
                v-if="filteredClients.length === 0"
                class="w-full p-10 flex items-center justify-center space-x-4"
              >

                <Icon size="m" name="folder" class="text-core-50" />

                <p class="text-xs text-core-50">
                  {{ $t('global.phrase.noRecordsFound', { name: 'Clients' }) }}
                </p>

              </div>

              <button
                v-for="client in filteredClients"
                :key="client.id"
                class="group/client truncate w-full h-9 relative flex items-center gap-x-2 px-3 hover:bg-core-100"
                @click="() => setActiveClient(client.id)"
              >

                <div class="truncate size-6 grid place-content-center bg-purple-600 border border-purple-300 rounded-full">
                  <p class="truncate text-clip text-core-10 text-[0.5rem] font-semibold">
                    {{ client.description }}
                  </p>
                </div>

                <p class="truncate text-sm text-core-50">
                  {{ client.name }}
                </p>

                <div class="hidden group-hover/client:flex h-full absolute top-0 right-2 items-center">

                  <Button
                    size="auto"
                    type="badge"
                    class="size-6"
                    @click.stop="() => addFavorite(client)"
                  >
                    <svg
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      width="12"
                      height="11"
                      viewBox="0 0 12 11"
                    >
                      <path d="M5.90767 1.22199C5.94183 1.13986 6.05817 1.13986 6.09233 1.22199L7.29906 4.12332C7.31346 4.15794 7.34602 4.1816 7.3834 4.18459L10.5156 4.4357C10.6043 4.44281 10.6402 4.55346 10.5727 4.61133L8.18626 6.65555C8.15779 6.67995 8.14535 6.71823 8.15405 6.7547L8.88314 9.81122C8.90378 9.89774 8.80965 9.96613 8.73374 9.91976L6.05213 8.28184C6.02012 8.26229 5.97988 8.26229 5.94787 8.28184L3.26626 9.91976C3.19035 9.96613 3.09622 9.89774 3.11686 9.81122L3.84595 6.7547C3.85465 6.71823 3.84221 6.67995 3.81374 6.65555L1.42731 4.61133C1.35976 4.55346 1.39571 4.44281 1.48438 4.4357L4.6166 4.18459C4.65398 4.1816 4.68654 4.15794 4.70094 4.12332L5.90767 1.22199Z" fill="#FDDC69" stroke="#FDDC69" />
                    </svg>
                  </Button>

                </div>

              </button>

            </div>

          </div>

        </Transition>

      </div>

    </div>

  </div>

</template>
