import { flushPromises, mount } from '@vue/test-utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'

import InventoryProductDetails from '@/components/InventoryProductDetails.vue'

import { getDraftProduct, getProduct } from '@/modules/inventory/store'

// Mock the dependencies
vi.mock( '@/store', () => ({
  facilitiesList: {
    value: [
      { id: 'NYC', name: 'New York' },
      { id: 'LA', name: 'Los Angeles' }
    ]
  }
}))

vi.mock( '@/modules/inventory/store', () => ({
  getProduct:      vi.fn(),
  getDraftProduct: vi.fn()
}))

// Mock sample data
const mockLiveProduct = {
  id:                   1,
  sku:                  'ABC123',
  title:                'Test Product',
  description:          'Test Description',
  supplier:             'Test Supplier',
  height:               '10 cm',
  width:                '20 cm',
  length:               '30 cm',
  cubicFeet:            '0.5',
  size:                 'Medium',
  color:                'Red',
  reorderAlertQuantity: 10,
  availableStockLevels: [
    {
      facilityCode:   'NYC',
      facilityName:   'New York',
      unitsAvailable: 20,
      lots:           []
    }
  ]
}

const mockDraftProduct = {
  id:                   2,
  sku:                  'DEF456',
  title:                'Draft Product',
  description:          'Draft Description',
  supplier:             'Draft Supplier',
  height:               '15 cm',
  width:                '25 cm',
  length:               '35 cm',
  cubicFeet:            '0.7',
  size:                 'Large',
  color:                'Blue',
  reorderAlertQuantity: 15
}

const mockKitProduct = {
  ...mockLiveProduct,
  kitComponents: [
    {
      id:                  101,
      sku:                 'KIT-1',
      availableByFacility: [
        { facilityCode: 'NYC', unitsAvailable: 5 },
        { facilityCode: 'LA', unitsAvailable: 2 }
      ]
    },
    {
      id:                  102,
      sku:                 'KIT-2',
      availableByFacility: [
        { facilityCode: 'NYC', unitsAvailable: 0 },
        { facilityCode: 'LA', unitsAvailable: 7 }
      ]
    }
  ]
}

describe( 'inventoryProductDetails.vue', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe( 'live product view', () => {
    it( 'fetches and displays live product data correctly', async () => {
      // Mock the API response
      vi.mocked( getProduct ).mockResolvedValue({ payload: mockLiveProduct } as any )

      const wrapper = mount( InventoryProductDetails, {
        props: {
          itemId: 1
        }
      })

      // Wait for promise to resolve
      await flushPromises()

      // Check if product SKU is displayed correctly
      expect( wrapper.find( '[data-element="product-sku"]' ).text()).toContain( 'ABC123' )

      // Check if product details are rendered
      expect( wrapper.find( '[data-element="inventory-heading"]' ).text()).toBe( 'Inventory' )
      expect( wrapper.find( '[data-element="details-heading"]' ).text()).toBe( 'Details' )
      expect( wrapper.find( '[data-element="attributes-heading"]' ).text()).toBe( 'Attributes' )
    })

    it( 'displays inventory status correctly for sufficient stock', async () => {
      const productWithSufficientStock = {
        ...mockLiveProduct,
        availableStockLevels: [
          {
            facilityCode:   'NYC',
            facilityName:   'New York',
            unitsAvailable: 20, // Greater than reorderAlertQuantity (10)
            lots:           []
          }
        ]
      }

      vi.mocked( getProduct ).mockResolvedValue({ payload: productWithSufficientStock } as any )

      const wrapper = mount( InventoryProductDetails, {
        props: {
          itemId: 1
        }
      })

      await flushPromises()

      // Find the facility card
      const facilityElement = wrapper.find( '[data-element="facility-NYC"]' )
      expect( facilityElement.exists()).toBe( true )

      // Check if it has success styling (using class check as an approximation)
      expect( facilityElement.classes()).toContain( 'bg-tag-background-green' )
      expect( facilityElement.classes()).toContain( 'border-tag-border-green' )

      // Check stock value
      expect( wrapper.find( '[data-element="facility-stock-NYC"]' ).text()).toBe( '20' )
    })

    it( 'displays inventory status correctly for low stock', async () => {
      const productWithLowStock = {
        ...mockLiveProduct,
        availableStockLevels: [
          {
            facilityCode:   'NYC',
            facilityName:   'New York',
            unitsAvailable: 5, // Lower than reorderAlertQuantity (10) but greater than 0
            lots:           []
          }
        ]
      }

      vi.mocked( getProduct ).mockResolvedValue({ payload: productWithLowStock } as any )

      const wrapper = mount( InventoryProductDetails, {
        props: {
          itemId: 1
        }
      })

      await flushPromises()

      // Find the facility card
      const facilityElement = wrapper.find( '[data-element="facility-NYC"]' )
      expect( facilityElement.exists()).toBe( true )

      // Check if it has warning styling
      expect( facilityElement.classes()).toContain( 'bg-tag-background-orange' )
      expect( facilityElement.classes()).toContain( 'border-tag-border-orange' )

      // Check stock value
      expect( wrapper.find( '[data-element="facility-stock-NYC"]' ).text()).toBe( '5' )
    })

    it( 'displays inventory status correctly for out of stock', async () => {
      const productWithNoStock = {
        ...mockLiveProduct,
        availableStockLevels: [
          {
            facilityCode:   'NYC',
            facilityName:   'New York',
            unitsAvailable: 0, // Zero stock
            lots:           []
          }
        ]
      }

      vi.mocked( getProduct ).mockResolvedValue({ payload: productWithNoStock } as any )

      const wrapper = mount( InventoryProductDetails, {
        props: {
          itemId: 1
        }
      })

      await flushPromises()

      // Find the facility card
      const facilityElement = wrapper.find( '[data-element="facility-NYC"]' )
      expect( facilityElement.exists()).toBe( true )

      // Check if it has error styling
      expect( facilityElement.classes()).toContain( 'bg-tag-background-red' )
      expect( facilityElement.classes()).toContain( 'border-tag-border-red' )

      // Check stock value
      expect( wrapper.find( '[data-element="facility-stock-NYC"]' ).text()).toBe( '0' )
    })

    it( 'displays inventory status correctly for not available stock', async () => {
      const productWithNullStock = {
        ...mockLiveProduct,
        availableStockLevels: [
          {
            facilityCode:   'NYC',
            facilityName:   'New York',
            unitsAvailable: null, // Null stock
            lots:           []
          }
        ]
      }

      vi.mocked( getProduct ).mockResolvedValue({ payload: productWithNullStock } as any )

      const wrapper = mount( InventoryProductDetails, {
        props: {
          itemId: 1
        }
      })

      await flushPromises()

      // Find the facility card
      const facilityElement = wrapper.find( '[data-element="facility-NYC"]' )
      expect( facilityElement.exists()).toBe( true )

      // Check if it has core styling
      expect( facilityElement.classes()).toContain( 'bg-tag-background-gray' )
      expect( facilityElement.classes()).toContain( 'border-tag-border-gray' )

      // Check stock value
      expect( wrapper.find( '[data-element="facility-stock-NYC"]' ).text()).toBe( '/' )
    })

    it( 'adds missing facilities with null stock', async () => {
      // Product with only one facility
      const productWithOneFacility = {
        ...mockLiveProduct,
        availableStockLevels: [
          {
            facilityCode:   'NYC',
            facilityName:   'New York',
            unitsAvailable: 20,
            lots:           []
          }
          // No LA facility
        ]
      }

      vi.mocked( getProduct ).mockResolvedValue({ payload: productWithOneFacility } as any )

      const wrapper = mount( InventoryProductDetails, {
        props: {
          itemId: 1
        }
      })

      await flushPromises()

      // Should automatically add LA facility with null stock
      const nyFacility = wrapper.find( '[data-element="facility-NYC"]' )
      const laFacility = wrapper.find( '[data-element="facility-LA"]' )

      expect( nyFacility.exists()).toBe( true )
      expect( laFacility.exists()).toBe( true )

      // NYC should have value
      expect( wrapper.find( '[data-element="facility-stock-NYC"]' ).text()).toBe( '20' )

      // LA should have placeholder
      expect( wrapper.find( '[data-element="facility-stock-LA"]' ).text()).toBe( '/' )
    })
  })

  describe( 'draft product view', () => {
    it( 'fetches and displays draft product data correctly', async () => {
      // Mock the API response
      vi.mocked( getDraftProduct ).mockResolvedValue({ payload: mockDraftProduct } as any )

      const wrapper = mount( InventoryProductDetails, {
        props: {
          itemId: 2,
          type:   'Draft'
        }
      })

      // Wait for promise to resolve
      await flushPromises()

      // Check if product SKU is displayed correctly
      expect( wrapper.find( '[data-element="product-sku"]' ).text()).toContain( 'DEF456' )

      // Check if product details are rendered
      expect( wrapper.find( '[data-element="inventory-heading"]' ).text()).toBe( 'Inventory' )
      expect( wrapper.find( '[data-element="details-heading"]' ).text()).toBe( 'Details' )
      expect( wrapper.find( '[data-element="attributes-heading"]' ).text()).toBe( 'Attributes' )
    })
  })

  describe( 'kit product view', () => {

    it( 'displays kit components correctly', async () => {
      // Mock the API response
      vi.mocked( getProduct ).mockResolvedValue({ payload: mockKitProduct } as any )

      const wrapper = mount( InventoryProductDetails, {
        props: {
          itemId: 1
        }
      })

      // Wait for promise to resolve
      await flushPromises()

      // Should show kit inventory instead of standard inventory
      expect( wrapper.find( '[data-element="kit-inventory"]' ).exists()).toBe( true )
      expect( wrapper.find( '[data-element="standard-inventory"]' ).exists()).toBe( false )

      // Should show kit components
      expect( wrapper.find( '[data-element="kit-component-101"]' ).exists()).toBe( true )
      expect( wrapper.find( '[data-element="kit-component-102"]' ).exists()).toBe( true )

      // Find all kit components
      const kitComponents = wrapper.findAll( '[data-element^="kit-component-"]' )
      expect( kitComponents.length ).toBe( 2 )

      // Check that the first component exists
      const firstKitComponent = wrapper.find( '[data-element="kit-component-101"]' )
      expect( firstKitComponent.exists()).toBe( true )

      // Check that it contains the SKU
      const kitSku = firstKitComponent.find( '[data-element="kit-sku"]' )
      expect( kitSku.exists()).toBe( true )
      expect( kitSku.text()).toBe( 'KIT-1' )
    })

  })

  describe( 'user interaction', () => {
    it( 'emits close event when close button is clicked', async () => {
      vi.mocked( getProduct ).mockResolvedValue({ payload: mockLiveProduct } as any )

      const wrapper = mount( InventoryProductDetails, {
        props: {
          itemId: 1
        }
      })

      await flushPromises()

      // Click the close button
      await wrapper.find( '[data-button="close"]' ).trigger( 'click' )

      // Check if close event is emitted
      expect( wrapper.emitted().close ).toBeTruthy()
      expect( wrapper.emitted().close.length ).toBe( 1 )
    })
  })
})
