import { mount } from '@vue/test-utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { nextTick } from 'vue'
import PublishBar from '@/components/PublishBar.vue'
import { setNotificationOptions } from '@lib/store/snackbar'
import { publishDraftOrders } from '@/modules/orders/store'
import { publishDraftProducts } from '@/modules/inventory/store'

// Mock external dependencies
vi.mock( '@lib/store/snackbar', () => ({
  setNotificationOptions: vi.fn()
}))

vi.mock( '@/modules/orders/store', () => ({
  publishDraftOrders: vi.fn()
}))

vi.mock( '@/modules/inventory/store', () => ({
  publishDraftProducts: vi.fn()
}))

describe( 'publishBar', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  // Helper function to create wrapper with props
  const createWrapper = ( props = {}) => {
    return mount( PublishBar, {
      props: {
        id:   1,
        type: 'order',
        ...props
      }
    })
  }

  describe( 'initial Rendering', () => {
    it( 'renders correctly for non-empty order', () => {
      const wrapper = createWrapper({ type: 'order', isEmpty: false })
      expect( wrapper.text()).toContain( 'This order is ready to go live' )
      expect( wrapper.find( '[data-element="order-title"]' ).exists()).toBe( true )
    })

    it( 'renders correctly for empty order', () => {
      const wrapper = createWrapper({ type: 'order', isEmpty: true })
      expect( wrapper.text()).toContain( 'This order is empty' )
      expect( wrapper.find( '[data-element="order-title"]' ).exists()).toBe( true )
    })

    it( 'renders correctly for non-empty product', () => {
      const wrapper = createWrapper({ type: 'product', isEmpty: false })
      expect( wrapper.text()).toContain( 'This product is ready to go live' )
      expect( wrapper.find( '[data-element="product-title"]' ).exists()).toBe( true )
    })

    it( 'renders correctly for empty product', () => {
      const wrapper = createWrapper({ type: 'product', isEmpty: true })
      expect( wrapper.text()).toContain( 'This kit is empty' )
      expect( wrapper.find( '[data-element="product-title"]' ).exists()).toBe( true )
    })
  })

  describe( 'publishing Flow', () => {
    it( 'shows confirmation dialog when publish button is clicked', async () => {
      const wrapper = createWrapper({ type: 'order', isEmpty: false })

      // Initial state
      expect( wrapper.text()).toContain( 'Publish' )

      // Click publish button
      await wrapper.find( 'button' ).trigger( 'click' )

      // Check confirmation state
      expect( wrapper.text()).toContain( 'Confirm' )
    })

    it( 'publishes order when confirmed', async () => {
      const wrapper = createWrapper({ type: 'order', isEmpty: false })

      // Setup mock return value
      // Setup mock return value with correct payload type
      vi.mocked( publishDraftOrders ).mockResolvedValue({
        status:  200,
        payload: null,
        error:   null
      })

      // Click publish button to show confirmation
      await wrapper.find( 'button' ).trigger( 'click' )

      // Click confirm button
      await wrapper.find( '[data-button="confirm"]' ).trigger( 'click' )

      // Verify publishDraftOrders was called
      expect( publishDraftOrders ).toHaveBeenCalledWith( [ 1 ] )
      expect( setNotificationOptions ).toHaveBeenCalledWith({
        message: 'Order has been published and is now pending.'
      })
    })

    it( 'publishes product when confirmed', async () => {
      const wrapper = createWrapper({ type: 'product', isEmpty: false })

      // Setup mock return value
      vi.mocked( publishDraftProducts ).mockResolvedValue({
        status:  200,
        payload: null,
        error:   null
      })

      // Click publish button to show confirmation
      await wrapper.find( 'button' ).trigger( 'click' )

      // Click confirm button
      await wrapper.find( '[data-button="confirm"]' ).trigger( 'click' )

      // Verify publishDraftProducts was called
      expect( publishDraftProducts ).toHaveBeenCalledWith( [ 1 ] )
      expect( setNotificationOptions ).toHaveBeenCalledWith({
        message: 'Product has been published and is now pending.'
      })
    })

    it( 'handles publish errors gracefully', async () => {
      const wrapper = createWrapper({ type: 'order', isEmpty: false })

      // Setup mock to return error
      vi.mocked( publishDraftOrders ).mockResolvedValue({
        status:  200,
        payload: null,
        error:   []
      })

      // Click publish button to show confirmation
      await wrapper.find( 'button' ).trigger( 'click' )

      // Click confirm button
      await wrapper.find( '[data-button="confirm"]' ).trigger( 'click' )

      // Verify error handling
      expect( setNotificationOptions ).not.toHaveBeenCalled()
    })
  })

  describe( 'uI States', () => {
    it( 'shows loading state while publishing', async () => {
      const wrapper = createWrapper({ type: 'order', isEmpty: false })

      // Setup mock with delay to test loading state
      vi.mocked( publishDraftOrders ).mockImplementation(() =>
        new Promise( resolve => setTimeout(() => resolve({ error: null, status: null, payload: null }), 100 ))
      )

      // Click publish button to show confirmation
      await wrapper.find( 'button' ).trigger( 'click' )

      // Click confirm button
      await wrapper.find( '[data-button="confirm"]' ).trigger( 'click' )

      // Check loading state
      expect( wrapper.text()).toContain( 'Publishing' )

      // Wait for publish to complete
      await new Promise( resolve => setTimeout( resolve, 100 ))
      await nextTick()
      expect( wrapper.text()).toContain( 'This order is ready to go live. Confirm' )

    })

    it( 'hides publish button when canPublish is false', () => {
      const wrapper = createWrapper({ type: 'order', isEmpty: false, canPublish: false })
      expect( wrapper.text()).not.toContain( 'Publish' )
    })

    it( 'cancels publish when cancel button is clicked', async () => {
      const wrapper = createWrapper({ type: 'order', isEmpty: false })

      expect( wrapper.find( 'button' ).text()).toBe( 'Publish Order' )
      // Click publish button to show confirmation
      await wrapper.find( 'button' ).trigger( 'click' )
      // Click cancel button
      await wrapper.find( '[data-button="cancel"]' ).trigger( 'click' )

      // Verify we're back to initial state
      expect( wrapper.text()).toContain( 'Publish' )
      expect( wrapper.text()).not.toContain( 'Confirm' )
    })
  })

})
