import { ref } from 'vue'
import { mount } from '@vue/test-utils'
import { describe, expect, it, vi } from 'vitest'

import Icon from '@lib/components/blocks/Icon.vue'
import Navbar from '@/components/Navbar.vue'
import NavbarItem from '@/components/NavbarItem.vue'
import NavbarDrawer from '@/components/NavbarDrawer.vue'

const mockRoute = ref({
  name:     'Dashboard',
  path:     '/dashboard',
  params:   { },
  fullPath: '/dashboard',
  query:    {},
})

vi.mock( 'vue-router', async ( importOriginal ) => {

  const original = await importOriginal() as any

  return {
    ...original,
    useRoute:  vi.fn(() => mockRoute.value ),
    useRouter: vi.fn(() => ({
      currentRoute: { value: mockRoute.value },
      push:         vi.fn(),
      resolve:      vi.fn(() => ({ href: mockRoute.value.path })),
    })),
  }

})

function createNavbarWrapper() {
  return mount( Navbar, {
    global: {
      stubs: {
        NavbarItem,
        NavbarDrawer,
        Guard: true
      },
    },
  })
}

describe( 'components::Navbar', () => {

  describe( 'internal level 1 navbar RouterLink item', () => {

    it( 'renders a RouterLink component when entry has no entries, renders the active item blue line, passes the correct props, sets the text to entry.name', async () => {

      const wrapper = createNavbarWrapper()

      const navbarItems = wrapper.findAllComponents( NavbarItem )

      const levelOneLink = navbarItems[0]

      expect( levelOneLink.exists()).toBe( true )
      expect( levelOneLink.props().entry.name ).toBe( 'Dashboard' )
      expect( levelOneLink.props().entry.path ).toBe( '/dashboard' )
      expect( levelOneLink.props().entry.routeName ).toStrictEqual( [ 'Dashboard' ] )
      expect( levelOneLink.props().entry.scope ).toBe( 'safe-route' )

      expect( levelOneLink.attributes()['data-element'] ).toBe( 'RouterLink' )

      expect( levelOneLink.find( '[data-element="blue-line"]' ).exists()).toBe( true )
      expect( levelOneLink.find( '[data-element="entry-name"]' ).exists()).toBe( true )
      expect( levelOneLink.find( '[data-element="entry-name"]' ).text()).toBe( 'Dashboard' )

    })

    it( 'renders a RouterLink component when entry has no entries, hides the active item blue line, passes the correct props, sets the text to entry.name', async () => {

      mockRoute.value = {
        name:     'Asn',
        path:     '/asn',
        params:   {},
        fullPath: '/asn',
        query:    {},
      }

      const wrapper = createNavbarWrapper()

      const navbarItems = wrapper.findAllComponents( NavbarItem )

      const levelOneLink = navbarItems[0]

      expect( levelOneLink.exists()).toBe( true )

      expect( levelOneLink.props().entry.name ).toBe( 'Dashboard' )
      expect( levelOneLink.props().entry.path ).toBe( '/dashboard' )
      expect( levelOneLink.props().entry.routeName ).toStrictEqual( [ 'Dashboard' ] )
      expect( levelOneLink.props().entry.scope ).toBe( 'safe-route' )
      expect( levelOneLink.props().entry.scope ).toBe( 'safe-route' )

      expect( levelOneLink.attributes()['data-element'] ).toBe( 'RouterLink' )
      expect( levelOneLink.attributes().href ).toBe( '/dashboard' )

      // If it's an internal route, the target should be default
      expect( levelOneLink.attributes().target ).toBe( 'default' )

      expect( levelOneLink.find( '[data-element="blue-line"]' ).exists()).toBe( false )
      expect( levelOneLink.find( '[data-element="entry-name"]' ).exists()).toBe( true )
      expect( levelOneLink.find( '[data-element="entry-name"]' ).text()).toBe( 'Dashboard' )

    })

  })

  describe( 'internal level 2 navbar RouterLink item', () => {

    it( 'renders an inactive RouterLink component when entry has no entries, hides the active item blue line, passes the correct props, sets the text to entry.name', async () => {

      mockRoute.value = {
        name:     'Asn',
        path:     '/asn',
        params:   {},
        fullPath: '/asn',
        query:    {},
      }

      const wrapper = createNavbarWrapper()

      const navbarItems = wrapper.findAllComponents( NavbarItem )

      const levelOneDropdown = navbarItems[1]
      const levelOneDropdownElements = levelOneDropdown.findAllComponents( NavbarItem )

      // Make sure it's nested
      const levelTwoRouterLink = levelOneDropdownElements[1]

      expect( levelTwoRouterLink.attributes()['data-element'] ).toBe( 'RouterLink' )
      expect( levelTwoRouterLink.attributes().label ).toBe( 'Live Orders' )
      expect( levelTwoRouterLink.attributes().href ).toBe( '/orders/live' )
      expect( levelTwoRouterLink.attributes().level ).toBe( '3' )
      expect( levelTwoRouterLink.attributes().target ).toBe( 'default' )

      // Item is inactive so don't show the blue line
      expect( levelTwoRouterLink.find( '[data-element="blue-line"]' ).exists()).toBe( false )

      expect( levelTwoRouterLink.find( '[data-element="entry-name"]' ).exists()).toBe( true )
      expect( levelTwoRouterLink.find( '[data-element="entry-name"]' ).text()).toBe( 'Live Orders' )

      const icons = levelTwoRouterLink.findAllComponents( Icon )

      const icon = icons.find( i => i.attributes()['data-element'] === 'link-icon' )

      // Level 3 RouterLinks doesn't have icons
      expect( icon ).toBeUndefined()

    })

    it( 'renders an active RouterLink component when entry has no entries, renders the active item blue line, passes the correct props, sets the text to entry.name', async () => {

      mockRoute.value = {
        name:     'Live Orders',
        path:     '/orders/live',
        params:   {},
        fullPath: '/orders/live',
        query:    {},
      }

      const wrapper = createNavbarWrapper()

      const navbarItems = wrapper.findAllComponents( NavbarItem )

      const levelOneDropdown = navbarItems[1]
      const levelOneDropdownElements = levelOneDropdown.findAllComponents( NavbarItem )

      // Make sure it's nested
      const levelTwoRouterLink = levelOneDropdownElements[1]

      expect( levelTwoRouterLink.attributes()['data-element'] ).toBe( 'RouterLink' )
      expect( levelTwoRouterLink.attributes().label ).toBe( 'Live Orders' )
      expect( levelTwoRouterLink.attributes().href ).toBe( '/orders/live' )
      expect( levelTwoRouterLink.attributes().level ).toBe( '3' )
      expect( levelTwoRouterLink.attributes().target ).toBe( 'default' )

      // Item is active so show the blue line
      expect( levelTwoRouterLink.find( '[data-element="blue-line"]' ).exists()).toBe( true )

      expect( levelTwoRouterLink.find( '[data-element="entry-name"]' ).exists()).toBe( true )
      expect( levelTwoRouterLink.find( '[data-element="entry-name"]' ).text()).toBe( 'Live Orders' )

      const icons = levelTwoRouterLink.findAllComponents( Icon )

      const icon = icons.find( i => i.attributes()['data-element'] === 'link-icon' )

      // Level 3 RouterLinks doesn't have icons
      expect( icon ).toBeUndefined()

    })

  })

  describe( 'internal level 3 navbar RouterLink item', () => {

    it( 'renders an inactive RouterLink component when entry has no entries, hides the active item blue line, passes the correct props, sets the text to entry.name', async () => {

      mockRoute.value = {
        name:     'Asn',
        path:     '/asn',
        params:   {},
        fullPath: '/asn',
        query:    {},
      }

      const wrapper = createNavbarWrapper()

      const navbarItems = wrapper.findAllComponents( NavbarItem )

      const levelOneDropdown = navbarItems[1]
      const levelOneDropdownElements = levelOneDropdown.findAllComponents( NavbarItem )

      // Make sure it's nested
      const levelTwoRouterLink = levelOneDropdownElements[1]

      expect( levelTwoRouterLink.attributes()['data-element'] ).toBe( 'RouterLink' )
      expect( levelTwoRouterLink.attributes().label ).toBe( 'Live Orders' )
      expect( levelTwoRouterLink.attributes().href ).toBe( '/orders/live' )
      expect( levelTwoRouterLink.attributes().level ).toBe( '3' )
      expect( levelTwoRouterLink.attributes().target ).toBe( 'default' )

      // Item is inactive so don't show the blue line
      expect( levelTwoRouterLink.find( '[data-element="blue-line"]' ).exists()).toBe( false )

      expect( levelTwoRouterLink.find( '[data-element="entry-name"]' ).exists()).toBe( true )
      expect( levelTwoRouterLink.find( '[data-element="entry-name"]' ).text()).toBe( 'Live Orders' )

      const icons = levelTwoRouterLink.findAllComponents( Icon )

      const icon = icons.find( i => i.attributes()['data-element'] === 'link-icon' )

      // Level 3 RouterLinks doesn't have icons
      expect( icon ).toBeUndefined()

    })

    it( 'renders an active RouterLink component when entry has no entries, renders the active item blue line, passes the correct props, sets the text to entry.name', async () => {

      mockRoute.value = {
        name:     'Live Orders',
        path:     '/orders/live',
        params:   {},
        fullPath: '/orders/live',
        query:    {},
      }

      const wrapper = createNavbarWrapper()

      const navbarItems = wrapper.findAllComponents( NavbarItem )

      const levelOneDropdown = navbarItems[1]
      const levelOneDropdownElements = levelOneDropdown.findAllComponents( NavbarItem )

      // Make sure it's nested
      const levelTwoRouterLink = levelOneDropdownElements[1]

      expect( levelTwoRouterLink.attributes()['data-element'] ).toBe( 'RouterLink' )
      expect( levelTwoRouterLink.attributes().label ).toBe( 'Live Orders' )
      expect( levelTwoRouterLink.attributes().href ).toBe( '/orders/live' )
      expect( levelTwoRouterLink.attributes().level ).toBe( '3' )
      expect( levelTwoRouterLink.attributes().target ).toBe( 'default' )

      // Item is active so show the blue line
      expect( levelTwoRouterLink.find( '[data-element="blue-line"]' ).exists()).toBe( true )

      expect( levelTwoRouterLink.find( '[data-element="entry-name"]' ).exists()).toBe( true )
      expect( levelTwoRouterLink.find( '[data-element="entry-name"]' ).text()).toBe( 'Live Orders' )

      const icons = levelTwoRouterLink.findAllComponents( Icon )

      const icon = icons.find( i => i.attributes()['data-element'] === 'link-icon' )

      // Level 3 RouterLinks doesn't have icons
      expect( icon ).toBeUndefined()

    })

  })

  describe( 'level 1 navbar dropdown item', () => {

    it( 'renders a NavbarDrawer component when entry has no entries, passes the correct props, sets the text to entry.name', async () => {

      const wrapper = createNavbarWrapper()

      const navbarItems = wrapper.findAllComponents( NavbarItem )

      const levelOneDropdown = navbarItems[1]

      expect( levelOneDropdown.exists()).toBe( true )
      expect( levelOneDropdown.props().entry.name ).toBe( 'Orders' )
      expect( levelOneDropdown.props().entry.path ).toBeUndefined()
      expect( levelOneDropdown.props().entry.routeName ).toBeUndefined()
      expect( levelOneDropdown.props().entry.scope ).toBe( 'Order.Read' )

      expect( levelOneDropdown.attributes()['data-element'] ).toBe( 'NavbarDrawer' )

      expect( levelOneDropdown.find( '[data-button="toggle"]' ).exists()).toBe( true )
      expect( levelOneDropdown.find( '[data-icon="icon"]' ).exists()).toBe( true )
      expect( levelOneDropdown.find( '[data-element="label"]' ).text()).toBe( 'Orders' )
      expect( levelOneDropdown.find( '[data-element="underline"]' ).exists()).toBe( true )

    })

  })

  describe( 'level 2 navbar dropdown item', () => {

    it( 'renders a NavbarDrawer component when entry has no entries, passes the correct props, sets the text to entry.name', async () => {

      const wrapper = createNavbarWrapper()

      const navbarItems = wrapper.findAllComponents( NavbarItem )

      const levelTwoDropdown = navbarItems[2]

      expect( levelTwoDropdown.exists()).toBe( true )
      expect( levelTwoDropdown.props().entry.name ).toBe( 'Your Orders' )
      expect( levelTwoDropdown.props().entry.path ).toBeUndefined()
      expect( levelTwoDropdown.props().entry.routeName ).toBeUndefined()
      expect( levelTwoDropdown.props().entry.scope ).toBe( 'Order.Read' )

      expect( levelTwoDropdown.attributes()['data-element'] ).toBe( 'NavbarDrawer' )

      expect( levelTwoDropdown.find( '[data-button="toggle"]' ).exists()).toBe( true )
      expect( levelTwoDropdown.find( '[data-icon="icon"]' ).exists()).toBe( true )
      expect( levelTwoDropdown.find( '[data-element="label"]' ).text()).toBe( 'Your Orders' )
      expect( levelTwoDropdown.find( '[data-element="underline"]' ).exists()).toBe( true )

      const icons = levelTwoDropdown.findAllComponents( Icon )

      const icon = icons.find( i => i.attributes()['data-icon'] === 'icon' )

      expect( icon.exists()).toBe( true )

      expect( icon.props().name ).toBe( 'portal-orders' )

    })

  })

  describe( 'external link', () => {
    const wrapper = createNavbarWrapper()
    const navbarItems = wrapper.findAllComponents( NavbarItem )
    const externalLink = navbarItems.find( i => i.props()?.entry?.name === 'Reports' )

    it( 'sets ', () => {
      expect( externalLink.exists()).toBe( true )
      expect( externalLink.attributes().target ).toBe( '_blank' )
    })
  })

  describe( 'no scopes', () => {

    const wrapper = mount( Navbar, {
      global: {
        stubs: {
          NavbarItem,
          NavbarDrawer,
          Guard: false
        },
      },
    })

    const navbarItems = wrapper.findAllComponents( NavbarItem )

    it( 'renders the 4 navbar items that have no scopes', () => {

      const internalDashboardLink   = navbarItems[0]
      const externalToolsDropdown   = navbarItems[1]
      const externalReportsLink     = navbarItems[2]
      const extranetLink            = navbarItems[3]
      const documentationDropdown   = navbarItems[4]
      const dashboardDocsLink       = navbarItems[5]
      const ordersDocsLink          = navbarItems[6]
      const ASNDocsLink             = navbarItems[7]
      const inventoryDocsLink       = navbarItems[8]
      const myOWDDocsLink           = navbarItems[9]
      const bulkUploadDropdown      = navbarItems[10]
      const bulkUploadOrdersLink    = navbarItems[11]
      const bulkUploadInventoryLink = navbarItems[12]

      expect( navbarItems.length ).toBe( 13 )

      expect( internalDashboardLink.props().entry.name ).toBe( 'Dashboard' )
      expect( internalDashboardLink.attributes()['data-element'] ).toBe( 'RouterLink' )
      expect( internalDashboardLink.props().entry.scope ).toBe( 'safe-route' )

      expect( externalToolsDropdown.props().entry.name ).toBe( 'External Tools' )
      expect( externalToolsDropdown.attributes()['data-element'] ).toBe( 'NavbarDrawer' )
      expect( externalToolsDropdown.props().entry.scope ).toBe( 'safe-route' )

      expect( extranetLink.props().entry.name ).toBe( 'Extranet' )
      expect( extranetLink.attributes()['data-element'] ).toBe( 'RouterLink' )
      expect( extranetLink.props().entry.scope ).toBe( 'safe-route' )

      expect( externalReportsLink.props().entry.name ).toBe( 'Reports' )
      expect( externalReportsLink.attributes()['data-element'] ).toBe( 'RouterLink' )
      expect( externalReportsLink.props().entry.scope ).toBe( 'safe-route' )

      expect( documentationDropdown.props().entry.name ).toBe( 'Documentation' )
      expect( documentationDropdown.attributes()['data-element'] ).toBe( 'NavbarDrawer' )
      expect( documentationDropdown.props().entry.scope ).toBe( 'safe-route' )

      expect( dashboardDocsLink.props().entry.name ).toBe( 'Dashboard Docs' )
      expect( dashboardDocsLink.attributes()['data-element'] ).toBe( 'RouterLink' )
      expect( dashboardDocsLink.props().entry.scope ).toBe( 'safe-route' )

      expect( ordersDocsLink.props().entry.name ).toBe( 'Orders Docs' )
      expect( ordersDocsLink.attributes()['data-element'] ).toBe( 'RouterLink' )
      expect( ordersDocsLink.props().entry.scope ).toBe( 'safe-route' )

      expect( ASNDocsLink.props().entry.name ).toBe( 'ASN Docs' )
      expect( ASNDocsLink.attributes()['data-element'] ).toBe( 'RouterLink' )
      expect( ASNDocsLink.props().entry.scope ).toBe( 'safe-route' )

      expect( inventoryDocsLink.props().entry.name ).toBe( 'Inventory Docs' )
      expect( inventoryDocsLink.attributes()['data-element'] ).toBe( 'RouterLink' )
      expect( inventoryDocsLink.props().entry.scope ).toBe( 'safe-route' )

      expect( myOWDDocsLink.props().entry.name ).toBe( 'My OWD Docs' )
      expect( myOWDDocsLink.attributes()['data-element'] ).toBe( 'RouterLink' )
      expect( myOWDDocsLink.props().entry.scope ).toBe( 'safe-route' )

      expect( bulkUploadDropdown.props().entry.name ).toBe( 'Bulk Upload Instructions' )
      expect( bulkUploadDropdown.attributes()['data-element'] ).toBe( 'NavbarDrawer' )
      expect( bulkUploadDropdown.props().entry.scope ).toBe( 'safe-route' )

      expect( bulkUploadOrdersLink.props().entry.name ).toBe( 'Bulk Upload - Orders' )
      expect( bulkUploadOrdersLink.attributes()['data-element'] ).toBe( 'RouterLink' )
      expect( bulkUploadOrdersLink.props().entry.scope ).toBe( 'safe-route' )

      expect( bulkUploadInventoryLink.props().entry.name ).toBe( 'Bulk Upload - Inventory' )
      expect( bulkUploadInventoryLink.attributes()['data-element'] ).toBe( 'RouterLink' )
      expect( bulkUploadInventoryLink.props().entry.scope ).toBe( 'safe-route' )

    })

  })

})
