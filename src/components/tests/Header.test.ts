import { mount } from '@vue/test-utils'
import { describe, expect, it } from 'vitest'

import Header from '@/components/Header.vue'

import { openAnnouncementsDrawer } from '@/store'

describe( 'header Component', () => {
  // Setup default wrapper with common options
  const createWrapper = ( props = {}) => {
    return mount( Header, {
      global: {
        stubs: {
          Icon:                true,
          Tab:                 true,
          Button:              true,
          Sidebar:             true,
          AnnouncementsDrawer: true
        }
      },
      props
    })
  }

  describe( 'menu Toggle', () => {
    it( 'should emit update:openMenu event when menu button is clicked', async () => {
      const wrapper = createWrapper({ openMenu: false })
      const menuButton = wrapper.findComponent({ name: 'Button' })

      await menuButton.trigger( 'click' )
      expect( wrapper.emitted()['update:openMenu'] ).toBeTruthy()
      expect( wrapper.emitted()['update:openMenu'][0] ).toEqual( [ true ] )
    })

    it( 'should apply correct classes based on menu state', () => {
      const wrapper = createWrapper({ openMenu: true })
      const menuLines = wrapper.findAll( '[data-element="menu-icon-line"]' )

      expect( menuLines[0].classes()).toContain( 'rotate-45' )
      expect( menuLines[1].classes()).toContain( '-rotate-45' )
    })
  })

  describe( 'user Name Generation', () => {
    it( 'should return "/" when name is empty', async () => {
      const wrapper = createWrapper()
      const result = await wrapper.vm.generateUserName( '' )
      expect( result ).toBe( '/' )
    })

    it( 'should return first name and last initial for full names', async () => {
      const wrapper = createWrapper()
      const result = await wrapper.vm.generateUserName( 'John Smith Junior' )
      expect( result ).toBe( 'John S.' )
    })

    it( 'should return full name when only one name provided', async () => {
      const wrapper = createWrapper()
      const result = await wrapper.vm.generateUserName( 'John' )
      expect( result ).toBe( 'John' )
    })
  })

  describe( 'announcements Drawer', () => {
    it( 'should toggle announcements drawer when announcement tab is clicked', async () => {
      const wrapper = createWrapper()
      const initialValue = openAnnouncementsDrawer.value

      const announcementTab = wrapper.findAllComponents({ name: 'Tab' })[0]
      await announcementTab.trigger( 'click' )

      expect( openAnnouncementsDrawer.value ).toBe( !initialValue )
    })
  })

})
