<script setup lang="ts">

import Icon from '@lib/components/blocks/Icon.vue'

import type { RouteLocationRaw } from 'vue-router'

defineProps<{
  to?:      RouteLocationRaw
  error?:   string
  label?:   string
  content?: string | number
  pending?: boolean
}>()

</script>

<template>

  <div class="bg-core-10">

    <div
      class="px-4"
      :class="{
        'grid grid-cols-[1fr_max-content] gap-x-2 bg-error/20': !!error,
      }"
    >

      <div class="pt-1 pb-2">

        <span class="text-[0.625rem] text-core uppercase">{{ label }}</span>

        <p v-if="pending" class="min-w-14 w-max animated-background text-transparent rounded-sm" v-html="content" />

        <div v-else>

          <RouterLink v-if="to" :to class="text-main-60 underline">
            <p class="text-sm" v-html="content" />
          </RouterLink>

          <p v-else class="text-sm" v-html="content" />

        </div>

      </div>

      <div
        v-if="error"
        v-tooltip.left="{ content: error }"
        class="w-4 h-full grid place-content-center"
      >

        <Icon name="issue-circle" size="m" class="text-error" />

      </div>

    </div>
  </div>

</template>
