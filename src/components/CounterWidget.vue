<script setup lang="ts">

import { ref, watch } from 'vue'
import { growNumberToTarget } from '@lib/scripts/utils'

import Icon from '@lib/components/blocks/Icon.vue'

import type { IconName } from '@lib/store/icon'

interface CounterWidgetProps {
  icon?:     IconName
  title?:    string
  count?:    number
  color?:    string
  action?:   any
  disabled?: boolean
}

const props         = defineProps<CounterWidgetProps>()
const animatedCount = ref<number | null>( null )

// Watch for changes in props.count and animate the count

watch(() => props.count, n => growNumberToTarget( n, value => animatedCount.value = value ), { immediate: true })

</script>

<template>

  <div
    class="w-full h-8 sm:h-10 rounded-xs border-[0.063rem] flex overflow-hidden cursor-pointer transition-shadow"
    :class="{
      'border-data1-120/50 hover:shadow-data1-120/20 shadow-[0_0_15px_rgba(0,0,0,0)]': props.color === 'data1-120',
      'border-success/50 hover:shadow-success/20 shadow-[0_0_15px_rgba(0,0,0,0)]': props.color === 'success',
      'border-main/50 hover:shadow-main/20 shadow-[0_0_15px_rgba(0,0,0,0)]': props.color === 'main',
      'border-error/50 hover:shadow-error/20 shadow-[0_0_15px_rgba(0,0,0,0)]': props.color === 'error',
      'border-warning/50 hover:shadow-warning/20 shadow-[0_0_15px_rgba(0,0,0,0)]': props.color === 'warning',
      'pointer-events-none opacity-60': disabled,
    }"
    @click="action"
  >

    <div
      class="min-w-8 sm:min-w-10 h-full flex items-center justify-center"
      :class="{
        'bg-data1-120/40': props.color === 'data1-120',
        'bg-success/40': props.color === 'success',
        'bg-main/40': props.color === 'main',
        'bg-error/40': props.color === 'error',
        'bg-warning/40': props.color === 'warning',
      }"
    >

      <Icon
        :name="props.icon" size="l" :class="{
          'text-data1-120': props.color === 'data1-120',
          'text-success': props.color === 'success',
          'text-main': props.color === 'main',
          'text-error': props.color === 'error',
          'text-warning': props.color === 'warning',
        }"
      />

    </div>

    <div
      class="w-full sm:min-w-[8.5rem] px-2 sm:px-4 flex justify-end sm:justify-between items-center"
      :class="{
        'bg-data1-120/10': props.color === 'data1-120',
        'bg-success/10': props.color === 'success',
        'bg-main/10': props.color === 'main',
        'bg-error/10': props.color === 'error',
        'bg-warning/10': props.color === 'warning',
      }"
    >

      <span class="hidden sm:inline-block text-sm truncate">{{ props.title }}</span>
      <span class="text-sm sm:text-lg font-medium">{{ animatedCount }}</span>

    </div>

  </div>

</template>
