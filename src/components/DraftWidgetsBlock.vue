<script setup lang="ts">

import CounterWidget from '@/components/CounterWidget.vue'

import type { DraftWidgets } from '@/types'
import type { DraftOrdersParams } from '@/modules/orders/types'
import type { DraftProductsParams } from '@/modules/inventory/types'

defineProps<{
  widgets:   DraftWidgets | null
  disabled?: boolean
}>()

defineEmits<{
  filter: [ payload: DraftOrdersParams | DraftProductsParams ]
}>()

</script>

<template>

  <div class="p-2 sm:p-4 sm:pb-0 md:px-[1.625rem] grid grid-cols-4 sm:grid-cols-2 xl:grid-cols-4 gap-2 xl:gap-4">

    <CounterWidget
      icon="draft-ready-to-import"
      title="Ready to Publish"
      color="success"
      :count="widgets?.totalDraftValid"
      :disabled="disabled"
      :action="() => $emit('filter', { hasErrors: false, importStatus: 'Draft' })"
    />

    <CounterWidget
      icon="draft-validation-errors"
      title="Validation Errors"
      color="warning"
      :count="widgets?.totalValidationErrors"
      :disabled="disabled"
      :action="() => $emit('filter', { hasErrors: true, importStatus: 'Draft' })"
    />

    <CounterWidget
      icon="draft-pending-import"
      title="Publishing Now"
      color="data1-120"
      :count="widgets?.totalPendingImport"
      :disabled="disabled"
      :action="() => $emit('filter', { importStatus: 'Pending' })"
    />

    <CounterWidget
      icon="draft-failed-import"
      title="Publish Failed"
      color="error"
      :count="widgets?.totalFailedImport"
      :disabled="disabled"
      :action="() => $emit('filter', { importStatus: 'Failed' })"
    />

  </div>

</template>
