<script setup lang="ts">

defineProps<{
  value?:      string | number
  attribute:   string
  hideBorder?: boolean
}>()

</script>

<template>

  <div
    class="py-1"
    :class="{ 'border-b-2 border-dotted': !hideBorder }"
  >

    <div class="flex text-sm">

      <div class="max-w-[6.375rem] md:max-w-[45%] w-full flex items-center">
        {{ attribute }}:
      </div>

      <span class="text-main">{{ value || '/' }}</span>

    </div>

  </div>

</template>
