<script setup lang="ts">

import { computed } from 'vue'
import { RouterLink, useRoute } from 'vue-router'

import Icon from '@lib/components/blocks/Icon.vue'
import NavbarDrawer from '@/components/NavbarDrawer.vue'

import type { GuardScope } from '@/types'

export interface NavbarSchemaItem extends DropListOption {
  name:       string
  path?:      string
  scope?:     GuardScope | GuardScope[]
  single?:    boolean
  entries?:   NavbarSchemaItem[]
  routeName?: string[] // List of route names to put the nav item into active mode
}

const props = defineProps<{
  entry?: NavbarSchemaItem
  level?: number
}>()

const route     = useRoute()
const isActive  = computed(() => props.entry.path === route.path )

defineExpose({
  isActive
})

</script>

<template>

  <Component
    :is="!entry?.entries ? RouterLink : NavbarDrawer"
    :key="entry?.id"
    :data-element="!entry?.entries ? 'RouterLink' : 'NavbarDrawer'"
    class="flex items-center w-full relative"
    :class="{
      'border-l border-core-100': level === 3,
      'py-4 after:absolute after:left-4 after:w-[calc(100%-2rem)] after:h-[1.125rem] after:border-b after:border-core-100 after:bottom-[0.188rem]': entry.single,
    }"
    :to="entry?.path || null"
    :href="entry?.path || null"
    :target="entry?.path?.startsWith('https') ? '_blank' : 'default'"
    size="m"
    :icon-name="entry?.icon?.name"
    :options="entry?.entries"
    :label="entry?.name"
    :left-align="true"
    :level="level"
  >

    <div
      class="flex flex-col w-full relative"
      :class="{
        'py-2 pl-6': level === 2 && entry.entries,
        'bg-core-100': isActive,
        'hover:bg-core-100': !entry?.entries,
      }"
    >

      <div
        v-if="!entry.entries"
        class="h-8 flex gap-3 items-center"
        :class="{
          'pl-4': entry?.path,
        }"
      >

        <div
          v-if="isActive"
          data-element="blue-line"
          class="w-1 h-full absolute top-0 bg-main"
          :class="{
            '-left-[0.063rem]': level === 3,
            'left-0': level === 2 || level === 1,
          }"
        />

        <Icon
          v-if="entry?.path && entry.icon"
          :name="entry.icon?.name"
          data-element="link-icon"
          size="m"
          class="text-core-10"
        />

        <p
          v-if="entry.path"
          data-element="entry-name"
          class="text-sm truncate font-medium"
          :class="{
            'text-core-50': !isActive,
            'text-core-10': isActive,
          }"
        >
          {{ entry.name }}
        </p>

      </div>

      <slot v-else />

    </div>

  </Component>

</template>
