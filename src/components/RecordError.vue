<script setup lang="ts">

import Icon from '@lib/components/blocks/Icon.vue'

</script>

<template>

  <div class="h-full px-5 py-3 grid grid-cols-[max-content_1fr] gap-x-2 bg-core-20 border-l-2 border-l-error border-r border-r-core-30 overflow-hidden overflow-y-auto">

    <div class="py-0.5">
      <Icon name="issue-circle" size="m" class="text-error" />
    </div>

    <div>

      <slot />

    </div>

  </div>

</template>
