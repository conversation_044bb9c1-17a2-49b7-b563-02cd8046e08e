import { createFetchy } from '@lib/scripts/fetchy'
import { loginRequest, msalInstance } from '@lib/auth/scripts/authConfig'
import { clientId, userMail, userName } from '@/modules/auth/store'

import type { AxiosRequestConfig } from 'axios'

async function generateHeaders(): Promise<AxiosRequestConfig['headers']> {

  const t = await msalInstance.acquireTokenSilent( loginRequest )

  const localHeaders: AxiosRequestConfig['headers'] = {
    'X-User-Id':   userMail.value,
    'X-UserName':  userName.value,
    'X-Client-Id': clientId.value
  }

  const appendLocalHeaders = import.meta.env.VITE_BASE_URL.match( /localhost/ ) ? localHeaders : {}

  return {
    ...appendLocalHeaders,
    Authorization: `${t.tokenType} ${t.accessToken}`
  }

}

export const fetchy = createFetchy( generateHeaders )
