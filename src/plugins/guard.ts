import { ref } from 'vue'
import { fetchy } from '@/plugins/fetchy'
import { createGuard } from '@lib/scripts/guard'
import { msalInstance } from '@lib/auth/scripts/authConfig'

import type { GuardScope, Scope } from '@/types'

// ---- PERMISSIONS

/**
 * The user's permissions.
 * This list is kept in state to avoid fetching the user's permissions every time the guard is called.
 * If this is empty, than the guard will fetch the claims from the instance again.
 */

const userPermissions = ref<number[]>( null )

/**
 * Checks if the user has the required permissions.
 * @param scope - The required permissions.
 * @param scopesList - List of all available scopes.
 * @param permissions - The user's permissions.
 * @returns True if the user has the required permissions, false otherwise.
 */

function hasPermission( scope: GuardScope | GuardScope[], scopesList: Scope[], permissions: number[] ): boolean {

  // If no scope is provided, block access.

  if ( !scope )
    return false

  // If the scope is 'ADMIN' or 'safe-route', allow access.
  // The safe-route scope is used for routes that don't require guard or scope.

  if ( scope === 'ADMIN' || scope === 'safe-route' )
    return true

  // If scope is an array and it's empty, block access.

  if ( Array.isArray( scope ) && scope.length === 0 )
    return false

  // If scope is an array, check if all of the scopes are allowed.

  if ( Array.isArray( scope ))
    return scope.every( s => permissions.includes( scopesList.find( sItem => sItem.value === s )?.key ))

  // If scope is a single scope, check if it's allowed.

  return permissions.includes( scopesList?.find( sItem => sItem.value === scope )?.key )

}

/**
 * Fetches the user's permissions from the server.
 * @param accessToken - The user's access token.
 * @returns Payload object with error and the scopes list as payload.
 */

export const scopesProvider: ScopesProvider<Scope> = async function ( accessToken: string ) {

  const { error, status, payload } = await fetchy<{ scopes: Scope[] }>({
    url:     'auth/scopes',
    baseURL: import.meta.env.VITE_SCOPES_BASE_URL,
    headers: { Authorization: `Bearer ${accessToken}` }
  })

  return {
    error,
    status,
    payload: payload?.scopes
  }

}

/**
 * Checks if the user has the required permissions to access the route.
 * @param scope - The required permissions.
 * @param scopesList - List of all available scopes.
 * @param instance - The MSAL instance.
 * @returns True if the user has the required permissions, false otherwise.
 */

export const accessGuard: AccessGuard<Scope, GuardScope> = function ( scope, scopesList, instance = msalInstance ): boolean {

  // Extract the permissions from the
  // access token if they are not saved in state yet.

  if ( !userPermissions.value ) {

    const account = instance?.getActiveAccount()?.idTokenClaims
    const permissionKeys = account?.scc as string[] ?? []

    userPermissions.value = permissionKeys.map( k => Number.parseInt( String( k )))

  }

  return hasPermission( scope, scopesList, userPermissions.value )

}

/**
 * Create the Guard component.
 */

const newGuard = createGuard( accessGuard )

export const Guard = newGuard.component
export const guard = newGuard.function
