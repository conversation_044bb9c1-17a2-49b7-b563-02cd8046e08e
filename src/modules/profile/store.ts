import { fetchy } from '@/plugins/fetchy'

import type { DocumentModel, RequestDetails } from '@/modules/profile/types'

interface GetDocumentsPayload extends RequestDetails {
  documents: DocumentModel[]
}

export async function getDocuments<ParamsType>( params: ParamsType ) {

  const documentsData = fetchy<GetDocumentsPayload>({
    url: 'client-documents',
    params
  })

  return documentsData

}

export async function downloadDocument( documentId: string ) {

  const documentData = await fetchy<ArrayBuffer>({
    url:          `client-documents/${documentId}/download`,
    method:       'GET',
    responseType: 'arraybuffer'
  })

  return documentData

}

/**
 * Saves a document file.
 *
 * @param stream - The file data as an ArrayBuffer.
 * @param fileName - The name of the file.
 */
export function saveDocumentFile( stream: ArrayBuffer, fileName: string ) {

  // Create a new File object with the given data and name

  const file = new File( [ stream ], fileName, { type: 'application/pdf' })

  // Create a new anchor element to simulate a download link

  const anchorElement = document.createElement( 'a' )

  // Create a URL for the file data

  const fileUrl = window.URL.createObjectURL( file )

  // Set the href and download attributes of the anchor element

  anchorElement.href = fileUrl
  anchorElement.target = '_blank'
  anchorElement.download = fileName

  // Simulate a click on the anchor element to trigger the download

  anchorElement.click()

  // Revoke the URL object to free up memory

  window.URL.revokeObjectURL( fileUrl )

}
