export interface RequestDetails {
  currentPage: number
  pageSize:    number
  nextPage:    number
  totalRows:   number
  totalPages:  number
  isLastPage:  boolean
}

export interface DocumentRouteParams extends BaseParams {
  sortBy?:          string
  clientId?:        number
  documentType?:    'invoice' | 'contract'
  documentTitle?:   string
  sortDirection?:   SortDirection
  periodEndDate?:   string
  documentNumber?:  string
  periodStartDate?: string
}

export interface DocumentModel {
  id:                 string
  clientId:           number
  documentTitle:      string
  description:        string
  documentNumber:     string
  documentFileName:   string
  documentType:       LooseAutoComplete<'invoice' | 'contract'>
  documentUrl:        string
  periodStartDate:    string
  periodEndDate:      string
  createdByUserId:    string
  createdByUserName:  string
  createdDateTime:    string
  modifiedByUserId:   string
  modifiedByUserName: string
  modifiedDateTime:   string
  parentDocumentId:   string
  documentMeta:       DocumentModelMeta[]
  versions:           DocumentModel[]
}

export interface DocumentModelMeta {
  key:   string
  value: string
}
