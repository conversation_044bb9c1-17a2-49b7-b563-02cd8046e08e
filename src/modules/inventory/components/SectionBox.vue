<script setup lang="ts">

import Icon from '@lib/components/blocks/Icon.vue'

import type { IconName } from '@lib/store/icon'

withDefaults( defineProps<{
  name:     string
  mode?:    'default' | 'error'
  iconName: IconName
}>(), {
  mode: 'default'
})

</script>

<template>

  <div class="w-full bg-core-10 md:shadow-custom">

    <div class="w-full h-10 sticky top-0 z-1 pl-3 flex items-center space-x-3 bg-core-20 border-core-30 border-b">

      <Icon
        :name="iconName"
        size="m"
        :class="{
          'text-main': mode === 'default',
          'text-error': mode === 'error',
        }"
      />

      <p class="text-sm font-medium grow" v-html="name" />

      <slot name="head-slot" />

    </div>

    <slot />

  </div>

</template>
