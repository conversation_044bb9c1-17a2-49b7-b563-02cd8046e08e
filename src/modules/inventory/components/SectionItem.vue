<script setup lang="ts">

defineProps<{
  mini?:    boolean
  label:    string
  textbox?: boolean
}>()

</script>

<template>

  <div
    class="w-full px-3 grid border-b border-core-30 overflow-hidden"
    :class="{
      'h-10 grid-cols-[max-content_1fr] items-center gap-x-3': !textbox && !mini,
      'h-8 grid-cols-[max-content_1fr] items-center gap-x-3 space-x-3 border-b-0': mini,
      'h-auto py-3': textbox,
    }"
  >

    <p class="text-sm text-core grow whitespace-nowrap">
      {{ label }}
    </p>

    <div :class="{ truncate: !textbox }">
      <slot />
    </div>

  </div>

</template>
