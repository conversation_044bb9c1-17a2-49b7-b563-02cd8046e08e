import { pageSize } from '@lib/store/table'

import type { InventoryParams } from '@/modules/inventory/types'

export const inventoryParams: InventoryParams = {
  page:     1,
  pageSize: pageSize.value
}

export const inventoryRoutes: RouteModel<InventoryParams> = {
  name: 'Inventory',
  path: '/inventory',
  meta: {
    scope: 'Inventory.Read',
  },
  redirect:  '/inventory/live',
  component: () => import( '@/modules/inventory/views/Inventory.vue' ),
  children:  [
    {
      name: 'Live Products',
      path: '/inventory/live',
      meta: {
        title: 'Live Products - OWD',
        scope: 'Inventory.Read'
      },
      component: () => import( '@/modules/inventory/views/LiveProducts.vue' ),
      children:  [
        {
          name: 'Live Product Details',
          path: '/inventory/live/details/:productId',
          meta: {
            title: 'Live Product Details - OWD',
            scope: 'Inventory.Read'
          },
          component: () => import( '@/modules/inventory/views/LiveProductDetails.vue' )
        }
      ]
    },
    {
      name: 'Draft Products',
      path: '/inventory/draft',
      meta: {
        title: 'Draft Products - OWD',
        scope: 'Inventory.Read'
      },
      component: () => import( '@/modules/inventory/views/DraftProducts.vue' ),
      children:  [
        {
          name: 'Draft Product Details',
          path: '/inventory/draft/details/:productId',
          meta: {
            title: 'Draft Product Details - OWD',
            scope: 'Inventory.Read'
          },
          component: () => import( '@/modules/inventory/views/DraftProductDetails.vue' ),
          children:  [
            {
              name: 'Draft Product Details Inventory',
              path: '/inventory/draft/details/:productId/inventory',
              meta: {
                scope: [ 'Inventory.Read', 'Inventory.Write' ]
              },
              component: () => import( '@/components/InventoryPanel.vue' )
            }
          ]
        },
        {
          name: 'Create Product',
          path: '/inventory/draft/create-product',
          meta: {
            title: 'Create Product - OWD',
            scope: [ 'Inventory.Read', 'Inventory.Write' ]
          },
          component: () => import( '@/modules/inventory/views/create-product/views/CreateProduct.vue' )
        },
        {
          name: 'Upload Bulk Products',
          path: '/inventory/draft/bulk-upload',
          meta: {
            title: 'Upload Bulk Products - OWD',
            scope: [ 'Inventory.Read', 'Inventory.Write' ]
          },
          component: () => import( '@/components/BulkUploadDrawer.vue' )
        }
      ]
    }
  ]
}
