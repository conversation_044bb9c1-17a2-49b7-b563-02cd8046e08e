export enum CreateProductFlow {
  productInfo = 1,
  otherDetails = 2,
}

export enum CreateProductMicroFlow {
  productInfo = 1,
  kitComponents = 2,
  otherDetails = 3,
}

export interface CreateProductDetails {
  sku:                  string // required
  upc:                  string
  isbn:                 string
  type:                 'KIT' | 'PHYSICAL' // required
  size:                 string
  title:                string // required
  notes:                string
  color:                string
  webUrl:               string
  supplier:             string
  isActive:             boolean
  keywords:             string
  imageUrl:             string
  groupCode:            string
  description:          string
  retailPrice:          number
  supplierSku:          string
  supplierCost:         number
  customsValue:         number
  caseQuantity:         number
  imageThumbUrl:        string
  customsDescription:   string
  masterCaseQuantity:   number
  packingInstructions:  string
  reorderAlertQuantity: number // required
}
