import { computed, ref } from 'vue'
import { validateModel } from '@lib/scripts/inputValidation'
import { createKitComponent, createProduct } from '@/modules/inventory/store'
import { CreateProductFlow, CreateProductMicroFlow } from '@/modules/inventory/views/create-product/types'
import { checkValue, convertObjectToPlain, convertObjectToValidatable } from '@lib/scripts/utils'

import type { CreateProductDetails } from '@/modules/inventory/views/create-product/types'
import type { InventoryPanelProduct } from '@/modules/inventory/types'

export const isKitProduct         = ref<boolean>( false )
export const selectedKitProducts  = ref<InventoryPanelProduct[]>( [] )
export const createProductPending = ref<boolean>( false )

/*
 * Controls the flow of the create product wizard.
 */

export const storedStep = ref<CreateProductMicroFlow>( CreateProductMicroFlow.productInfo )
export const activeStep = ref<CreateProductMicroFlow>( CreateProductMicroFlow.productInfo )

export const activeFlow = computed(() =>
  activeStep.value === CreateProductMicroFlow.productInfo && CreateProductMicroFlow.kitComponents
    ? CreateProductFlow.productInfo
    : CreateProductFlow.otherDetails
)

/*
 * Create Product Details.
 */

const createProductDetailsDefaultModel: CreateProductDetails = {
  sku:                  null,
  upc:                  null,
  isbn:                 null,
  size:                 null,
  type:                 'PHYSICAL',
  title:                null,
  notes:                null,
  color:                null,
  webUrl:               null,
  isActive:             true,
  keywords:             null,
  supplier:             null,
  imageUrl:             null,
  groupCode:            null,
  description:          null,
  retailPrice:          null,
  supplierSku:          null,
  supplierCost:         null,
  customsValue:         null,
  caseQuantity:         null,
  imageThumbUrl:        null,
  customsDescription:   null,
  masterCaseQuantity:   null,
  packingInstructions:  null,
  reorderAlertQuantity: null,
}

const omitProductDetailsKeys: ( keyof CreateProductDetails )[] = [ 'isActive', 'description', 'retailPrice', 'isbn', 'upc', 'groupCode', 'keywords', 'notes', 'supplier', 'supplierSku', 'supplierCost', 'color', 'size', 'customsValue', 'customsDescription', 'masterCaseQuantity', 'caseQuantity', 'type', 'packingInstructions', 'webUrl', 'imageUrl', 'imageThumbUrl' ]

// Validatable objects for product details.

export const productDetails    = ref<Validatable<CreateProductDetails>>( convertObjectToValidatable( createProductDetailsDefaultModel, null, omitProductDetailsKeys ))
export const enableAddKitItems = computed(() => isKitProduct.value )

// Booleans to determine the validity of the steps.

export const kitComponentsValid       = computed(() => isKitProduct.value ? ( selectedKitProducts.value.length > 0 && kitComponentHasQuantity.value ) : true )
export const productInfoStepValid     = computed(() => validateModel( productDetails.value ))
export const kitComponentHasQuantity  = computed(() => selectedKitProducts.value.every( item => item.quantity ))

export async function createNewProduct( callback: ( productId: number ) => Promise<any> | void ) {

  // Determine the product type based on the isKitProduct flag.

  const productType = isKitProduct.value ? 'KIT' : 'PHYSICAL'

  productDetails.value.type.value = productType

  const data: CreateProductDetails = {
    ...convertObjectToPlain( productDetails.value )
  }

  Object.keys( data ).forEach(( key ) => {

    if ( typeof data[key] != 'boolean' )
      data[key] = !checkValue( data[key] ) ? null : data[key]

  })

  createProductPending.value = true

  const { error, payload } = await createProduct( data )

  if ( !error ) {

    if ( isKitProduct.value ) {

      // Create a list of requests from the selected kit components.

      const requests = selectedKitProducts.value.map( product => createKitComponent( payload.id, {
        sku:      product.sku,
        quantity: product.quantity
      }))

      // Wait for all requests to complete in a non blocking way.

      const responses = await Promise.all( requests )

      if ( !responses.some( response => response.error )) {

        await callback( payload.id )
        resetCreateProductState()

      }

    }

    else {

      await callback( payload.id )
      resetCreateProductState()

    }

  }

  createProductPending.value = false

}

export function resetCreateProductState() {

  // Reset the step value to 'productInfo'

  activeStep.value = CreateProductMicroFlow.productInfo

  // Reset product details values using default models

  productDetails.value = convertObjectToValidatable( createProductDetailsDefaultModel, null, omitProductDetailsKeys )

  // Reset the has Facility flag

  isKitProduct.value = false
  selectedKitProducts.value = []

}
