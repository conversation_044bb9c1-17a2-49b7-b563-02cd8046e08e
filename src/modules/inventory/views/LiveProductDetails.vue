<script setup lang="ts">

import { Guard } from '@/plugins/guard'
import { computed, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { saveFile, viewSetup } from '@lib/scripts/utils'
import { setNotificationOptions } from '@lib/store/snackbar'
import { createSections, exportLiveProduct, getProduct, mapDetailsToSections, sanitizeKey, updateLiveProduct } from '@/modules/inventory/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Toggle from '@lib/components/inputs/Toggle.vue'
import Button from '@lib/components/button/Button.vue'
import Loader from '@lib/components/blocks/Loader.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import SectionBox from '@/modules/inventory/components/SectionBox.vue'
import SectionItem from '@/modules/inventory/components/SectionItem.vue'
import KitProductItem from '@/modules/inventory/components/KitProductItem.vue'
import BasicSectionBox from '@/modules/inventory/components/BasicSectionBox.vue'
import EditSectionForm from '@/modules/inventory/components/EditSectionForm.vue'
import InventorySectionBox from '@/modules/inventory/components/InventorySectionBox.vue'

import type { LiveProduct, ProductDetailsSection, SectionKeys } from '@/modules/inventory/types'

const route           = useRoute()
const router          = useRouter()
const pending         = ref<boolean>( false )
const product         = ref<LiveProduct>( null )
const sections        = ref<ProductDetailsSection[]>()
const productId       = computed(() => Number.parseInt( String( route.params.productId )))
const openActions     = ref<boolean>( false )
const exportPending   = ref<boolean>( false )
const updatePending   = ref<boolean>( false )
const hasKitProducts  = computed(() => product.value?.kitComponents?.length > 0 )
const editSectionForm = ref<SectionKeys>( null )

function openEditForm( key: SectionKeys ) {
  editSectionForm.value = key
  sections.value = mapDetailsToSections( sections.value, product.value )
}

function closeEditForm() {

  editSectionForm.value = null

  sections.value.forEach(( section ) => {
    section?.model.forEach(( item ) => {
      item.declaredValue = null
    })
  })

}

// --- PRODUCT SERVICES

async function exportProduct( productId: number ) {

  exportPending.value = true

  const { error, payload } = await exportLiveProduct( productId )

  if ( !error )
    saveFile( payload, `Product_${productId}.xlsx` )

  exportPending.value = false

}

async function getLiveProduct( pId: number ) {

  pending.value = true

  const { error, status, payload } = await getProduct( pId )

  if ( !error ) {
    product.value = payload
    sections.value = createSections( 'live', payload?.defaultFacilityCode )
    sections.value = mapDetailsToSections( sections.value, payload )
  }

  pending.value = false

  return {
    error,
    status,
    payload
  }

}

async function updateProduct() {

  updatePending.value = true

  const editedSection = sections.value.find( s => s.key === editSectionForm.value )
  const data: LiveProduct = { ...product.value }

  editedSection.model.forEach(( item ) => {

    if ( item.key in product.value )
      data[item.key] = sanitizeKey( item.declaredValue, item.type )

  })

  if ( data?.kitComponents?.length > 0 )
    data.type = 'KIT'

  delete ( data.kitComponents )

  const { error, payload } = await updateLiveProduct( data, product.value.id )

  if ( !error ) {
    product.value = payload
    sections.value = createSections( 'live', product.value.defaultFacilityCode )
    sections.value = mapDetailsToSections( sections.value, payload )
    closeEditForm()
    setNotificationOptions({ message: 'Product details are updated successfully.' })
  }

  updatePending.value = false

}

// --- GET PRODUCT DATA

viewSetup(
  null,
  null,
  router,
  () => getLiveProduct( productId.value ),
  productId
)

</script>

<template>

  <div class="w-full h-full grid grid-rows-[max-content_1fr] bg-core-30 overflow-hidden">

    <!-- Header -->

    <div class="w-full h-[5.5rem] md:h-12 grid grid-rows-[max-content_max-content] md:grid-rows-1 grid-cols-[1fr_max-content] md:grid-cols-[max-content_1fr_max-content] bg-core-120">

      <!-- Order Id -->

      <div class="h-12 px-4 flex items-center border-b md:border-r border-core-100">

        <p v-if="pending" class="text-core-10">
          Loading Product Details ...
        </p>

        <p v-else class="text-core-10 font-medium">
          Live Product: <span class="text-main-50">[{{ productId }}]</span>
        </p>

      </div>

      <!-- Other Header Options -->

      <div class="h-10 md:h-12 col-span-2 md:col-span-1 row-start-2 md:row-start-auto flex justify-end">

        <div
          v-if="!pending"
          class="truncate text-core-10 font-medium h-full px-4 flex items-center space-x-2 md:border-r border-core-100"
        >
          <p class="truncate">
            <span>Status: </span>
            <span
              class="truncate"
              :class="{
                'text-success': product?.isActive,
                'text-warning': !product?.isActive,
              }"
            >{{ product?.isActive ? 'Active' : 'Not Active' }}</span>
          </p>

        </div>

        <div class="grow" />

        <div class="h-full hidden md:block md:border-l border-core-100">

          <Button
            mode="ghost"
            class="gap-x-3 text-text-inverse"
            :pending="exportPending"
            @click="() => exportProduct(product.id)"
          >
            <Icon name="upload" size="m" />
            <p>Export Excel</p>
          </Button>

        </div>

        <div class="w-10 h-full md:hidden border-core-100">

          <Button
            size="m"
            type="box"
            mode="ghost"
            icon="dots-vertical"
            class="text-text-inverse"
            @click="openActions = true"
          />

        </div>

      </div>

      <!-- Close Button -->

      <div class="h-12 border-b md:border-l border-core-100">

        <Button
          type="box"
          mode="ghost"
          icon="close"
          class="text-text-inverse"
          @click="$router.push({ name: 'Live Products' })"
        />

      </div>

    </div>

    <div v-if="pending" class="w-full h-full flex items-center justify-center space-x-2">
      <Loader name="Product Data" />
    </div>

    <!-- Content -->

    <div v-else-if="!!product" class="w-full h-full grid md:grid-cols-[max-content_1fr] md:grid-rows-1 content-start overflow-y-auto md:overflow-hidden">

      <!-- General Info Sidebar -->

      <div class="md:w-[20rem] h-full md:row-span-2 md:overflow-y-auto md:shadow-custom">

        <BasicSectionBox
          :can-edit="sections?.find(s => s.name === 'General Information').editable"
          :section="sections?.find(s => s.name === 'General Information')"
          class="w-full md:min-h-full md:w-[20rem] md:max-w-[20rem] md:min-w-[20rem]"
          @open-section-form="openEditForm"
        />

      </div>

      <!-- Product Details -->

      <div class="md:overflow-y-auto">

        <div class="w-full md:p-8 grid xl:grid-cols-2 md:gap-8">

          <div class="w-full grid md:gap-8 content-start">

            <!-- Kit Components Section -->

            <SectionBox
              v-if="hasKitProducts"
              name="Kit Components"
              class="w-full"
              icon-name="products-group"
            >

              <template #head-slot>

                <Guard scope="Inventory.Write">

                  <div class="h-full border-l border-core-30">

                    <Button
                      size="m"
                      mode="ghost"
                      class="gap-x-2"
                      :disabled="true"
                    >

                      <p class="text-sm">
                        Edit
                      </p>
                      <Icon name="edit" size="s" />

                    </Button>

                  </div>

                </Guard>

              </template>

              <KitProductItem
                v-for="kit in product.kitComponents"
                :key="kit.id"
                :kit-product="kit"
                inventory-type="live"
                :disabled="true"
              />

            </SectionBox>

            <!-- Attributes | Other Details Sections | External Links Section -->

            <BasicSectionBox
              v-for="section in sections?.filter(s => !s.hidden && s.viewSection === 'left')"
              :key="section?.name"
              :section="section"
              :can-edit="section.editable"
              @open-section-form="openEditForm"
            />

          </div>

          <div class="grid md:gap-8 content-start">

            <!-- Inventory Section -->

            <InventorySectionBox
              v-if="product?.availableStockLevels"
              :available-stock-levels="product.availableStockLevels"
              :reorder-alert-quantity="product.reorderAlertQuantity"
            />

            <!-- Stock | Supplier | Customs Sections -->

            <BasicSectionBox
              v-for="section in sections?.filter(s => !s.hidden && s.viewSection === 'right')"
              :key="section?.name"
              :section="section"
              :can-edit="section.editable"
              @open-section-form="openEditForm"
            />

            <!-- Bulk Section -->

            <SectionBox name="Bulk Options" icon-name="bulk" class="w-full">

              <SectionItem label="Bulk Pick">

                <div class="w-full grid justify-end">
                  <Toggle v-model="product.isBulkyPick" :readonly="true" />
                </div>

              </SectionItem>

              <div v-if="product.isBulkyPick" class="border-b border-core-30">

                <SectionItem :mini="true" label="Ship System weight:">

                  <p class="text-sm">

                    <Icon
                      name="checkmark"
                      size="m"
                      :class="{
                        'text-main': product?.shipSystemWeight,
                        'text-core': !product?.shipSystemWeight,
                      }"
                    />

                  </p>

                </SectionItem>

                <SectionItem :mini="true" label="Is Insert:">

                  <p class="text-sm">

                    <Icon
                      name="checkmark"
                      size="m"
                      :class="{
                        'text-main': product?.isInsert,
                        'text-core': !product?.isInsert,
                      }"
                    />

                  </p>

                </SectionItem>

              </div>

              <SectionItem label="Bulk Pack">

                <div class="w-full grid justify-end">
                  <Toggle v-model="product.isBulkyPack" :readonly="true" />
                </div>

              </SectionItem>

            </SectionBox>

          </div>

        </div>

      </div>

    </div>

    <!-- Sidebar :: Edit Section Form -->

    <Sidebar
      :dim="true"
      :open="!!editSectionForm"
      :strict="true"
      @close="closeEditForm"
    >

      <EditSectionForm
        :section="sections?.find(s => s.key === editSectionForm)"
        :save-pending="updatePending"
        @close="closeEditForm"
        @save="updateProduct"
      />

    </Sidebar>

    <!-- Sidebar :: Product Mobile Actions -->

    <Sidebar
      :dim="true"
      :open="openActions"
      :strict="true"
      position="bottom"
      custom-class="md:hidden"
      @close="openActions = false"
    >

      <div class="w-full bg-core-10">

        <div class="w-full h-12 pl-4 sticky top-0 z-1 flex items-center bg-core-20 border-b border-core-30">

          <p class="text-sm font-medium grow">
            Available Options
          </p>

          <Button
            type="box"
            mode="ghost"
            icon="close"
            @click="openActions = false"
          />

        </div>

        <div class="p-4 grid gap-2">

          <Button
            type="pill"
            mode="secondary"
            class="justify-between"
            :pending="exportPending"
            @click="exportProduct(productId)"
          >

            <p>Export Excel</p>
            <Icon name="upload" size="m" />

          </Button>

        </div>

      </div>

    </Sidebar>

  </div>

</template>
