import { fetchy } from '@/plugins/fetchy'
import { generateRandomNumber } from '@lib/scripts/utils'

import type { FacilityOperationalStatus, GlobeFacilityStatus } from '@/modules/dashboard/types'

export const statusList: GlobeFacilityStatus[] = [
  { id: 1, name: 'Fully Operational' },
  { id: 2, name: 'Partially Operational' },
  { id: 3, name: 'Not Operational' }
]

export function pickRandomImageSources( imageCount: number = 3 ): string[] {

  const numbersList: number[] = []

  while ( numbersList.length < imageCount ) {

    const number = generateRandomNumber( 1, 10 )

    if ( !numbersList.includes( number ))
      numbersList.push( number )

  }

  return numbersList.map( number => `/images/operational-status/OperationalStatus${number}.jpg` )

}

export async function updateFacility( facilityReference: string, data: FacilityOperationalStatus ) {
  return await fetchy<{ facilities: FacilityOperationalStatus[], defaultFacility: string }>(
    {
      url:    `facilities/operational-status/${facilityReference}`,
      data,
      method: 'PUT'
    }
  )
}
