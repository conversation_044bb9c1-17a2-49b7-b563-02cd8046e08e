import type { Vector3 } from 'three'
import type { Facility } from '@/types'

export interface DashboardClient {
  asn:          Asn
  receives:     Receives
  inventory:    Inventory
  promisesMet:  PromisesMet
  todaysOrders: TodaysOrders
}

export interface PromisesMet {
  onTimePercent:              number
  fulfillmentAccuracyPercent: number
  fulfillmentSavings:         number
}

export interface TodaysOrders {
  void:                     number
  onHold:                   number
  shipped:                  number
  unknown:                  number
  atWarehouse:              number
  backorderActive:          number
  backorderOnHold:          number
  fulfillmentStatusPercent: number
}

export interface Asn {
  pendingCount:        number
  receivedCount:       number
  cancelledCount:      number
  partialReceiptCount: number
}

export interface Receives {
  total:         number
  nonconforming: number
}

export interface Inventory {
  lowStockProductCount:   number
  damagedProductCount:    number
  outOfStockProductCount: number
}

export enum OperationalStatus {
  active = 1,
  inactive = 2,
  restricted = 3
}

export interface FacilityOperationalStatus extends Facility {
  operationalPercentage: number
  description:           string
  operationalTitle:      string
}

export interface GlobeFacility extends Facility {
  status:   GlobeFacilityStatus['id']
  locked?:  boolean
  position: Vector3
}

export interface GlobeFacilityStatus {
  id:   1 | 2 | 3
  name: `${'Fully' | 'Partially' | 'Not'} Operational`
}
