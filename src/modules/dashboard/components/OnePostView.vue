<script setup lang="ts">
import { ref } from 'vue'
import { announcementsList, openAnnouncement, openAnnouncementsDrawer } from '@/store'

import Icon from '@lib/components/blocks/Icon.vue'

const hover             = ref( false )
const firstAnnouncement = ref ( announcementsList.value[0] )

</script>

<template>

  <div class="absolute top-0 w-full px-8 pt-7 hidden lg:block">

    <!-- News title and read all -->

    <div class="flex justify-between items-center text-core-10 pb-[1.125rem]">
      <span class="text-lg">News and Announcements</span>
      <span v-if="firstAnnouncement" class="text-sm cursor-pointer animate-hover-underline" @click="openAnnouncementsDrawer = true">Read All</span>
    </div>

    <!-- Post section -->

    <div
      v-if="firstAnnouncement"
      class="h-24  flex cursor-pointer"
      :class="{
        'bg-core-20': !hover,
        'shadow-xl bg-core-10': hover,
      }"
      @mouseover="hover = true" @mouseleave="hover = false"
      @click="openAnnouncement(firstAnnouncement)"
    >

      <img class="w-[40%] object-cover" :src="firstAnnouncement?.yoast_head_json?.og_image?.[0].url">

      <div class="px-3 py-2 w-full flex flex-col justify-between">

        <div class="flex justify-between gap-[1.375rem]">

          <h3 class="line-clamp-2" v-html="firstAnnouncement?.title?.rendered" />
          <Icon
            name="go" size="m"
            :class="{
              'text-main': hover,
              'text-core-40': !hover,
            }"
          />

        </div>

        <span class="text-owd w-fit animate-hover-underline before:bg-owd">Read More</span>

      </div>

    </div>

    <div v-else>

      <p class="text-sm text-core-50">
        No news or announcements available at this time.
      </p>

    </div>

    <div class="border-t border-core-30 mt-6 pt-6">
      <h2 class="text-core-10 text-lg">
        Fulfillment Centers
      </h2>
    </div>

  </div>

</template>
