<script setup lang="ts">

import { checkValue } from '@lib/scripts/utils'
import { pickRandomImageSources } from '@/modules/dashboard/store'
import { defaultFacility, facilitiesLookup } from '@/store.ts'
import { onBeforeUnmount, reactive, ref, toRaw, watch } from 'vue'

import {
  AmbientLight,
  Color,
  DirectionalLight,
  Fog,
  Group,
  MathUtils,
  Mesh,
  MeshBasicMaterial,
  PerspectiveCamera,
  PointLight,
  Raycaster,
  Scene,
  SphereGeometry,
  Vector2,
  Vector3,
  WebGLRenderer
} from 'three'

import Sidebar from '@lib/components/blocks/Sidebar.vue'
import countries from '@/assets/db/globe.json'
import ThreeGlobe from 'three-globe'
import GlobeFacilityCard from '@/modules/dashboard/components/GlobeFacilityCard.vue'
import OperationalStatusInfoDrawer from '@/modules/dashboard/components/OperationalStatusInfoDrawer.vue'

import type { Camera } from 'three'
import type { GlobeFacility } from '@/modules/dashboard/types'

interface GlobeProps {
  scene:                   Scene
  globe:                   ThreeGlobe
  mouse:                   Vector2
  mobile:                  boolean
  camera:                  PerspectiveCamera
  renderer:                WebGLRenderer
  raycaster:               Raycaster
  locations:               Record<'DC7' | 'DC1' | 'DC6', Mesh>
  viewHalfX:               number
  viewHalfY:               number
  globeMaster:             Group
  facilities:              GlobeFacility[]
  locationsGroup:          Scene
  globeMouseFollow:        Group
  animationFrameReference: number
}

/**
 * Global Globe properties
 */

const globeContainer                = ref<HTMLElement>( null )
const activeOperationalStatus       = ref<GlobeFacility>()
const globeFacilityCardsImages      = pickRandomImageSources( 3 )
const openOperationalStatusDrawer   = ref<boolean>( false )
const activeOperationalStatusIndex  = ref<number>( null )

const g = reactive<GlobeProps>({
  globe:            null,
  scene:            null,
  mouse:            null,
  camera:           null,
  mobile:           true,
  renderer:         null,
  viewHalfX:        0,
  viewHalfY:        0,
  raycaster:        null,
  globeMaster:      null,
  locationsGroup:   null,
  globeMouseFollow: null,
  locations:        {
    DC1: null,
    DC6: null,
    DC7: null,
  },
  facilities: [
    {
      code:                  'DC1',
      status:                null,
      display:               null,
      position:              new Vector3(),
      isDefault:             false,
      description:           null,
      operationalTitle:      null,
      operationalPercentage: 0,
    },
    {
      code:                  'DC6',
      status:                null,
      display:               null,
      position:              new Vector3(),
      isDefault:             false,
      description:           null,
      operationalTitle:      null,
      operationalPercentage: 0,
    },
    {
      code:                  'DC7',
      status:                null,
      display:               null,
      position:              new Vector3(),
      isDefault:             false,
      description:           null,
      operationalTitle:      null,
      operationalPercentage: 0,
    }
  ],
  animationFrameReference: null
})

/**
 * Map the facilities data to the globe facilities
 */

watch( facilitiesLookup, ( n ) => {

  if ( !n )
    return

  n.forEach(( facility ) => {

    g.facilities.forEach(( globeFacility ) => {

      if ( globeFacility.code === facility.code ) {

        globeFacility.locked = defaultFacility.value !== 'ALL' ? defaultFacility.value !== facility.code : false
        globeFacility.display = facility.display
        globeFacility.isDefault = facility.isDefault
        globeFacility.description = facility.description
        globeFacility.operationalTitle = facility.operationalTitle
        globeFacility.operationalPercentage = facility.operationalPercentage

        if ( facility.operationalPercentage === 0 )
          globeFacility.status = 3

        else if ( facility.operationalPercentage < 100 )
          globeFacility.status = 2

        else
          globeFacility.status = 1

      }

    })

  })

}, { immediate: true })

/**
 * Rotate the card index
 *
 * @param delay ---------- Delay between the card rotation
 * @param timeout -------- Timeout reference
 * @param duration ------- Duration of the card
 * @param autoIndex ------ Auto index of the card
 * @param prevIndex ------ Previous index of the card
 * @param activeIndex ---- Active index of the card - This is set when the user hovers over the card and takes precedence over the auto index
 *
 * Show the facilities cards in a rotating manner, except if the user hovers
 * over a card, then the card should stay active until the user leaves the card.
 */

const delay       = ref<number>( 1500 )
const timeout     = ref<NodeJS.Timeout>( null )
const duration    = ref<number>( 4000 )
const autoIndex   = ref<number>( null )
const prevIndex   = ref<number>( 0 )
const activeIndex = ref<number>( null )

function rotateCardIndex() {

  timeout.value = setTimeout(() => {

    autoIndex.value = prevIndex.value

    clearTimeout( timeout.value )

    timeout.value = setTimeout(() => {

      autoIndex.value = null
      prevIndex.value = prevIndex.value === ( g.facilities.length - 1 ) ? 0 : prevIndex.value + 1

      clearTimeout( timeout.value )
      rotateCardIndex()

    }, duration.value )

  }, delay.value )

}

watch( activeIndex, ( n ) => {

  if ( checkValue( n )) {

    clearTimeout( timeout.value )
    autoIndex.value = n
    prevIndex.value = n === ( g.facilities.length - 1 ) ? 0 : n + 1

  }

  else {
    autoIndex.value = null
    rotateCardIndex()
  }

})

/**
 * Project a 3D object position to 2D
 * @param position3d ---- 3D position
 * @param camera -------- Camera
 * @param containerW ---- Container width
 * @param containerH ---- Container height
 * @returns - 3D Vector with x and y mapped to 2D Position
 */

function projectObjectPositionIn2D( position3d: Vector3, camera: Camera, containerW: number, containerH: number ): Vector3 {

  const position = new Vector3( position3d.x, position3d.y, position3d.z )
  const vector2d = position.project( camera )

  vector2d.x = ( vector2d.x + 1 ) / 2 * containerW
  vector2d.y = -( vector2d.y - 1 ) / 2 * containerH
  vector2d.z = 0

  return vector2d

}

/**
 * Get 2D locations of the facilities
 * @param locations - Locations of the facilities
 */

function get2dLocations( locations: GlobeProps['locations'] ) {

  if ( !g?.facilities || g.facilities?.length === 0 )
    return

  Object.keys( locations ).forEach(( key ) => {

    const position = new Vector3()
    const facility = g.facilities.find( facility => facility.code === key )

    locations[key].getWorldPosition( position )

    facility.position = projectObjectPositionIn2D(
      position,
      g.camera,
      globeContainer.value.clientWidth,
      globeContainer.value.clientHeight
    )

  })

}

async function init() {

  // Create the renderer

  g.renderer = new WebGLRenderer({ antialias: true, alpha: true })
  g.renderer.setPixelRatio( window.devicePixelRatio )
  g.renderer.setSize( globeContainer.value.clientWidth, globeContainer.value.clientHeight )

  globeContainer.value.appendChild( g.renderer.domElement )

  // Create the scene

  g.scene = new Scene()
  g.scene.add( new AmbientLight( 0xBBBBBB, 0.3 ))

  // Create the camera

  g.camera = new PerspectiveCamera()
  g.camera.aspect = globeContainer.value.clientWidth / globeContainer.value.clientHeight
  g.camera.updateProjectionMatrix()

  // Create the lights

  const dL1 = new DirectionalLight( 0xFFFFFF, 0.8 )
  dL1.position.set( -800, 2000, 400 )
  g.camera.add( dL1 )

  const dL2 = new DirectionalLight( 0x7982F6, 1 )
  dL2.position.set( -200, 500, 200 )
  g.camera.add( dL2 )

  const pL = new PointLight( 0x8566CC, 0.5 )
  pL.position.set( -200, 500, 200 )
  g.camera.add( pL )

  // Set the camera position

  g.camera.position.z = 210
  g.camera.position.y = 70
  g.camera.position.x = -35

  g.scene.add( g.camera )

  g.scene.fog = new Fog( 0x535EF3, 400, 2000 )

  g.raycaster = new Raycaster()
  g.mouse = new Vector2()

  initGlobe()
  render()
  resize()
  rotateCardIndex()

  window.addEventListener( 'resize', resize )
  window.addEventListener( 'pointermove', handleFollowMouse )

}

function initGlobe() {

  g.globeMaster = new Group()
  g.globeMouseFollow = new Group()

  g.globe = new ThreeGlobe({
    waitForGlobeReady: true,
    animateIn:         true
  })

  // Set the globe properties

  g.globe
    .hexPolygonsData( countries.features )
    .hexPolygonResolution( 3 )
    .hexPolygonMargin( 0.5 )
    .showAtmosphere( true )
    .atmosphereColor( '#3a228a' )
    .atmosphereAltitude( 0.25 )
    .hexPolygonColor(() => 'rgba(123, 154, 250, 1)' )

  // Set the globe material

  const globeMaterial = g.globe.globeMaterial() as any
  globeMaterial.color = new Color( '#0E1D61' )
  globeMaterial.opacity = 0.8
  globeMaterial.transparent = true
  globeMaterial.depthWrite = false

  // Set the locations

  const baseMat = new MeshBasicMaterial({ color: '#FB6628' })

  g.locations.DC7 = new Mesh( new SphereGeometry( 1, 6 ), baseMat )
  g.locations.DC1 = new Mesh( new SphereGeometry( 1, 6 ), baseMat )
  g.locations.DC6 = new Mesh( new SphereGeometry( 1, 6 ), baseMat )

  g.locations.DC7.position.set( -60.0088371942631, 72.5949736179838, 10.719861315998658 )
  g.locations.DC1.position.set( -60.94689636737687, 80.28896556499335, -17.775863682184017 )
  g.locations.DC6.position.set( -75.64225473370547, 60.705491164908345, -35.98025363497826 )

  g.locationsGroup = new Scene()
  g.locationsGroup.add( g.locations.DC7 )
  g.locationsGroup.add( g.locations.DC1 )
  g.locationsGroup.add( g.locations.DC6 )

  g.locationsGroup.visible = false

  g.globeMaster.add( g.globe )
  g.globeMaster.add( g.locationsGroup )
  g.globeMouseFollow.add( g.globeMaster )

  g.globeMaster.rotation.y = -250.2
  g.globeMaster.rotation.x = 0.2

  g.scene.add( g.globeMouseFollow )

}

function openOperationalStatusInfoDrawer( facility: GlobeFacility, index: number ) {

  openOperationalStatusDrawer.value = true
  activeOperationalStatus.value = facility
  activeOperationalStatusIndex.value = index

}

function render() {

  g.renderer.render( toRaw( g.scene ), g.camera )

  get2dLocations( g.locations )

  g.animationFrameReference = requestAnimationFrame( render )

}

function resize() {

  g.mobile = window.innerWidth < 768

  if ( window.innerWidth < 768 ) {

    g.mobile = true
    g.globeMouseFollow.rotation.set( 0, 0, 0 )

  }

  else { g.mobile = false }

  g.camera.aspect = globeContainer.value.clientWidth / globeContainer.value.clientHeight
  g.camera.updateProjectionMatrix()

  g.viewHalfX = globeContainer.value.clientWidth / 1.5
  g.viewHalfY = globeContainer.value.clientHeight / 1.5

  g.renderer.setSize( globeContainer.value.clientWidth, globeContainer.value.clientHeight )

}

function getMouseDegrees( x: number, y: number, degreeLimit: number ) {

  let dx = 0
  let dy = 0
  let xDiff: number
  let yDiff: number
  let xPercentage: number
  let yPercentage: number

  const w = { x: window.innerWidth, y: window.innerHeight }

  if ( x <= w.x / 2 ) {
    xDiff = w.x / 2 - x
    xPercentage = ( xDiff / ( w.x / 2 )) * 100
    dx = (( degreeLimit * xPercentage ) / 100 ) * -1
  }

  if ( x >= w.x / 2 ) {
    xDiff = x - w.x / 2
    xPercentage = ( xDiff / ( w.x / 2 )) * 100
    dx = ( degreeLimit * xPercentage ) / 100
  }

  if ( y <= w.y / 2 ) {
    yDiff = w.y / 2 - y
    yPercentage = ( yDiff / ( w.y / 2 )) * 100
    dy = ((( degreeLimit * 0.5 ) * yPercentage ) / 100 ) * -1
  }

  if ( y >= w.y / 2 ) {
    yDiff = y - w.y / 2
    yPercentage = ( yDiff / ( w.y / 2 )) * 100
    dy = ( degreeLimit * yPercentage ) / 100
  }

  return { x: dx, y: dy }

}

function followMouse( mouse: { x: number, y: number }, target: Group, degreeLimit: number ) {

  if ( g.mobile )
    return

  if ( target ) {

    const degrees = getMouseDegrees( mouse.x, mouse.y, degreeLimit )

    target.rotation.y = MathUtils.degToRad( degrees.x )
    target.rotation.z = MathUtils.degToRad( degrees.y )

  }

}

function handleFollowMouse( e: MouseEvent ) {

  g.mouse.x = ( e.clientX / window.innerWidth ) * 2 - 1
  g.mouse.y = -( e.clientY / window.innerHeight ) * 2 + 1

  const target: Group = g.globeMouseFollow
  const followStrength: number = 8

  followMouse({ x: e.clientX, y: e.clientY }, target, followStrength )

}

watch( globeContainer, ( n ) => {
  if ( n )
    init()
})

onBeforeUnmount(() => {

  clearTimeout( timeout.value )
  cancelAnimationFrame( g.animationFrameReference )

  window.removeEventListener( 'resize', resize )

})

</script>

<template>

  <div class="w-full h-full relative">

    <div
      id="globe-container"
      ref="globeContainer"
      class="w-full h-full grid place-content-end bg-linear-to-tr from-main-120 via-main-90 to-owd"
    />

    <!-- Desktop :: Facilities -->

    <div
      v-for="facility, index in g.facilities"
      :key="facility.code"
      class="absolute z-1 w-4 h-4 hidden md:block -translate-x-2 -translate-y-2"
      :style="{
        left: `${facility.position.x}px`,
        top: `${facility.position.y}px`,
      }"
    >

      <div class="absolute bottom-4 left-1/2 -translate-x-1/2">

        <GlobeFacilityCard
          :index="index"
          :image-src="globeFacilityCardsImages[index]"
          :facility="facility"
          :auto-index="autoIndex"
          @set-active-index="(index) => activeIndex = index"
          @set-active-facility="openOperationalStatusInfoDrawer"
        />

      </div>

      <!-- Location Circles -->

      <div class="w-4 h-4 absolute -bottom-2 left-1/2 -translate-x-1/2">

        <div
          class="
          w-full h-full relative rounded-full
          after:w-[120%] after:h-[120%] after:absolute after:-left-[10%] after:-top-[10%] after:border-2 after:rounded-full after:animate-ping
          before:w-[160%] before:h-[160%] before:absolute before:-left-[30%] before:-top-[30%] before:border before:rounded-full before:animate-[ping_1s_cubic-bezier(0,0,0.2,1)_infinite]
          "
          :class="{
            'bg-error after:border-error before:border-error': facility.status === 3,
            'bg-warning after:border-warning before:border-warning': facility.status === 2,
            'bg-data2-120 after:border-data2-120 before:border-data2-120': facility.status === 1,
          }"
        />

      </div>

    </div>

    <!-- Mobile :: Facilities -->

    <div class="w-full absolute z-1 top-24 px-4 grid md:hidden gap-y-2">

      <GlobeFacilityCard
        v-for="facility, index in g.facilities"
        :key="facility.code"
        :index="index"
        :image-src="globeFacilityCardsImages[index]"
        :facility="facility"
        :auto-index="null"
        @set-active-facility="openOperationalStatusInfoDrawer"
      />

    </div>

    <Sidebar
      :dim="true"
      :open="openOperationalStatusDrawer"
      @close="openOperationalStatusDrawer = false"
    >
      <OperationalStatusInfoDrawer
        :facility="activeOperationalStatus"
        :image-src="globeFacilityCardsImages[activeOperationalStatusIndex]"
        @close="openOperationalStatusDrawer = false"
      />

    </Sidebar>

  </div>

</template>
