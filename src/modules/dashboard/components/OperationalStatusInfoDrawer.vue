<script setup lang="ts">

import { Guard } from '@/plugins/guard'
import { appMode } from '@/store'
import { statusList } from '@/modules/dashboard/store'
import { growNumberToTarget } from '@lib/scripts/utils'
import { computed, onMounted, ref } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/button/Button.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import EditOperationalStatusForm from '@/modules/dashboard/components/EditOperationalStatusForm.vue'

import type { GlobeFacility } from '@/modules/dashboard/types'

const props = defineProps<{
  facility: GlobeFacility
  imageSrc: string
}>()

defineEmits<{
  ( eventName: 'close' ): void
}>()

const iconName                  = computed(() => props.facility.locked ? 'locked' : props.facility.status === 1 ? 'checkmark' : props.facility.status === 2 ? 'issue-circle' : 'warning-sign' )
const callsNumber               = ref( 10 )
const chatsNumber               = ref( 100 )
const emailsNumber              = ref( 25 )
const openOperationalStatusEdit = ref( false )

onMounted(() => {

  growNumberToTarget( callsNumber.value, ( value ) => {
    callsNumber.value = value
  })

  growNumberToTarget( emailsNumber.value, ( value ) => {
    emailsNumber.value = value
  })

  growNumberToTarget( chatsNumber.value, ( value ) => {
    chatsNumber.value = value
  })

})

function openOperationalStatusEditDrawer() {
  openOperationalStatusEdit.value = true
}

</script>

<template>

  <div class="w-full md:w-[30.25rem] h-full bg-layer-01 flex flex-col ">

    <div class="w-full h-fit flex items-center justify-between pl-4 border-b border-b-border-subtle-00">

      <div class="flex h-full items-center">

        <div class="flex gap-3">
          <Icon name="supplier" size="m" class="text-main" />
          <span class="text-sm font-medium">{{ facility.display }}</span>
        </div>

      </div>

      <div class="flex items-center">

        <Guard :scope="appMode">

          <div class="h-full border-x border-border-subtle-00">

            <Button
              mode="ghost"
              class="gap-x-2"
              @click="openOperationalStatusEditDrawer"
            >

              <p class="text-sm">
                Edit
              </p>

              <Icon name="edit" size="s" />

            </Button>

          </div>

        </Guard>

        <Button
          type="box"
          mode="ghost"
          icon="close"
          @click="$emit('close')"
        />

      </div>

    </div>

    <div class="h-full overflow-y-auto ">

      <div
        class="w-full h-52 bg-cover bg-no-repeat bg-center"
        :style="{ backgroundImage: `url(${imageSrc})` }"
      />

      <div
        class="text-core-10 flex items-center justify-between px-4 py-3"
        :class="{
          'bg-error': facility.status === 3,
          'bg-warning': facility.status === 2,
          'bg-data2-120': facility.status === 1,
          'cursor-pointer': facility.locked,
        }"
      >

        <div class="flex flex-col">

          <span class="font-medium flex items-center gap-2">
            <Icon :name="iconName" size="s" />
            {{ facility.locked ? 'Locked' : statusList?.find(status => status?.id === facility?.status)?.name }}
          </span>

          <span v-if="facility.locked">Want to expand your business?  Speak to our specialist.</span>
          <span v-if="!facility.locked && facility.status !== 1">{{ facility.operationalTitle }}</span>

        </div>

        <div v-if="facility.locked">

          <Icon name="go" size="m" />

        </div>

      </div>

      <div class="flex flex-col gap-4 md:px-[1.625rem] md:py-8 p-4">

        <h3 class="text-lg">
          About
        </h3>

        <p class="text-sm text-text-helper">
          {{ facility.description }}
        </p>

      </div>

    </div>

    <Button v-if="facility.locked">
      Expand My Business
    </Button>

    <Sidebar
      :open="openOperationalStatusEdit"
      :strict="true"
      @close="openOperationalStatusEdit = false"
    >
      <EditOperationalStatusForm :facility-reference="facility.code" @close="openOperationalStatusEdit = false" />
    </Sidebar>

  </div>

</template>
