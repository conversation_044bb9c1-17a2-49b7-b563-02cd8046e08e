<script setup lang="ts">

import { growNumberToTarget } from '@lib/scripts/utils'
import { computed, ref, watch } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'

import type { IconProps } from '@lib/store/icon'

const props = withDefaults( defineProps<{

  type?:          'default' | 'error'
  icon?:          IconProps
  label?:         string
  loading?:       boolean
  content?:       number
  contentSuffix?: string | number

}>(), {
  type:    'default',
  label:   'Kpi Label',
  content: 0
})

const kpiIcon         = computed(() => props.icon )
const dynamicContent  = ref<number>( 0 )

watch(() => props.content, n => growNumberToTarget( n, value => dynamicContent.value = value ), { immediate: true })

</script>

<template>

  <div
    class="px-4 h-16 md:h-auto md:py-4 grid md:grid-cols-1 grid-cols-[1fr_max-content] md:grid-rows-2 items-center rounded-xs"
    :class="{
      'bg-main-20 hover:bg-main-30': type === 'default',
      'bg-error/30 hover:bg-error/40': type === 'error',
    }"
  >

    <div class="w-full grid md:grid-cols-[1fr_max-content] grid-cols-[max-content_1fr] gap-4 items-center">

      <div class="col-start-2 row-start-1 md:col-start-auto">

        <slot name="label">
          <p
            class="md:text-lg font-medium"
            :class="{
              'text-error': type === 'error',
            }"
          >
            {{ label }}
          </p>
        </slot>

      </div>

      <div class="text-error col-start-1 row-start-1 md:col-start-auto">
        <slot name="icon">
          <Icon :name="kpiIcon.name" :size="kpiIcon.size" />
        </slot>
      </div>

    </div>

    <slot>
      <div v-if="loading" class="py-2" :class="{ 'text-main': type === 'default', 'text-error': type === 'error' }">
        <Icon name="loading" size="m" />
      </div>
      <p
        v-else
        class="text-xl md:text-3xl flex flex-col items-end md:inline-block font-semibold"
        :class="{
          'text-main': type === 'default',
          'text-error': type === 'error',
        }"
      >
        {{ dynamicContent }} <span class="text-core text-sm font-normal">{{ contentSuffix ? `${contentSuffix}` : '' }}</span>
      </p>
    </slot>

  </div>

</template>
