<script setup lang="ts">

import { ref } from 'vue'
import { CcvGaugeChart } from '@carbon/charts-vue'
import { getColorHexFromRoot } from '@lib/scripts/utils'

import type { GaugeChartData } from '@/modules/orders/types'
import type { GaugeChartOptions } from '@carbon/charts-vue'

defineProps<{
  statusData?: GaugeChartData
}>()

const statusChartOptions = ref<GaugeChartOptions>({
  resizable: true,
  height:    '100%',
  width:     '100%',
  toolbar:   {
    enabled: false
  },
  gauge: {
    type:          'semi',
    status:        'success',
    arcWidth:      8,
    alignment:     'center',
    valueFontSize: () => 0
  },
  color: {
    scale: {
      value: getColorHexFromRoot( '--support-caution-major' )
    }
  }
})

</script>

<template>

  <div class="w-36 md:w-44 lg:w-44 xl:w-56 h-full relative grid place-items-end">

    <div class="h-full absolute bottom-0 p-4 pt-6">

      <div class="w-fit absolute top-2.5 lg:top-8 xl:top-4 right-[80%] xl:right-[70%]">

        <p class="whitespace-nowrap text-xs md:text-sm text-support-caution-major font-medium">
          Fulfillment Status
        </p>

        <div class="bg-border-subtle-00 w-px h-[0.438rem] rotate-[140deg] absolute -right-[0.375rem] -bottom-1" />

      </div>

      <CcvGaugeChart
        :data="[statusData]"
        :options="statusChartOptions"
        class="absolute bottom-0 xl:-bottom-4 lg:-bottom-8"
      />

      <p class="text-xl md:text-2xl text-interactive absolute left-1/2 -translate-x-1/2 bottom-6 md:bottom-10 lg:bottom-4">
        {{ statusData?.value }}%
      </p>

      <p class="text-xs text-text-hero lg:text-text-placeholder absolute left-1/2 -translate-x-1/2 bottom-3 md:bottom-8 lg:bottom-1">
        Today
      </p>

    </div>

  </div>

</template>
