<script setup lang="ts">

import { ref, watch } from 'vue'
import { userFirstName } from '@/modules/auth/store'
import { growNumberToTarget } from '@lib/scripts/utils'

import FulfillmentStatus from '@/modules/dashboard/components/FulfillmentStatusDashboard.vue'

import type { GaugeChartData, OrdersByStatusPayload } from '@/modules/orders/types'

const props = defineProps<{ ordersByStatus?: OrdersByStatusPayload }>()

const fulfillmentStatusPercent = ref<GaugeChartData>({
  group: 'value',
  value: 0
}, )

watch(() => props.ordersByStatus, () => {

  growNumberToTarget( props.ordersByStatus?.fulfillmentStatusPercent, value => fulfillmentStatusPercent.value.value = value )

}, { deep: true, immediate: true })

</script>

<template>

  <div class="w-full h-24 md:h-32 grid grid-cols-[1fr_max-content] pr-0 md:pr-8 lg:pr-0">

    <div class="pl-4 md:pl-8 flex items-center">

      <p class="text-lg md:text-2xl text-text-hero lg:text-text-primary">
        Hello <span class="text-support-caution-major whitespace-nowrap">{{ userFirstName }}</span>, <br> See what's new today.
      </p>

    </div>

    <FulfillmentStatus :status-data="fulfillmentStatusPercent" />

  </div>

</template>
