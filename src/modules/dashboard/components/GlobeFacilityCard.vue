<script setup lang="ts">

import { computed } from 'vue'
import { statusList } from '@/modules/dashboard/store'

import Icon from '@lib/components/blocks/Icon.vue'

import type { GlobeFacility } from '@/modules/dashboard/types'

const props = defineProps<{
  index:     number
  imageSrc:  string
  facility:  GlobeFacility | null
  autoIndex: number | null
}>()

const emits = defineEmits<{
  setActiveIndex:    [ index: number ]
  setActiveFacility: [ facility: GlobeFacility, index: number ]
}>()

const showCard            = computed(() => ( props.index === props.autoIndex ))
const iconName            = computed(() => props.facility?.locked ? 'locked' : props.facility?.status === 1 ? 'checkmark' : props.facility?.status === 2 ? 'issue-circle' : 'warning-sign' )
const facilityRegionText  = computed(() => props.facility?.display?.split( '-' )[0] )
const facilityDisplayText = computed(() => props.facility?.display?.split( '-' )[1] )

function openCard() {
  emits( 'setActiveIndex', props.index )
}

function closeCard() {
  emits( 'setActiveIndex', null )
}

</script>

<template>

  <div
    class="relative z-10"
    @mouseenter="openCard"
    @mouseleave="closeCard"
  >

    <div
      class="w-max h-7 relative px-2 hidden md:flex items-center space-x-2 bg-core-100 rounded-sm transition-all duration-300"
      :class="{
        'text-error': facility?.status === 3,
        'text-warning': facility?.status === 2,
        'text-data2-120': facility?.status === 1,
        'opacity-100 translate-y-0': !showCard,
        'opacity-0 -translate-y-4 pointer-events-none': showCard,
      }"
    >

      <Icon :name="iconName" size="s" />

      <p class="text-sm font-medium pointer-events-none">
        {{ facilityDisplayText }}
      </p>

    </div>

    <!-- Desktop :: Facility Card Popup -->

    <div
      class="
      w-48 h-40 absolute bottom-1 left-1/2 -translate-x-1/2 hidden md:grid grid-rows-[max-content_1fr] bg-core-100 rounded cursor-pointer transition-all duration-300
      before:w-3 before:h-3 before:absolute before:left-1/2 before:-translate-x-1/2 before:rotate-45 before:-bottom-1.5 before:bg-core-100
      "
      :class="{
        'opacity-0 -translate-y-2 pointer-events-none': !showCard,
        'opacity-100 translate-y-0': showCard,
      }"
      @click="() => emits('setActiveFacility', facility, index)"
    >

      <div
        class="w-full h-24 bg-core-100 rounded-t bg-cover bg-no-repeat bg-center"
        :style="{ backgroundImage: `url(${imageSrc})` }"
      />

      <div class="h-full py-1.5">

        <div class="pl-2 pr-3 grid grid-cols-[1fr_max-content] items-center">

          <p class="text-[10px] text-core-40">
            {{ facilityRegionText }}
          </p>

          <Icon
            name="go"
            size="m"
            class="text-core-40 transition-all duration-300"
            :class="{
              'opacity-0 -translate-x-4': !showCard,
              'opacity-100 translate-x-0': showCard,
            }"
          />

        </div>

        <div class="px-2">

          <p class="text-xs text-core-10">
            {{ facilityDisplayText }}
          </p>

        </div>

        <div
          class="mt-0.5 text-xs px-2 flex items-center space-x-1"
          :class="{
            'text-error': facility?.status === 3,
            'text-warning': facility?.status === 2,
            'text-data2-120': facility?.status === 1,
          }"
        >

          <Icon :name="iconName" size="s" />

          <p class="grow">
            {{ facility?.locked ? 'Locked' : statusList?.find(status => status?.id === facility?.status)?.name }}
          </p>

          <p v-if="facility?.operationalPercentage > 0 && facility?.operationalPercentage < 100">
            {{ facility?.operationalPercentage }}%
          </p>

        </div>

      </div>

    </div>

    <!-- Mobile :: Facility Card -->

    <div
      class="w-full h-10 md:hidden grid grid-cols-[max-content_1fr] bg-core-100 rounded-sm cursor-pointer"
      @click="() => emits('setActiveFacility', facility, index)"
    >

      <div
        class=" w-[3.75rem] h-full bg-core-100 rounded-l bg-cover bg-no-repeat bg-center"
        :style="{ backgroundImage: `url(${imageSrc})` }"
      />

      <div class="px-3">

        <div class="mt-1.5 grid grid-cols-[1fr_max-content]">

          <p class="text-[10px] text-core-40">
            {{ facilityRegionText }}
          </p>

          <div
            class="text-xs flex items-center space-x-1"
            :class="{
              'text-error': facility?.status === 3,
              'text-warning': facility?.status === 2,
              'text-data2-120': facility?.status === 1,
            }"
          >

            <p>
              {{ facility?.locked ? 'Locked' : statusList?.find(status => status?.id === facility?.status)?.name }}
            </p>

            <Icon :name="iconName" size="s" />

          </div>

        </div>

        <p class="-mt-1 text-xs text-core-10">
          {{ facilityDisplayText }}
        </p>

      </div>

    </div>

  </div>

</template>
