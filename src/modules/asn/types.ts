export interface AsnParams extends BaseParams {
  id?:              number
  asnStatus?:       number
  clientReference?: number
  clientPo?:        string
  shipperName?:     string
  expectedDate?:    string
  sku?:             string
  itemTitle?:       string
  notes?:           string
  facilityCode?:    string
}

export interface Asn {
  id?:                  number
  asnStatus?:           string
  clientReference?:     string
  clientPo?:            string
  shipperName?:         string
  stockRelease?:        string
  expectedDate?:        string
  lastReceivedDate?:    string
  pendingReceiveCount?: number
  skuCount?:            number
  clientId:             number
}

export interface AsnWidgets {
  pending?:        number
  received?:       number
  partialReceipt?: number
  cancelled?:      number
}

export interface AsnDetails {
  id?:              number
  clientReference?: string
  clientPo?:        string
  shipperName:      string
  expectedDate:     Date
  notes?:           string
  carrier:          string
  cartonCount:      number
  palletCount:      number
  facilityCode:     string
  facility:         string
  createdBy?:       string
  isAutoRelease?:   boolean
  hasBlind?:        boolean
  asnStatus?:       string
  clientId?:        number
  clientFKey?:      number
  createdDate?:     Date
  isActive?:        boolean
}

export interface AsnDetailsInfo {
  id?:              number
  clientReference?: string
  clientPo?:        string
  shipperName:      string
  expectedDate:     Date
  notes?:           string
  carrier:          string
  cartonCount:      number
  palletCount:      number
  facilityCode?:    string
  facility?:        string
  createdBy?:       string
  isAutoRelease?:   boolean
  hasBlind?:        boolean
  asnStatus?:       string
}

export interface AsnItemReceive {
  id:               number
  receiveId:        number
  quantityReceived: number
  quantityDamaged:  number
  quantityPackslip: number
  notes:            string
  toLocation:       string
}

export interface AsnItem {
  id?:              number
  description?:     string
  sku:              string
  title?:           string
  inventoryId?:     number
  expectedUnits?:   number
  receivedUnits?:   number
  price?:           number
  notes?:           string
  itemReceives?:    AsnItemReceive[]
  children?:        any[]
  isReceive?:       boolean
  quantityDamaged?: number
}

export interface ReceiveItem {
  id:             number
  receiveDate:    Date
  receiveMin:     number
  receiveBy:      string
  receiveCartons: number
  receivePallets: number
  receiveNotes:   string
}

export interface ReceiveParams extends BaseParams {
  isRpc?:     boolean
  receiveId?: string
}

export interface CloneAsnData {
  isBlind?:     boolean
  createdBy:    string
  expectedDate: Date
}
