import { ref } from 'vue'
import { fetchy } from '@/plugins/fetchy'

import type {
  Asn,
  AsnDetails,
  AsnDetailsInfo,
  AsnItem,
  AsnItemReceive,
  AsnParams,
  AsnWidgets,
  CloneAsnData,
  ReceiveItem,
  ReceiveParams
} from '@/modules/asn/types'

export enum AsnStatus {
  PENDING = 0,
  RECEIVED = 1,
  PARTIAL_RECEIPT = 2,
  CANCELED = 3
}

export enum AsnCarriers {
  DHL = 'DHL',
  UPS = 'UPS',
  USPS = 'USPS',
  FEDEX = 'FedEx',
  UNKNOWN = 'Unknown',
  LTL_TRUCK = 'LTL Truck'
}

export const asnCarriersList: DropListOption[] = [
  {
    id:   AsnCarriers.UNKNOWN,
    name: AsnCarriers.UNKNOWN
  },
  {
    id:   AsnCarriers.UPS,
    name: AsnCarriers.UPS
  },
  {
    id:   AsnCarriers.USPS,
    name: AsnCarriers.USPS
  },
  {
    id:   AsnCarriers.FEDEX,
    name: AsnCarriers.FEDEX
  },
  {
    id:   AsnCarriers.LTL_TRUCK,
    name: AsnCarriers.LTL_TRUCK
  },
  {
    id:   AsnCarriers.DHL,
    name: AsnCarriers.DHL
  }
]

export const asnStatusList: DropListOption[] = [
  {
    id:   AsnStatus.PENDING,
    name: 'Pending'
  },
  {
    id:   AsnStatus.RECEIVED,
    name: 'Received'
  },
  {
    id:   AsnStatus.PARTIAL_RECEIPT,
    name: 'Partial Receipt'
  },
  {
    id:   AsnStatus.CANCELED,
    name: 'Canceled'
  },
]

export const asn                = ref<AsnDetails>()
export const isRpc              = ref<boolean>( false )
export const isCached           = ref<boolean>( false )
export const asnReceivesData    = ref<PaginatedResponse<'asnReceives', ReceiveItem[]>>()
export const preventModalClose  = ref<boolean>( false )

export function resetAsnStore() {
  asn.value = null
  asnReceivesData.value = null
}

export async function createAsn( asnDetails: AsnDetailsInfo ) {
  return await fetchy<AsnDetails>({
    url:    `asn`,
    method: 'POST',
    data:   asnDetails
  })
}

export async function getAsns( params: AsnParams ) {
  return await fetchy<PaginatedResponse<'asnList', Asn[]>>({
    url:    `asn`,
    method: 'GET',
    params,
  })
}

export async function getAsn( asnId: number, showError = true ) {
  return await fetchy<AsnDetails>({
    url:    `asn/${asnId}`,
    method: 'GET',
    showError
  })
}

export async function exportAsn( asnId: number ) {

  return await fetchy<ArrayBuffer>({
    url:          `export/asn/${asnId}/excel`,
    method:       'GET',
    responseType: 'blob'
  })

}

export async function getAsnItems( asnId: number, params?: ReceiveParams ) {
  return await fetchy<PaginatedResponse<'asnItems', Tablify<AsnItem, AsnItemReceive>[]>>({
    url:    `asn/${asnId}/items`,
    method: 'GET',
    params
  })
}

export async function getAsnReceives( asnId: number, params?: BaseParams ) {
  return await fetchy<PaginatedResponse<'asnReceives', ReceiveItem[]>>({
    url:    `asn/${asnId}/receives`,
    method: 'GET',
    params
  })
}

export async function updateAsnDetails( asnId: number, data: AsnDetails ) {
  return await fetchy<AsnDetails>({
    url:    `asn/${asnId}`,
    method: 'PUT',
    data
  })
}

export async function updateAsnItem( asnId: number, data: AsnItem ) {
  return await fetchy<AsnItem>({
    url:    `asn/${asnId}/items/${data.id}`,
    method: 'PUT',
    data
  })
}

export async function createAsnItem( asnId: number, data: AsnItem ) {
  return await fetchy<AsnItem>({
    url:    `asn/${asnId}/items`,
    method: 'POST',
    data
  })
}

export async function getAsnItem( asnId: number, itemId: number ) {
  return await fetchy<AsnItem>({
    url:       `asn/${asnId}/items-by-inventory/${itemId}`,
    method:    'GET',
    showError: false
  })
}

export async function cloneAsn( asnId: number, data: CloneAsnData ) {
  return await fetchy<AsnDetails>({
    url:    `asn/${asnId}/clone`,
    method: 'POST',
    data
  })
}

export async function deleteAsnItem( asnId: number, itemId: number ) {
  return await fetchy<AsnItem>({
    url:    `asn/${asnId}/items/${itemId}`,
    method: 'DELETE',
  })
}

export async function deactivateAsn( asnId: number, ) {
  return await fetchy({
    url:    `asn/${asnId}/deactivate`,
    method: 'POST',
  })
}

export async function getAsnWidgets() {
  return await fetchy<AsnWidgets>({
    url:    `widgets/asn-status-count`,
    method: 'GET',
  })
}

export async function bulkExportAsns( asnIds: number[] ) {
  return await fetchy<Blob>({
    url:          `export/asn`,
    data:         { asnIds },
    method:       'POST',
    responseType: 'blob'
  })
}
