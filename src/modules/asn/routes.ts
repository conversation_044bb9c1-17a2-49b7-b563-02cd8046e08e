import { pageSize } from '@lib/store/table'

import type { AsnParams, ReceiveParams } from '@/modules/asn/types'

export const asnParams: AsnParams = {
  page:     1,
  pageSize: pageSize.value
}

export const receiveParams: ReceiveParams = {
  page:      1,
  pageSize:  pageSize.value,
  receiveId: null
}

export const asnRoutes: RouteModel<AsnParams> = {
  name:      'Asns',
  path:      '/asn',
  component: () => import( '@/modules/asn/views/AsnList.vue' ),
  meta:      {
    title: 'ASNs - OWD',
    scope: 'Asn.Read'
  },
  children: [
    {
      name: 'Asn Details',
      path: '/asn/details/:asnId',
      meta: {
        title: 'ASN Details - OWD',
        scope: 'Asn.Read'
      },
      component: () => import( '@/modules/asn/views/AsnDetails/AsnDetails.vue' ),
      children:  [
        {
          name: 'Asn Items Inventory',
          path: '/asn/details/:asnId/inventory',
          meta: {
            title: 'ASN Products - OWD',
            scope: [ 'Asn.Read', 'Asn.Write' ]
          },
          component: () => import( '@/components/InventoryPanel.vue' )
        }
      ]
    },
    {
      name: 'Create Asn',
      path: '/asn/create',
      meta: {
        title: 'Create ASN - OWD',
        scope: [ 'Asn.Read', 'Asn.Write' ]
      },
      component: () => import( '@/modules/asn/views/CreateAsn/views/CreateAsn.vue' )
    }
  ]
}
