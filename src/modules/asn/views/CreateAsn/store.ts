import { Flow } from '@/modules/asn/views/CreateAsn/types'
import { computed, ref } from 'vue'
import { validateModel } from '@lib/scripts/inputValidation'
import { convertObjectToValidatable } from '@lib/scripts/utils'

import type { AsnDetailsInfo } from '@/modules/asn/types'
import type { EditableAsnItem } from '@/modules/asn/views/CreateAsn/types'
import type { InventoryPanelProduct } from '@/modules/inventory/types'

/*
 * Controls the flow of the create order wizard.
 */

export const storedStep = ref<Flow>( Flow.GENERAL )
export const activeStep = ref<Flow>( Flow.GENERAL )

export const asnDetailsInfo: AsnDetailsInfo = {
  notes:           null,
  carrier:         'Unknown',
  clientPo:        null,
  facility:        null,
  palletCount:     0,
  cartonCount:     0,
  shipperName:     null,
  facilityCode:    null,
  expectedDate:    null,
  isAutoRelease:   true,
  clientReference: null
}

export const asnItems                   = ref<EditableAsnItem[]>( [] )
export const selectedItems              = ref<InventoryPanelProduct[]>( [] )
export const asnGeneralInfo             = ref<Validatable<AsnDetailsInfo>>( convertObjectToValidatable( asnDetailsInfo, null, [ 'facility', 'clientReference', 'clientPo', 'notes', 'isAutoRelease' ] ))
export const createAsnInProgress        = ref( false )
export const asnProductsListStepValid   = computed(() => !!asnItems.value.length && !asnItems.value.some( item => !item.quantity ))
export const asnGeneralDetailsStepValid = computed(() => validateModel( asnGeneralInfo.value ))

export function getTotalUnits() {
  return asnItems.value.reduce(( totalUnits, product ) => totalUnits + ( product.quantity || 0 ), 0 )
}

export function removeSelectedAsnItem( id: number ) {
  selectedItems.value = selectedItems.value.filter( item => item.id !== id )
}

export const activeFlow = computed(() =>
  activeStep.value === Flow.GENERAL
    ? Flow.GENERAL
    : activeStep.value
)

export function resetAsnCreateStore() {

  asnGeneralInfo.value = convertObjectToValidatable( asnDetailsInfo, null, [ 'facility', 'clientReference', 'clientPo', 'notes', 'isAutoRelease' ], )
  storedStep.value = Flow.GENERAL
  activeStep.value = Flow.GENERAL
  asnItems.value = []

}
