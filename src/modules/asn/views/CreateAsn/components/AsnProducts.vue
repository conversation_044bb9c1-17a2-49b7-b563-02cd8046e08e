<script setup lang="ts">

import { Flow } from '@/modules/asn/views/CreateAsn/types'
import { validateModel } from '@lib/scripts/inputValidation'
import { computed, reactive, ref } from 'vue'
import { convertObjectToValidatable } from '@lib/scripts/utils'
import { activeStep, asnItems, asnProductsListStepValid, removeSelectedAsnItem } from '@/modules/asn/views/CreateAsn/store'

import Input from '@lib/components/inputs/Input.vue'
import Button from '@lib/components/button/Button.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import Textbox from '@lib/components/inputs/Textbox.vue'
import AsnItemsTable from '@/modules/asn/views/CreateAsn/components/AsnItemsTable.vue'
import InventoryPanel from '@/components/InventoryPanel.vue'

import type { EditableAsnItem } from '@/modules/asn/views/CreateAsn/types'

const editableAsnItemModel: EditableAsnItem = {
  id:                  null,
  sku:                 null,
  notes:               null,
  title:               null,
  price:               null,
  quantity:            null,
  supplier:            null,
  description:         null,
  backOrdered:         null,
  kitComponents:       null,
  availableQuantity:   null,
  availableByFacility: null
}

const canUpdate           = computed(() => validateModel( asnItemModel ))
const asnItemModel        = reactive<Validatable<EditableAsnItem>>( convertObjectToValidatable( editableAsnItemModel, null, [ 'kitComponents', 'sku', 'id', 'price', 'title', 'description', 'id', 'notes', 'supplier', 'backOrdered', 'availableByFacility', 'availableQuantity' ] ))
const openEditAsnProduct  = ref<boolean>( false )

function updateAsnItem( ) {

  asnItems.value.forEach(( item ) => {

    if ( item.sku === asnItemModel.sku.value ) {
      item.notes = asnItemModel.notes.value
      item.title = asnItemModel.title.value
      item.description = asnItemModel.description.value
      item.quantity = asnItemModel.quantity.value
      openEditAsnProduct.value = false
    }

  })

  openEditAsnProduct.value = false

}

function removeProduct( item: EditableAsnItem ) {
  removeSelectedAsnItem( item.id )
  asnItems.value = asnItems.value.filter( itemToRemove => item.sku !== itemToRemove.sku )
}

function openEditItemDrawer( product: EditableAsnItem ) {

  const selectedProduct = asnItems.value.find( item => item.sku === product.sku )

  if ( selectedProduct ) {
    asnItemModel.sku.value = selectedProduct.sku
    asnItemModel.title.value = selectedProduct.title
    asnItemModel.notes.value = selectedProduct.notes
    asnItemModel.description.value = selectedProduct.description
    asnItemModel.quantity.value = selectedProduct.quantity
  }

  openEditAsnProduct.value = true

}

defineExpose({
  openEditAsnProduct,
  asnItemModel,
  canUpdate
})

</script>

<template>
  <div class="grid grid-cols-[1fr] grid-rows-[1fr_max-content] overflow-hidden">

    <div class="grid grid-cols-[1fr] md:grid-cols-[max-content_1fr] grid-rows-[1fr_max-content] overflow-hidden">

      <div class="h-full md:w-[26rem] bg-core-20 overflow-hidden md:border-r md:border-r-core-30">

        <InventoryPanel
          v-model:selected-products="asnItems"
          :allow-price-update="false"
          :actions="{
            editItemAction: openEditItemDrawer,
          }"
          @remove-product="removeProduct"
        />

      </div>

      <div data-section="products" class="h-full hidden md:grid grid-rows-[max-content_1fr_max-content] overflow-hidden">

        <div class="h-[2.531rem] pl-4 flex items-center bg-core-20 border-b border-core-30">

          <div class="h-full grow flex items-center">
            <p class="text-sm font-medium">
              Selected Products
            </p>
          </div>

        </div>

        <div class="w-full h-full overflow-hidden lg:px-[1.625rem] lg:pt-[1.625rem] md:bg-core-20 lg:bg-transparent">

          <AsnItemsTable
            table-name="ASN Products"
            :asn-item-model="asnItemModel"
            @open-asn-product-edit="openEditAsnProduct = true"
          />

        </div>

      </div>

      <div class="md:hidden">

        <Button
          :disabled="!asnProductsListStepValid"
          class="h-12 w-full"
          @click="activeStep = Flow.SUMMARY"
        >
          Continue
        </Button>

      </div>

      <Sidebar
        :dim="true"
        :open="openEditAsnProduct"
        :strict="true"
        @close="openEditAsnProduct = false"
      >

        <div class="h-full w-full md:w-[26.75rem] relative grid grid-rows-[max-content_1fr_max-content]">

          <div class="w-full h-12 flex items-center border-b border-core-30">

            <div class="h-full px-4 flex items-center grow">

              <p class="text-sm font-medium">
                Edit Product: <span class="text-main truncate">[{{ asnItemModel.sku.value }}]</span>
              </p>

            </div>

            <Button
              size="l"
              type="box"
              mode="ghost"
              icon="close"
              @click="openEditAsnProduct = false"
            />

          </div>

          <div class="overflow-y-auto">
            <form
              class="md:p-8 grid md:gap-4"
              @submit.prevent
            >
              <Input
                v-model="asnItemModel.sku.value"
                v-model:valid="asnItemModel.sku.valid"
                label="SKU"
                :required="true"
                readonly
              />
              <Input
                v-model="asnItemModel.title.value"
                v-model:valid="asnItemModel.title.valid"
                label="Title"
                :required="true"
              />

              <Input
                v-model="asnItemModel.quantity.value"
                v-model:valid="asnItemModel.quantity.valid"
                type="strict-number"
                :min="1"
                label="Unit"
                :required="true"
              />

              <Textbox
                v-model="asnItemModel.description.value"
                v-model:valid="asnItemModel.description.valid"
                label="Description"
                :required="false"
              />

              <Textbox
                v-model="asnItemModel.notes.value"
                v-model:valid="asnItemModel.notes.valid"
                label="Notes"
                :required="false"
              />
            </form>
          </div>

          <div class="w-full h-12 sticky top-0 z-1 grid bg-core-20 border-t border-core-30">

            <Button
              class="font-medium"
              :disabled="!canUpdate"
              data-button="save-product"
              @click="updateAsnItem"
            >
              Save Product
            </Button>

          </div>

        </div>

      </Sidebar>

    </div>

    <div class="w-full min-h-12 hidden md:flex items-center justify-end bg-core-20 md:border-t md:border-t-core-30">

      <Button
        size="auto"
        class="h-full w-auto px-12"
        :disabled="!asnProductsListStepValid"
        data-button="continue"
        @click="activeStep = Flow.SUMMARY"
      >
        Continue
      </Button>

    </div>

  </div>

</template>
