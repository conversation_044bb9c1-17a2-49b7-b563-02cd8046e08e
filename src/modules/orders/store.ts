import { fetchy } from '@/plugins/fetchy'

import type {
  AddEventPostData,
  BulkOrdersResponse,
  CreateDraftOrderPostData,
  CreateLiveOrderPostData,
  DraftOrder,
  EditableDraftOrderLineItem,
  EditableOrderLineItem,
  Order,
  OrderDetails,
  OrderEvent,
  OrderLineItem,
  OrderLineItemsParams,
  OrdersByChannelPayload,
  OrdersByStatusOrder,
  OrdersByStatusPayload,
  OrdersParams,
  ShippedOrdersByChannelPayload,
  StackedAreaChartData,
  TopSellingProduct,
  UpdateOrderDetailsPostData
} from '@/modules/orders/types'

import type { DraftWidgets, LiveOrderWidgets } from '@/types'

// ---- [ STATIC DATA ]
// ---- Used to populate the dropdowns in the orders modules.

export const orderTypes: DropListOption[] = [
  {
    id:   0,
    name: 'Consumer',
  },
  {
    id:   1,
    name: 'Business'
  }
]

export const paymentTypesList: DropListOption[] = [
  {
    id:          'CLIENT',
    name:        'Client',
    description: 'Use CLIENT if OWD does not handle payment processing. If CLIENT is specified, the paid attribute should be "YES".'
  }
]

export const internalTaxOptions: DropListOption[] = [
  {
    id:   1,
    name: 'DDP Delivered Duty Paid'
  },
  {
    id:   0,
    name: 'DDU Delivered Duty Unpaid'
  }
]

export const backorderRulesList: DropListOption[] = [
  {
    id:   'BACKORDER',
    name: 'Backorder'
  },
  {
    id:   'HOLDORDER',
    name: 'Hold Order'
  },
  {
    id:   'NOBACKORDER',
    name: 'Release for Shipping'
  },
  {
    id:   'PARTIALSHIP',
    name: 'Partial Ship'
  }
]

// ---- [ LIVE ORDERS REQUESTS ]

export async function getOrders( params: OrdersParams ) {

  return await fetchy<PaginatedResponse<'orders', Order[]>>({
    url:    `orders`,
    method: 'GET',
    params
  })

}

export async function getLiveOrdersWidgets( ) {

  return await fetchy<LiveOrderWidgets>({
    url:    `widgets/orders-count-by-status`,
    method: 'GET',
  })

}

export async function getOrder( orderId: number, isRpc: boolean = false, showError: boolean = true ) {

  return await fetchy<OrderDetails>({
    url:    `orders/${orderId}`,
    method: 'GET',
    params: isRpc ? { isRpc } : null,
    showError
  })

}

export async function updateOrder( orderId: number, data: UpdateOrderDetailsPostData ) {

  return fetchy<OrderDetails>({
    url:    `orders/${orderId}`,
    method: 'PUT',
    data
  })

}

export async function createLiveOrder( data: CreateLiveOrderPostData ) {

  return fetchy<OrderDetails>({
    url:    `orders`,
    method: 'POST',
    data
  })

}

export async function createDraftOrder( data: CreateDraftOrderPostData ) {

  return fetchy<OrderDetails>({
    url:    `stage/orders`,
    method: 'POST',
    data
  })

}

export async function voidOrder( orderId: number ) {

  return await fetchy({
    url:    `orders/${orderId}/void`,
    method: 'PUT'
  })

}

export async function holdOrder( orderId: number ) {

  return await fetchy({
    url:    `orders/${orderId}/hold`,
    method: 'PUT'
  })

}

export async function releaseOrder( orderId: number, partialShip: boolean ) {

  return await fetchy({
    url:    `orders/${orderId}/release?partialShip=${partialShip}`,
    method: 'PUT'
  })

}

export async function unholdOrder( orderId: number ) {

  return await fetchy({
    url:    `orders/${orderId}/unhold`,
    method: 'PUT'
  })

}

export async function unpostOrder( orderId: number ) {

  return await fetchy({
    url:    `orders/${orderId}/unpost`,
    method: 'PUT'
  })

}

export async function addEvent( orderId: number, data: AddEventPostData ) {

  return await fetchy<{ events: OrderEvent[] }>({
    url:    `orders/${orderId}/events`,
    method: 'POST',
    data
  })

}

export async function exportLiveOrder( orderId: number ) {

  return await fetchy<ArrayBuffer>({
    url:          `export/orders/${orderId}/excel`,
    method:       'GET',
    responseType: 'blob'
  })

}

export async function addOrderLineItem( orderId: number, data: Partial<OrderLineItem> ) {

  return await fetchy<EditableOrderLineItem>({
    url:    `orders/${orderId}/line-items`,
    data,
    method: 'POST'
  })

}

export async function updateOrderLineItem( orderId: number, itemSku: string, data: Partial<OrderLineItem> ) {

  return await fetchy<PaginatedResponse<'lineItems', EditableOrderLineItem[]>>({
    url:    `orders/${orderId}/line-items/${itemSku}`,
    data,
    method: 'PUT'
  })

}

export async function deleteOrderLineItem( orderId: number, itemId: number ) {

  return await fetchy<PaginatedResponse<'lineItems', EditableOrderLineItem[]>>({
    url:    `orders/${orderId}/line-items/${itemId}`,
    method: 'DELETE'
  })

}

export async function getOrderLineItem( orderId: number, productSku: string ) {

  return await fetchy<EditableOrderLineItem>({
    url:       `orders/${orderId}/items-by-inventory/${productSku}`,
    method:    'GET',
    showError: false
  })

}

export async function getOrderLineItems( orderId: number, params: OrderLineItemsParams ) {

  return await fetchy<PaginatedResponse<'lineItems', EditableOrderLineItem[]>>({
    url:    `orders/${orderId}/line-items`,
    method: 'GET',
    params
  })

}

export async function updateLineItems( orderId: number, data: Partial<OrderLineItem>[] ) {

  return await fetchy<PaginatedResponse<'lineItems', EditableOrderLineItem[]>>({
    url:    `orders/${orderId}/line-items`,
    method: 'PUT',
    data
  })

}

export async function batchDeleteLineItems( orderId: number, lineItemsToDelete: string[] ) {

  return await fetchy<PaginatedResponse<'lineItems', EditableOrderLineItem[]>>({
    url:    `orders/${orderId}/line-items/delete-batch`,
    data:   { lineItemsToDelete },
    method: 'POST'
  })

}

export async function exportPackingSlip( orderId: number ) {

  return await fetchy<{ url: string, expiresAt: string }>({
    url: `orders/${orderId}/packslip`
  })

}

export async function bulkExportOrders( orderIds: number[], isImportExport: boolean = false ) {

  return await fetchy<Blob>({
    url:          `export/orders?isImportExport=${isImportExport}`,
    data:         { orderIds },
    method:       'POST',
    responseType: 'blob'
  })

}

// ---- [ DRAFT ORDERS REQUESTS ]

export async function getDraftOrder( orderId: number ) {

  return await fetchy<DraftOrder>({
    url:    `stage/orders/${orderId}`,
    method: 'GET'
  })

}

export async function updateDraftOrder( orderId: number, data: DraftOrder ) {

  return fetchy<DraftOrder>({
    url:    `stage/orders/${orderId}`,
    method: 'PUT',
    data
  })

}

export async function getDraftOrders( params: OrdersParams ) {

  return await fetchy<PaginatedResponse<'stageOrders', DraftOrder[]>>({
    url:    `stage/orders`,
    method: 'GET',
    params
  })

}

export async function deleteDraftOrder( orderId: number ) {

  return await fetchy({
    url:    `stage/orders/${orderId}`,
    method: 'DELETE'
  })

}

export async function getDraftOrderLineItems( orderId: number, params: BaseParams ) {

  return await fetchy<PaginatedResponse<'stageOrderLineItems', EditableDraftOrderLineItem[]>>({
    url:    `stage/orders/${orderId}/line-items`,
    method: 'GET',
    params
  })

}

export async function getDraftOrderLineItem( orderId: number, itemId: number ) {

  return await fetchy<EditableDraftOrderLineItem>({
    url:       `stage/orders/${orderId}/line-items/${itemId}`,
    method:    'GET',
    showError: false
  })

}

export async function getDraftOrderLineItemBySku( orderId: number, sku: string ) {

  return await fetchy<EditableDraftOrderLineItem>({
    url:       `stage/orders/${orderId}/items-by-inventory/${sku}`,
    method:    'GET',
    showError: false
  })

}

export async function deleteDraftOrderLineItem( orderId: number, itemId: number ) {

  return await fetchy({
    url:    `stage/orders/${orderId}/line-items/${itemId}`,
    method: 'DELETE'
  })

}

export async function createDraftOrderLineItem( orderId: number, data: Partial<EditableDraftOrderLineItem> ) {

  return await fetchy<EditableDraftOrderLineItem>({
    url:    `stage/orders/${orderId}/line-items`,
    data,
    method: 'POST'
  })

}

export async function updateDraftOrderLineItem( orderId: number, itemId: number, data: Partial<EditableDraftOrderLineItem> ) {

  return await fetchy<EditableDraftOrderLineItem>({
    url:    `stage/orders/${orderId}/line-items/${itemId}`,
    data,
    method: 'PUT'
  })

}

export async function publishDraftOrders( ordersIds: number[] ) {

  return fetchy({
    url:    `stage/orders/bulk-operations/publish`,
    method: 'POST',
    data:   {
      stagingOrderIdList: ordersIds
    }
  })

}

export async function getDraftOrdersWidgets() {

  return await fetchy<DraftWidgets>({
    url:    `widgets/staging-orders-by-status`,
    method: 'GET'
  })

}

export async function bulkUploadOrders( document: FormData ) {

  return await fetchy<BulkOrdersResponse>({
    url:     `stage/orders/bulk-operations/import`,
    method:  'POST',
    data:    document,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })

}

export async function bulkDeleteDraftOrders( stagingOrderIdList: number[] ) {

  return await fetchy({
    url:    `stage/orders/bulk-operations/delete`,
    data:   { stagingOrderIdList },
    method: 'POST'
  })

}

// ---- [ SUMMARY REQUESTS, METHODS & STATIC DATA ]

export async function getOrdersByStatus() {

  return await fetchy<OrdersByStatusPayload>({
    url:    `widgets/orders-by-status`,
    method: 'GET'
  })

}

export async function getOrdersByChannel() {

  return await fetchy<OrdersByChannelPayload>({
    url:    `widgets/orders-by-channel`,
    method: 'GET'
  })

}

export async function getShippedOrdersByChannel() {

  return await fetchy<ShippedOrdersByChannelPayload>({
    url:    `widgets/shipped-orders-by-channel`,
    method: 'GET'
  })

}

export async function getTopSellingItems() {

  return await fetchy<{ products: TopSellingProduct[] }>({
    url:    `widgets/top-selling-products`,
    method: 'GET'
  })

}

/**
 * @description Combines the chart data by date.
 * @param {StackedAreaChartData} data The chart data to combine.
 * @returns Combined chart data.
 */
function combineDataByDate( data: StackedAreaChartData ) {

  const combinedData: StackedAreaChartData = []

  data.forEach(( item ) => { // ------------------------------------------ Iterate trough the data,

    const itemDateString = item.date.toISOString().split( 'T' )[0] // ---- Extract the date part (YYYY-MM-DD) without the time,

    const existingItem = combinedData.find(( d ) => { // ----------------- Check if there is an item with the same date and return it.

      const dDateString = d.date.toISOString().split( 'T' )[0]

      return dDateString === itemDateString

    })

    if ( existingItem ) // ----------------------------------------------- If there is an item with the same date, add the value to it.
      existingItem.value += item.value

    else combinedData.push({ ...item }) // ------------------------------- If there is no item with the same date, add the item to the combined data.

  })

  return combinedData

}

/**
 * @description Maps the orders data to the card data.
 * @param {OrdersByStatusOrder[]} orders The orders to map to the card data.
 * @param {'shippingDate' | 'orderDate'} dateKey The key to map the date to.
 * @returns {StackedAreaChartData} The mapped orders data.
 */
export function mapCardData( orders: OrdersByStatusOrder[], dateKey: 'orderDate' | 'shippingDate' ): StackedAreaChartData {

  const mappedOrders: StackedAreaChartData = []

  for ( const order of orders ) { // --------------- Iterate trough the orders data.

    mappedOrders.push({
      date:  new Date( order[dateKey] ), // -------- Map the order date to the chart date.
      group: 'Dataset 1', // ----------------------- Set the same group for every order.
      value: order.orderCount // ------------------- Set the order count as the value.
    })

  }

  if ( mappedOrders.length > 0 )
    return combineDataByDate( mappedOrders ) // ---- Combine the data by date.

  else
    return [ { group: 'Dataset 1', date: null, value: 0 } ]

}

// ---- [ HARD CODED DATA used for the coming soon TrendCards. Only backordered is used. ]
export const hardcodeCallCenterData: OrdersByStatusPayload  = {
  onHold:      [],
  backordered: [
    { orderStatus: 'Backorder(Active)', shippingMethod: 'Economy', orderCount: 1, orderDate: '2024-07-01T00:00:00+01:00', shippingDate: null },
    { orderStatus: 'Backorder(Active)', shippingMethod: 'Overnight', orderCount: 3, orderDate: '2024-07-03T00:00:00+01:00', shippingDate: null },
    { orderStatus: 'Backorder(Active)', shippingMethod: 'Standard Priority', orderCount: 7, orderDate: '2024-07-04T00:00:00+01:00', shippingDate: null },
    { orderStatus: 'Backorder(Active)', shippingMethod: 'LTL', orderCount: 9, orderDate: '2024-07-08T00:00:00+01:00', shippingDate: null },
    { orderStatus: 'Backorder(Active)', shippingMethod: 'LTL', orderCount: 6, orderDate: new Date().toString(), shippingDate: null },

  ],
  shipped:                  [],
  atWarehouse:              [],
  voided:                   [],
  unknown:                  [],
  fulfillmentStatusPercent: null,
  shippingMethodKeys:       [],
  totalNumberOrders:        null
}
