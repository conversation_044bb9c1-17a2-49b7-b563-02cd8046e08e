<script setup lang="ts">

import CounterWidget from '@/components/CounterWidget.vue'

import type { OrdersParams } from '@/modules/orders/types'
import type { LiveOrderWidgets } from '@/types'

defineProps<{
  widgets:   LiveOrderWidgets | null
  disabled?: boolean
}>()

defineEmits<{
  filter: [ payload: OrdersParams ]
}>()

</script>

<template>

  <div class="p-2 sm:p-4 md:px-[1.625rem] grid grid-cols-3 sm:grid-cols-2 xl:grid-cols-5 gap-2 xl:gap-4">

    <CounterWidget
      icon="on-hold"
      title="On Hold"
      color="warning"
      :count="widgets?.onHoldCount"
      :disabled="disabled"
      :action="() => $emit('filter', { isHeld: true })"
    />

    <CounterWidget
      icon="backorder"
      title="Backorder"
      color="data1-120"
      :count="widgets?.backorderedCount"
      :disabled="disabled"
      :action="() => $emit('filter', { isBackorder: true })"
    />

    <CounterWidget
      icon="at-warehouse-widget"
      title="At Warehouse"
      color="main"
      :count="widgets?.atWarehouseCount"
      :disabled="disabled"
      :action="() => $emit('filter', { atWarehouse: true })"
    />

    <CounterWidget
      icon="shipped-widget"
      title="Shipped"
      color="success"
      :count="widgets?.shippedCount"
      :disabled="disabled"
      :action="() => $emit('filter', { isShipped: true })"
    />

    <CounterWidget
      icon="void"
      title="Void"
      color="error"
      :count="widgets?.voidCount"
      :disabled="disabled"
      :action="() => $emit('filter', { isVoid: true })"
    />

  </div>

</template>
