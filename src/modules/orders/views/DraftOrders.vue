<script setup lang="ts">

import { guard } from '@/plugins/guard'
import { useI18n } from 'vue-i18n'
import { confirm } from '@lib/store/confirm.ts'
import { ordersParams } from '@/modules/orders/routes'
import { useRoute, useRouter } from 'vue-router'
import { setNotificationOptions } from '@lib/store/snackbar'
import { computed, reactive, ref, watch } from 'vue'
import { booleanToYesNo, defaultFacility, facilitiesList, importStatusList } from '@/store'
import { compareObjects, formatDate, removeEmptyKeysFromObject, sanitizeQueryParams, viewSetup } from '@lib/scripts/utils'
import { bulkDeleteDraftOrders, bulkUploadOrders, deleteDraftOrder, getDraftOrders, getDraftOrdersWidgets, publishDraftOrders } from '@/modules/orders/store'

import Tag from '@lib/components/blocks/tag/Tag.vue'
import Icon from '@lib/components/blocks/Icon.vue'
import Table from '@lib/components/table/Table.vue'
import Input from '@lib/components/inputs/Input.vue'
import Select from '@lib/components/inputs/Select.vue'
import Button from '@lib/components/button/Button.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import DatePicker from '@lib/components/inputs/DatePicker.vue'
import StatusBadge from '@lib/components/blocks/status-badge/StatusBadge.vue'
import DraftWidgetsBlock from '@/components/DraftWidgetsBlock.vue'

import type { BatchOption } from '@lib/types/tableTypes'
import type { DraftWidgets, SearchFilter } from '@/types'
import type { DraftOrder, DraftOrdersParams } from '@/modules/orders/types'

interface DraftOrderGridData extends DraftOrder {
  customerLastName?:  string
  customerFirstName?: string
}

interface DraftOrderBooleanParamsTemp {
  hasErrors:       number
  isBusinessOrder: number
  hasMasterRecord: number
}

const { t }       = useI18n()
const total       = ref<number>( 0 )
const route       = useRoute()
const router      = useRouter()
const orders      = ref<Tablify<DraftOrderGridData>[]>( [] )
const params      = reactive<DraftOrdersParams>({ ...ordersParams, ...sanitizeQueryParams( route.name === 'Draft Orders' ? route.query : {}) })
const maxPages    = ref<number>( 0 )
const openFilters = ref( false )
const hasFacility = ref<boolean>( defaultFacility.value !== 'ALL' )
const widgetsData = ref<DraftWidgets>( null )

/**
 * Returns the color scheme associated with a given import status.
 *
 * @param {string} importStatus - The status of the import (e.g., "Draft", "Pending", "Processing", "Failed", "Processed").
 * @returns {string} The corresponding color scheme for the provided import status.
 * Defaults to 'gray' if the status is not recognized.
 */

function getColorScheme( importStatus: string ): string {
  const colorSchemeMap: Record<string, string> = {
    Draft:      'gray',
    Pending:    'purple',
    Processing: 'blue',
    Failed:     'red',
    Processed:  'green',
  }
  return colorSchemeMap[importStatus] || 'gray'
}

/**
 * -- TABLE SCHEMA
 *
 * Sets the status of the record based on the key in the importErrors array
 *
 * @param key
 * @param record
 */

function setRecordStatus( key: keyof DraftOrderGridData, record: DraftOrderGridData ): TableRecordStatus {

  const fieldError = record?.importErrors?.find( e => e.propertyName === key )

  if ( !fieldError )
    return null

  return {
    type:    'error',
    message: fieldError.errorMsg
  }

}

function schema( record: DraftOrderGridData ): TableSchema<DraftOrderGridData> {
  return [
    {
      key:     'id',
      label:   'Draft ID',
      sortKey: 'orderId',
      status:  setRecordStatus( 'id', record )
    },
    {
      key:      null,
      label:    'Status',
      lockFlex: true,
      value:    {
        component: StatusBadge,
        props:     {
          colorScheme: getColorScheme( record?.importStatus ),
          label:       record?.importStatus === 'Processing' ? 'In Progress' : record?.importStatus
        }
      }
    },
    {
      key:    'masterRecordId',
      link:   record?.masterRecordId ? { name: 'Live Order Details', params: { orderId: record.masterRecordId } } : null,
      label:  'Order ID',
      value:  record?.masterRecordId ?? null,
      status: setRecordStatus( 'masterRecordId', record )
    },
    {
      key:     'clientReference',
      label:   t( 'orders.columnLabel.clientReference' ),
      sortKey: 'clientReference',
      status:  setRecordStatus( 'clientReference', record )
    },
    {
      key:     'facilityCode',
      sortKey: 'facilityCode',
      label:   t( 'orders.columnLabel.facility' ),
      status:  setRecordStatus( 'facilityCode', record )
    },
    {
      key:     'isBusinessOrder',
      sortKey: 'isBusinessOrder',
      label:   t( 'orders.columnLabel.orderType' ),
      value:   record?.isBusinessOrder !== null ? record?.isBusinessOrder ? 'Business' : 'Consumer' : null
    },
    {
      key:     'customerFirstName',
      label:   'First Name',
      sortKey: 'customerFirstName',
      status:  setRecordStatus( 'customerFirstName', record )
    },
    {
      key:     'customerLastName',
      label:   'Last Name',
      sortKey: 'customerLastName',
      status:  setRecordStatus( 'customerLastName', record )
    },
    {
      key:     'createdTs',
      label:   'Date Created',
      format:  'date',
      sortKey: 'createdTs',
      status:  setRecordStatus( 'createdTs', record )
    }
  ]
}

/**
 * -- SEARCH / FILTER DRAFT ORDERS
 */

const searchOrdersDefaultModel: DraftOrdersParams = {
  email:           null,
  orderId:         null,
  lastName:        null,
  firstName:       null,
  hasErrors:       null,
  importStatus:    null,
  facilityCode:    null,
  createdDateTo:   null,
  clientReference: null,
  isBusinessOrder: null,
  createdDateFrom: null,
  hasMasterRecord: null,
}

const draftOrderBooleanParamsTempModel = ref<DraftOrderBooleanParamsTemp> ({
  hasErrors:       null,
  isBusinessOrder: null,
  hasMasterRecord: null
})

const searchOrdersModel = reactive({ ...searchOrdersDefaultModel, ...sanitizeQueryParams( route.name === 'Draft Orders' ? route.query : {}) })

function generateFilters( selectedFilters: Partial<DraftOrdersParams> ): SearchFilter<DraftOrdersParams>[] {
  return [
    {
      key:   'createdDateFrom',
      label: t( 'orders.searchFilterLabel.createdStartDate' ),
      value: selectedFilters?.createdDateFrom ? formatDate( selectedFilters?.createdDateFrom, 'MMM, DD YYYY' ) : null
    },
    {
      key:   'createdDateTo',
      label: t( 'orders.searchFilterLabel.createdEndDate' ),
      value: selectedFilters?.createdDateTo ? formatDate( selectedFilters?.createdDateTo, 'MMM, DD YYYY' ) : null
    },
    {
      key:   'facilityCode',
      label: t( 'orders.searchFilterLabel.facility' ),
      value: facilitiesList.value?.find( item => item.id === selectedFilters?.facilityCode )?.name ?? null
    },
    {
      key:   'importStatus',
      label: 'Import Status',
      value: importStatusList.find( item => item.id === selectedFilters.importStatus )?.name ?? null
    },
    {
      key:   'orderId',
      label: 'Draft ID',
      value: selectedFilters?.orderId ?? null
    },
    {
      key:   'clientReference',
      label: t( 'orders.searchFilterLabel.clientReferenceNumber' ),
      value: selectedFilters?.clientReference ?? null
    },
    {
      key:   'firstName',
      label: t( 'orders.searchFilterLabel.customerFirstName' ),
      value: selectedFilters?.firstName ?? null
    },
    {
      key:   'lastName',
      label: t( 'orders.searchFilterLabel.customerLastName' ),
      value: selectedFilters?.lastName ?? null
    },
    {
      key:   'email',
      label: t( 'orders.searchFilterLabel.customerEmail' ),
      value: selectedFilters?.email ?? null
    },
    {
      key:   'isBusinessOrder',
      label: selectedFilters.isBusinessOrder ? 'Is Business Order' : 'Is Consumer Order',
      value: selectedFilters.isBusinessOrder !== null && selectedFilters.isBusinessOrder !== undefined ? 'Yes' : null
    },
    {
      key:   'hasMasterRecord',
      label: t( 'orders.searchFilterLabel.hasMasterRecord' ),
      value: booleanToYesNo( selectedFilters.hasMasterRecord )
    },
    {
      key:   'hasErrors',
      label: 'Has Errors',
      value: booleanToYesNo( selectedFilters.hasErrors )
    }
  ]
}

function downloadExampleCsv() {
  window.open( 'https://owdnewportalstaticassets.z5.web.core.windows.net/bulk_orders_upload_example.csv', '_blank' )
}

function downloadExampleExcel() {
  window.open( 'https://owdnewportalstaticassets.z5.web.core.windows.net/bulk_orders_upload_example.xlsx', '_blank' )
}

function downloadInstructionsPdf() {
  window.open( 'https://owdnewportalstaticassets.z5.web.core.windows.net/Bulk_Upload_Orders.pdf', '_blank' )
}

const searchOrdersFilters = computed(() => generateFilters( removeEmptyKeysFromObject( params )))
const hasFiltersApplied   = computed(() => searchOrdersFilters.value.some( filter => !!filter.value ))

function searchOrders() {

  Object.keys( searchOrdersModel ).forEach(( key ) => {
    params[key] = searchOrdersModel[key]
  })

  openFilters.value = false

}

function resetFilters() {
  Object.keys( searchOrdersDefaultModel ).forEach(( key ) => {
    params[key] = searchOrdersDefaultModel[key]
    searchOrdersModel[key] = searchOrdersDefaultModel[key]
    draftOrderBooleanParamsTempModel.value[key] = searchOrdersDefaultModel[key]
  })
}

function resetSearchModel() {

  Object.keys( searchOrdersDefaultModel ).forEach(( key ) => {
    searchOrdersModel[key] = searchOrdersDefaultModel[key]
    draftOrderBooleanParamsTempModel.value[key] = searchOrdersDefaultModel[key]
  })

}

function removeFilter( key: string ) {

  params[key] = null
  searchOrdersModel[key] = null
  draftOrderBooleanParamsTempModel.value[key] = null

}

function filterOrdersByWidget( filterParams: DraftOrdersParams ) {

  resetFilters()

  Object.keys( filterParams ).forEach(( key ) => {
    params[key] = filterParams[key]
    searchOrdersModel[key] = filterParams[key]
  })

}

// Watch for changes in the URL query params
// If the URL params are changed but the state params are not,
// update the filter models to match the URL params.

watch( route, ( n ) => {

  const URLParams   = sanitizeQueryParams( n.query )
  const cleanParams = removeEmptyKeysFromObject( params )

  if ( !compareObjects( URLParams, cleanParams )) {

    // Add new params to the models

    for ( const key in URLParams ) {

      if ( searchOrdersModel.hasOwnProperty( key ))
        searchOrdersModel[key] = URLParams[key]

    }

    // Remove non existing params from the models

    for ( const key in searchOrdersModel ) {

      if ( !URLParams.hasOwnProperty( key ))
        searchOrdersModel[key] = null

    }

    for ( const key in draftOrderBooleanParamsTempModel.value ) {

      if ( !URLParams.hasOwnProperty( key ))
        draftOrderBooleanParamsTempModel.value[key] = null

    }

  }

})

/**
 * -- GET DRAFT ORDERS DATA
 */

async function getWidgetsData() {

  const { payload } = await getDraftOrdersWidgets()

  widgetsData.value = payload

}

async function getOrdersList( viewParams: DraftOrdersParams ) {

  const { payload } = await getDraftOrders( viewParams )

  total.value = payload?.totalRows ?? 0
  orders.value = payload?.stageOrders ?? []
  maxPages.value = payload?.totalPages ?? 0

  orders.value.forEach(( order ) => {

    const statusMessage
      = order?.importErrors?.some( e => e.propertyName === 'record' )
        ? order?.importErrors?.find( e => e.propertyName === 'record' )?.errorMsg
        : order?.importErrorCount > 0
          ? `This order has ${order.importErrorCount} import error${order?.importErrorCount > 1 ? 's' : ''}.`
          : null

    const status: TableRecordStatus = {
      type:    'error',
      count:   order.importErrorCount,
      message: statusMessage
    }

    order.tableRecordStatus = status

  })

}

/**
 * -- PUBLISH DRAFT ORDERS
 */

async function publishSelectedOrders( selectedOrders: DraftOrderGridData[] ) {

  const { error } = await publishDraftOrders( selectedOrders.map( product => product.id ))

  if ( !error )
    await updateView()

}

async function deleteSelectedDraftOrders( ordersIds: number[] ) {

  const { error } = await bulkDeleteDraftOrders( ordersIds )

  if ( !error ) {
    setNotificationOptions({ message: 'The selected draft orders were deleted successfully.' })
    await updateView()
  }

}

/**
 * -- ROW OPTIONS && BULK ACTIONS
 */

function orderRowOptions( record: DraftOrderGridData ): DropListOption[] {
  return [
    {
      id:   1,
      name: t( 'orders.details.detailsText' ),
      icon: {
        name: 'edit'
      },
      action: () => {
        router.push({ name: 'Draft Order Details', params: { orderId: record.id } })
      }
    },
    {
      id:     2,
      name:   'Delete',
      hidden: !guard( 'Order.Write' ) || ![ 'Draft', 'Failed', 'Processed' ].includes( record.importStatus ),
      icon:   {
        name:  'delete',
        color: 'red'
      },
      action: () => confirm({
        header:      'Are you sure you want to delete this draft order?',
        description: 'This action cannot be undone.',
        action:      () => deleteDraftOrder( record.id ),
        onSuccess:   () => {
          updateView()
          setNotificationOptions({ message: 'Draft order is deleted successfully.' })
        }
      })
    }
  ]
}

function toolbarOptions( hasFilters: boolean ): ToolbarOption[] {
  return [
    {
      id:      'draft-orders-filters',
      name:    t( 'global.label.search', { name: 'Draft Orders' }),
      icon:    { name: 'search' },
      compact: false,
      action:  () => { openFilters.value = !openFilters.value },
    },
    {
      id:      'draft-orders-filters-reset',
      name:    'Reset Filters',
      icon:    { name: 'reset' },
      hidden:  !hasFilters,
      compact: true,
      action:  resetFilters
    }
  ]
}

const ordersBatchOptions: BatchOption<DraftOrderGridData>[] = [
  {
    id:             1,
    group:          'Bulk Actions',
    icon:           'live',
    type:           'positive',
    filter:         order => order.importStatus === 'Draft' && order.importErrorCount === 0,
    action:         ( selectedOrders: DraftOrder[] ) => publishSelectedOrders( selectedOrders ),
    actionName:     'Publish',
    description:    'Ready to Publish',
    pendingMessage: 'Publishing Orders',
  },
  {
    id:             2,
    group:          'Bulk Actions',
    icon:           'delete',
    type:           'negative',
    filter:         order => guard( 'Order.Write' ) && [ 'Draft', 'Failed' ].includes( order.importStatus ),
    action:         selected => deleteSelectedDraftOrders( selected.map( order => order.id )),
    actionName:     'Delete',
    pendingMessage: 'Deleting Orders',
  }
]

function closeBulkUploadDrawer( blockUpdate = true ) {
  blockPageUpdate( blockUpdate )
  router.push({ name: 'Draft Orders' })
}

async function uploadOrders( file: File ) {

  const formData = new FormData()

  formData.append( 'file', file )

  const response = await bulkUploadOrders( formData )

  if ( !response.error ) {

    setNotificationOptions({
      message: 'Success',
      details: `Imported ${response.payload.totalRowsInserted} out of ${response.payload.totalRowsInserted + response.payload.totalRowsRejected} rows.`
    })

    closeBulkUploadDrawer( false )
  }

}

const { pending, updateView, blockPageUpdate } = viewSetup(
  'Draft Orders',
  params,
  router,
  [
    { callback: getOrdersList },
    { callback: getWidgetsData, ignoreParams: 'all' }
  ],
  null,
  true
)

</script>

<template>

  <div class="h-full grid grid-rows-[max-content_1fr] overflow-hidden">

    <DraftWidgetsBlock
      :widgets="widgetsData"
      :disabled="pending"
      @filter="filterOrdersByWidget"
    />

    <div class="grid md:px-[1.625rem] overflow-hidden sm:pt-4">

      <Table
        v-model:params="params"
        name="Draft Orders"
        icon="draft-order"
        class="md:border md:border-border-subtle-00"
        :flex="true"
        :schema="schema"
        :records="orders"
        :pending="pending"
        :selectable="guard('Order.Write')"
        record-map-key="id"
        resource-name="Order"
        :record-options="orderRowOptions"
        :batch-options="ordersBatchOptions"
        :toolbar-options="toolbarOptions(hasFiltersApplied)"
        :pagination="{
          total,
          maxPages,
        }"
        :enable-column-chooser="true"
      >

        <template #table-neck>

          <div v-if="hasFiltersApplied" class="p-2 flex flex-wrap items-center gap-1 border-b border-border-subtle-00">

            <Tag
              v-for="filter in searchOrdersFilters"
              v-show="filter.value"
              :key="filter.key"
              :label="`${filter.label}: ${filter.value}`"
              @remove="() => removeFilter(filter.key)"
            >
              <p>{{ filter.label }}: <span class="font-medium">{{ filter.value }}</span></p>
            </Tag>

          </div>

        </template>

      </Table>

    </div>

    <Sidebar
      :open="openFilters"
      :strict="false"
      :dim="true"
      @close="openFilters = false"
    >

      <div class="w-full h-full flex flex-col">

        <!-- Search Header -->
        <div class="w-full h-12 sticky top-0 z-1 flex shrink-0 items-center bg-layer-01 border-b border-border-subtle-00">

          <div class="h-full px-4 flex items-center space-x-3 grow border-r border-border-subtle-00">

            <p class="text-sm font-medium">
              {{ $t('global.label.search', { name: 'Draft Orders' }) }}
            </p>

          </div>

          <div class="h-full border-r border-border-subtle-00">

            <Button mode="ghost" size="auto" class="h-full px-4 flex items-center space-x-2" @click="resetSearchModel">

              <p class="text-sm">
                Reset Filters
              </p>

              <Icon name="reset" size="s" class="text-main" />

            </Button>

          </div>

          <Button
            type="box"
            size="auto"
            mode="ghost"
            icon="close"
            class="w-12 h-full min-w-[3rem]"
            @click="openFilters = false"
          />

        </div>

        <!-- Search Options -->
        <div class="w-full h-full overflow-hidden overflow-y-auto">
          <form class="w-full md:w-[46rem] md:max-w-[46rem] md:px-6 grid md:gap-4" @submit.prevent>

            <!-- Date Created -->
            <section>

              <div class="h-12 sticky top-0 z-1 px-4 md:px-2 flex items-center md:col-span-2 bg-layer-01 border-b md:border-b-0 border-border-subtle-00">
                <p class="text-xs text-core uppercase">
                  {{ $t('orders.searchFilterLabel.dateCreated') }}
                </p>
              </div>

              <div class="grid md:grid-cols-2 md:gap-4">
                <DatePicker
                  v-model="searchOrdersModel.createdDateFrom"
                  :label="$t('orders.searchFilterLabel.startDate')"
                  :required="false"
                  :limit-to="searchOrdersModel.createdDateTo"
                />

                <DatePicker
                  v-model="searchOrdersModel.createdDateTo"
                  :label="$t('orders.searchFilterLabel.endDate')"
                  :required="false"
                  :limit-from="searchOrdersModel.createdDateFrom"
                />
              </div>

            </section>

            <!-- Search By -->
            <section>
              <div class="h-12 w-full sticky top-0 z-1 px-4 md:px-2 flex items-center bg-layer-01 border-b md:border-b-0 border-border-subtle-00">
                <p class="text-xs text-core uppercase">
                  {{ $t('global.label.search', { name: 'By' }) }}
                </p>
              </div>

              <div class="grid md:grid-cols-2 md:gap-4">
                <Select
                  v-show="!hasFacility"
                  v-model="searchOrdersModel.facilityCode"
                  :disabled="hasFacility"
                  :label="$t('orders.searchFilterLabel.facility')"
                  class="hidden md:block md:col-span-2"
                  :required="false"
                  :options="facilitiesList"
                  :teleport="false"
                />

                <Select
                  v-show="!hasFacility"
                  v-model="searchOrdersModel.facilityCode"
                  :disabled="hasFacility"
                  :label="$t('orders.searchFilterLabel.facility')"
                  class="md:hidden"
                  :required="false"
                  :options="facilitiesList"
                />

                <Input
                  v-model="searchOrdersModel.orderId"
                  label="Draft ID"
                  :required="false"
                />

                <Input
                  v-model="searchOrdersModel.clientReference"
                  :label="$t('orders.searchFilterLabel.clientReferenceNumber')"
                  :required="false"
                />

                <Input
                  v-model="searchOrdersModel.firstName"
                  :label="$t('orders.searchFilterLabel.customerFirstName')"
                  :required="false"
                />

                <Input
                  v-model="searchOrdersModel.lastName"
                  :label="$t('orders.searchFilterLabel.customerLastName')"
                  :required="false"
                />

                <Input
                  v-model="searchOrdersModel.email"
                  :label="$t('orders.searchFilterLabel.customerEmail')"
                  :required="false"
                />

                <Select
                  v-model="searchOrdersModel.importStatus"
                  label="Import Status"
                  class="hidden md:block"
                  :required="false"
                  :options="importStatusList"
                  :teleport="false"
                />

                <Select
                  v-model="searchOrdersModel.importStatus"
                  label="Import Status"
                  class="md:hidden"
                  :required="false"
                  :options="importStatusList"
                />

              </div>

            </section>

            <!-- Filter By Other Params -->

            <section>
              <div class="h-12 sticky top-0 z-1 flex items-center md:col-span-2 bg-layer-01 px-4 md:px-2 border-b md:border-b-0 border-border-subtle-00">
                <p class="text-xs text-core uppercase">
                  Filter By Other Parameters
                </p>
              </div>
              <div class="grid md:grid-cols-2 md:gap-4 md:pb-4">

                <Select
                  v-model="draftOrderBooleanParamsTempModel.hasMasterRecord"
                  v-model:boolean-model="searchOrdersModel.hasMasterRecord"
                  label="Master Record"
                  :options="[
                    {
                      id: 1,
                      name: 'Has Master Record',
                      mapToBoolean: 'true',
                    },
                    {
                      id: 2,
                      name: 'No Master Record',
                      mapToBoolean: 'false',
                    },
                  ]"
                  :required="false"
                />

                <Select
                  v-model="draftOrderBooleanParamsTempModel.hasErrors"
                  v-model:boolean-model="searchOrdersModel.hasErrors"
                  label="Errors"
                  :options="[
                    {
                      id: 1,
                      name: 'Has Errors',
                      mapToBoolean: 'true',
                    },
                    {
                      id: 2,
                      name: 'No errors',
                      mapToBoolean: 'false',
                    },
                  ]"
                  :required="false"
                />

                <Select
                  v-model="draftOrderBooleanParamsTempModel.isBusinessOrder"
                  v-model:boolean-model="searchOrdersModel.isBusinessOrder"
                  label="Order Type"
                  class="md:col-span-2"
                  :options="[
                    {
                      id: 1,
                      name: 'Business',
                      mapToBoolean: 'true',
                    },
                    {
                      id: 2,
                      name: 'Consumer',
                      mapToBoolean: 'false',
                    },
                  ]"
                  :required="false"
                />

              </div>
            </section>

          </form>
        </div>

        <!-- Search Buttons -->
        <div class="shrink-0 w-full h-12 sticky top-0 z-1 grid grid-cols-2 bg-layer-01 border-t border-border-subtle-00">

          <Button
            size="auto"
            mode="secondary"
            class="px-4"
            @click="openFilters = false"
          >
            {{ $t('global.button.cancel') }}
          </Button>

          <Button
            size="auto"
            class="px-4"
            @click="searchOrders"
          >
            Search
          </Button>

        </div>

      </div>

    </Sidebar>

    <Sidebar
      :open="['Draft Order Details', 'Create Order', 'Ref Create Order', 'Draft Order Products'].includes(String($route.name))"
      :fit-content="false"
    >

      <div class="w-full h-full">
        <RouterView return-to="Draft Orders" />
      </div>

    </Sidebar>

    <Sidebar
      :open="['Upload Bulk Orders'].includes(String($route.name))"
      :dim="true"
      @close="closeBulkUploadDrawer"
    >

      <div class="w-full h-full">
        <RouterView
          drawer-name="Orders"
          :download-example-csv="downloadExampleCsv"
          :download-example-excel="downloadExampleExcel"
          :download-instructions-pdf="downloadInstructionsPdf"
          :bulk-upload-items="uploadOrders"
          @close="closeBulkUploadDrawer"
        />
      </div>

    </Sidebar>

  </div>

</template>
