<script setup lang="ts">

import Table from '@lib/components/table/Table.vue'

import type { PackageShipment } from '@/modules/orders/types'

defineProps<{
  pending?:  boolean
  shipments: PackageShipment[]
}>()

function schema(): TableSchema<PackageShipment> {
  return [
    {
      key:    'shippedDate',
      label:  'Shipped Date',
      format: 'date'
    },
    {
      key:   'carrier',
      label: 'Carrier'
    },
    {
      key:   'serviceLevel',
      label: 'Carrier Service Level'
    },
    {
      key:   'trackingNumber',
      label: 'Tracking No'
    },
    {
      key:       'weight',
      label:     'Weight',
      format:    'custom',
      transform: ( value ) => {
        return `${value} lbs`
      }
    },
    {
      key:       'dimWeight',
      label:     'Dim Weight',
      format:    'custom',
      transform: ( value ) => {
        return `${value} lbs`
      }
    },
    {
      key:    'rate',
      label:  'Rate',
      format: 'currency'
    }
  ]
}

</script>

<template>

  <div class="w-full grid overflow-hidden shadow-custom">

    <Table
      icon="shipped"
      name="Package Shipments"
      :flex="true"
      :schema="schema"
      :pending="pending"
      :records="shipments"
      record-map-key="trackingNumber"
    />

  </div>

</template>
