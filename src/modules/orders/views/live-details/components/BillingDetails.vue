<script setup lang="ts">

import { paymentTypesList } from '@/modules/orders/store'
import { handleDetailsError } from '@/store'

import DetailsCard from '@/components/DetailsCard.vue'

import type { ImportError } from '@/types'
import type { OrderBilling } from '@/modules/orders/types'

defineProps<{
  billing:        OrderBilling
  importErrors?:  ImportError<OrderBilling>[]
  updatePending?: boolean
}>()

</script>

<template>

  <div class="grid grid-cols-2 gap-px bg-core-30 border-b border-core-30">

    <DetailsCard
      label="Name"
      class="col-span-2"
      :content="!billing.firstName && !billing.lastName ? '/' : `${billing.firstName ? billing.firstName : '/'} ${billing.lastName ? billing.lastName : '/'}`"
      :error="handleDetailsError('firstName', importErrors, 'billing')"
      :pending="updatePending"
    />

    <DetailsCard
      label="Address Line 1"
      class="col-span-2"
      :content="billing.address1 ? billing.address1 : '/'"
      :error="handleDetailsError('address1', importErrors, 'billing')"
      :pending="updatePending"
    />

    <DetailsCard
      label="Address Line 2"
      class="col-span-2"
      :content="billing.address2 ? billing.address2 : '/'"
      :error="handleDetailsError('address2', importErrors, 'billing')"
      :pending="updatePending"
    />

    <DetailsCard
      label="City"
      :content="billing.city ? billing.city : '/'"
      :error="handleDetailsError('city', importErrors, 'billing')"
      :pending="updatePending"
    />

    <DetailsCard
      label="State"
      :content="billing.state ? billing.state : '/'"
      :error="handleDetailsError('state', importErrors, 'billing')"
      :pending="updatePending"
    />

    <DetailsCard
      label="Postal Code"
      :content="billing.zip ? billing.zip : '/'"
      :error="handleDetailsError('zip', importErrors, 'billing')"
      :pending="updatePending"
    />

    <DetailsCard
      label="Country"
      :content="billing.country ? billing.country : '/'"
      :error="handleDetailsError('country', importErrors, 'billing')"
      :pending="updatePending"
    />

    <DetailsCard
      label="Email"
      class="col-span-2"
      :content="billing.email ? billing.email : '/'"
      :error="handleDetailsError('email', importErrors, 'billing')"
      :pending="updatePending"
    />

    <DetailsCard
      label="Phone"
      class="col-span-2"
      :content="billing.phone ? billing.phone : '/'"
      :error="handleDetailsError('phone', importErrors, 'billing')"
      :pending="updatePending"
    />

    <DetailsCard
      label="Payment Type"
      class="col-span-2"
      :content="paymentTypesList.find((type) => billing?.paymentType === type.id)?.name || '/'"
      :error="handleDetailsError('paymentType', importErrors, 'billing')"
      :pending="updatePending"
    />

    <DetailsCard
      label="Company Name"
      class="col-span-2"
      :content="billing?.companyName || '/'"
      :error="handleDetailsError('companyName', importErrors, 'billing')"
      :pending="updatePending"
    />

  </div>

</template>
