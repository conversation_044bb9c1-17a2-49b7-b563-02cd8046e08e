<script setup lang="ts">

import { handleDetailsError, shippingMethodsList } from '@/store'

import DetailsCard from '@/components/DetailsCard.vue'

import type { ImportError } from '@/types'
import type { OrderShipping } from '@/modules/orders/types'

defineProps<{
  shipping:       OrderShipping
  importErrors?:  ImportError<OrderShipping>[]
  updatePending?: boolean
}>()

</script>

<template>

  <div class="grid grid-cols-2 gap-px bg-core-30 border-b border-core-30">

    <DetailsCard
      label="Name"
      class="col-span-2"
      :content="!shipping.firstName && !shipping.lastName ? '/' : `${shipping.firstName ? shipping.firstName : '/'} ${shipping.lastName ? shipping.lastName : '/'}`"
      :error="handleDetailsError(['firstName', 'lastName'], importErrors, 'shipping')"
      :pending="updatePending"
    />

    <DetailsCard
      label="Shipping Method"
      class="col-span-2"
      :content="shipping?.method ? shipping?.method : shippingMethodsList.find(m => m?.id === shipping?.shipType)?.name || '/'"
      :error="handleDetailsError('shippingMethod', importErrors)"
      :pending="updatePending"
    />

    <DetailsCard
      label="Address Line 1"
      class="col-span-2"
      :content="shipping.address1 ? shipping.address1 : '/'"
      :error="handleDetailsError('address1', importErrors, 'shipping')"
      :pending="updatePending"
    />

    <DetailsCard
      label="Address Line 2"
      class="col-span-2"
      :content="shipping.address2 ? shipping.address2 : '/'"
      :error="handleDetailsError('address2', importErrors, 'shipping')"
      :pending="updatePending"
    />

    <DetailsCard
      label="City"
      :content="shipping.city ? shipping.city : '/'"
      :error="handleDetailsError('city', importErrors, 'shipping')"
      :pending="updatePending"
    />

    <DetailsCard
      label="State"
      :content="shipping.state ? shipping.state : '/'"
      :error="handleDetailsError('state', importErrors, 'shipping')"
      :pending="updatePending"
    />

    <DetailsCard
      label="Postal Code"
      :content="shipping.zip ? shipping.zip : '/'"
      :error="handleDetailsError('zip', importErrors, 'shipping')"
      :pending="updatePending"
    />

    <DetailsCard
      label="Country"
      :content="shipping.country ? shipping.country : '/'"
      :error="handleDetailsError('country', importErrors, 'shipping')"
      :pending="updatePending"
    />

    <DetailsCard
      label="Email"
      class="col-span-2"
      :content="shipping.email ? shipping.email : '/'"
      :error="handleDetailsError('email', importErrors, 'shipping')"
      :pending="updatePending"
    />

    <DetailsCard
      label="Phone"
      :content="shipping.phone ? shipping.phone : '/'"
      class="col-span-2"
      :error="handleDetailsError('phone', importErrors, 'shipping')"
      :pending="updatePending"
    />

    <DetailsCard
      label="Company Name"
      class="col-span-2"
      :content="shipping?.companyName || '/'"
      :error="handleDetailsError('companyName', importErrors, 'shipping')"
      :pending="updatePending"
    />

  </div>

</template>
