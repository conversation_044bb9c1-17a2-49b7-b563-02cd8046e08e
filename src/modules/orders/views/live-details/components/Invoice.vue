<script setup lang="ts">

import { facilitiesLookup } from '@/store'
import { computed, ref, watch } from 'vue'
import { formatCurrency, formatDate } from '@lib/scripts/utils'

import html2pdf from '@/assets/scripts/html2pdf'

import type { EditableOrderLineItem, OrderDetails } from '@/modules/orders/types'

const props = defineProps<{
  order: OrderDetails
  items: EditableOrderLineItem[]
}>()

const emits = defineEmits<{
  closeInvoice: []
}>()

const invoice     = ref<HTMLElement | null>( null )
const shipperInfo = computed(() => facilitiesLookup.value.find( f => f.code === props.order.facilityCode ))

watch( invoice, async ( n ) => {

  if ( n ) {

    await html2pdf( n, {
      margin:      [ 0.4, 1 ],
      filename:    `Invoice-${props.order.id}.pdf`,
      html2canvas: { scale: 4 },
      jsPDF:       { unit: 'cm', format: 'A4', orientation: 'portrait' },
    })

    emits( 'closeInvoice' )

  }

})

</script>

<template>

  <div class="w-full absolute top-0 -left-full z-2000 overflow-y-auto">

    <div ref="invoice" class="commercial-invoice w-full min-h-[27.94cm] grid grid-rows-[max-content_1fr_max-content] bg-white">

      <!-- Header -->

      <div>

        <!-- Title -->

        <div class="w-full h-12 grid place-content-center border-t-4 border-b-4 border-black">

          <p class="text-xl -mt-5 text-center font-semibold">
            Commercial Invoice
          </p>

        </div>

        <!-- Date & Number -->

        <div class="font-semibold w-full h-8 grid grid-cols-4 items-center">

          <p class="-mt-4">
            Date Shipped:
          </p>
          <p class="-mt-4">
            {{ formatDate(order?.shipped, 'YYYY/MM/DD') }}
          </p>
          <p class="-mt-4">
            Invoice:
          </p>
          <p class="-mt-4">
            {{ order.owdReference }}
          </p>

        </div>

        <!-- Separator Block -->

        <div class="w-full h-2 bg-[gray] border border-black" />

        <!-- Shipper / Buyer Info -->

        <div class="w-full h-auto py-1 grid grid-cols-3">

          <div class="text-sm">

            <p class="font-semibold">
              Shipper Name/Address
            </p>

            <p>TAX ID: <span>46-0458580</span></p>
            <p>One World Direct</p>
            <p>{{ shipperInfo?.address }}</p>
            <p>{{ shipperInfo?.city }}, {{ shipperInfo?.state }} {{ shipperInfo?.zip }}</p>
            <p>USA</p>
            <br>
            <p>Phone: <span>************</span></p>
            <p>Reason for export: <span>Sale</span></p>

          </div>

          <div class="text-sm">

            <p class="font-semibold">
              Buyer Name and Address
            </p>

            <p>{{ order?.billing?.firstName }} {{ order?.billing?.lastName }}</p>
            <p>{{ order?.billing?.address1 }}</p>
            <p v-if="order?.billing?.address2">
              {{ order?.billing?.address2 }}
            </p>
            <p>{{ order?.billing?.city }}, {{ order?.billing?.state }} {{ order?.billing?.zip }}</p>
            <p>{{ order?.billing?.country }}</p>
            <p v-if="order?.billing?.phone">
              Phone: <span>{{ order?.billing?.phone }}</span>
            </p>
            <br>
            <p>Billing Ref: <span>{{ order?.clientReference }}</span></p>
            <br>
            <p v-if="order?.trackingNumber">
              BOL/Waybill: <span>{{ order?.trackingNumber }}</span>
            </p>
            <p v-if="order?.billing?.email">
              Email: <span>{{ order?.billing?.email }}</span>
            </p>

          </div>

          <div class="text-sm">

            <p class="font-semibold">
              Consignee Name and Address
            </p>

            <p>{{ order?.shipping?.firstName }} {{ order?.shipping?.lastName }}</p>
            <p v-if="order?.shipping?.companyName">
              {{ order?.shipping?.companyName }}
            </p>
            <p>{{ order?.shipping?.address1 }}</p>
            <p v-if="order?.shipping?.address2">
              {{ order?.shipping?.address2 }}
            </p>
            <p>{{ order?.shipping?.city }}, {{ order?.shipping?.state }} {{ order?.shipping?.zip }}</p>
            <p>{{ order?.shipping?.country }}</p>
            <p v-if="order.shipping?.phone">
              Phone: <span>{{ order.shipping?.phone }}</span>
            </p>
            <p v-if="order?.shipping?.email">
              Email: <span>{{ order?.shipping?.email }}</span>
            </p>

          </div>

        </div>

      </div>

      <!-- Items -->

      <div class="mt-14">

        <div class="text-sm font-semibold pb-2 grid grid-cols-6 border-b-4 border-black">

          <p>Units</p>

          <p class="col-span-3">
            Complete Description of Goods
          </p>

          <p class="text-right">
            Unit Value
          </p>

          <p class="text-right">
            Total
          </p>

        </div>

        <div v-for="item in items" :key="item.id" class="text-sm pb-3 grid grid-cols-6 items-center border-b-4 border-black">

          <p>{{ item.shippedQuantity }}</p>

          <p class="text-xs col-span-3">
            <span>{{ item?.originCountry }} /</span> {{ item?.customsDescription || item?.description || item?.sku }}
          </p>

          <p class="text-right">
            {{ formatCurrency(item.cost) }}
          </p>

          <p class="text-right">
            {{ formatCurrency(item.shippedQuantity * item.cost) }}
          </p>

        </div>

      </div>

      <!-- Footer -->

      <div>

        <div class="grid grid-cols-6 items-end">

          <div class="col-span-4 pr-10">

            <p class="text-xs font-semibold">
              These commodities, technology or software were exported from the United States of
              America in accordance with the Export Administration Regulations. Diversion
              contrary to U.S. law prohibited.
            </p>

            <div class="pl-10 grid grid-cols-[max-content_1fr] items-end gap-x-2">

              <p class="text-sm">
                x
              </p>

              <div class="pt-8 pl-12 h-12 grid place-content-center border-b-2 border-black">
                <svg
                  height="55.364055pt"
                  preserveAspectRatio="xMidYMid meet"
                  viewBox="0 0 180.014641 55.364055"
                  width="180.014641pt"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g transform="matrix(.095238 0 0 -.095238 -10.718792 59.780137)">
                    <path d="m343 623c-4-4-7-15-7-25 0-9-17-39-38-66s-36-52-33-55 23 7 45 21c22 15 48 27 58 27 50 0-34-138-150-247-40-38-75-67-79-64-8 8 38 92 69 125 14 15 24 31 21 34-11 11-74-62-98-114-23-48-25-80-5-80 11-1 111 78 121 94 5 8 11 8 21 0 17-14 34-5 100 57 23 21 42 35 42 31 0-3-22-41-49-84-70-112-55-120 73-38 40 25 74 44 76 42s-16-27-39-56c-86-107-111-160-82-178 12-8 128 105 162 155 12 19 17 40 13 59-8 39 25 75 50 55 8-7 25-10 36-6 16 4 22 2 22-10 0-20 35-39 57-31 23 9 21 25-4 25-13 0-21 8-23 24-4 23-19 27-54 13-13-5-18-3-18 9 0 9 35 42 79 74 83 62 94 84 62 127-22 29-41 14-109-87-68-100-105-141-122-135-6 2-48-19-94-47-45-28-84-49-87-47-2 2 13 29 34 59s40 67 44 82c3 15 10 35 15 44 34 54 52 96 46 106-9 14-46-30-47-54-1-25-24-49-64-66l-38-16 30 59c17 33 30 67 30 77 1 23-25 42-45 34-21-7-21-3-5 33 12 26 0 56-16 40zm420-134c-6-16-101-85-101-73 0 4 19 33 43 65 37 49 45 55 53 40 6-10 8-25 5-32zm-391-129c-5-5-25-23-45-40-19-17-32-24-28-15 5 14 66 63 78 63 3 0 1-4-5-8zm174-112c0-17-66-103-109-142-48-44-31-2 31 80 58 75 78 91 78 62z" />
                    <path d="m895 532c-12-24-35-93-50-154-25-101-31-112-70-146-59-51-103-114-103-150 0-61 53-19 134 105 22 33 44 63 50 67 19 14 29 9 15-8-19-22-3-33 19-12 16 17 16 19-6 34-21 16-22 19-8 46 9 17 25 65 37 107 36 128 24 197-18 111zm-3-106c-7-27-15-47-17-44-2 2 1 26 8 53s14 47 17 44c2-2-2-26-8-53zm-110-234c-54-84-111-139-78-75 12 24 83 104 92 104 2 0-4-13-14-29z" /><path d="m1202 491c-103-55-144-83-202-138-45-43-57-66-29-55 8 3 28-1 45-10 39-20 93-19 241 6 218 36 402 52 596 54 133 1 174 4 137 9-142 23-429 6-735-42-158-24-182-25-223-10l-29 12 54 50c30 27 97 72 150 99 52 27 93 54 89 60-7 12 4 16-94-35z" />
                  </g>
                </svg>
              </div>

            </div>

          </div>

          <div class="text-sm col-span-2 grid grid-cols-2">

            <p class="text-right font-semibold">
              Subtotal Value
            </p>

            <p class="text-right font-semibold">
              {{ formatCurrency(order.subtotal) }}
            </p>

            <p class="text-right">
              Discount
            </p>

            <p class="text-right">
              {{ formatCurrency(order.discount) }}
            </p>

            <p class="text-right">
              Total Value
            </p>

            <p class="text-right">
              {{ formatCurrency(order.total) }}
            </p>

            <p class="text-right font-semibold mt-2">
              Total Weight (Lbs):
            </p>

            <p class="text-right font-semibold mt-2">
              {{ order.weightLbs }}
            </p>

            <p class="text-right font-semibold mt-2">
              Total Packages:
            </p>

            <p class="text-right font-semibold mt-2">
              {{ order.packages }}
            </p>

          </div>

        </div>

        <div class="h-10 pl-10 flex items-center">

          <p class="text-sm">
            Date: 2021/06/30
          </p>

        </div>

      </div>

    </div>

  </div>

</template>
