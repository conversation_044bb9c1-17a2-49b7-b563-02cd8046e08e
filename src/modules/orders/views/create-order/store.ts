import { Flow } from '@/modules/orders/views/create-order/types'
import { validateModel } from '@lib/scripts/inputValidation'
import { computed, reactive, ref, watch } from 'vue'
import { checkValue, convertObjectToValidatable } from '@lib/scripts/utils'

import type { InventoryPanelProduct } from '@/modules/inventory/types'
import type { CreateOrderBillingInfo, CreateOrderDetailsInfo, CreateOrderLineItem, CreateOrderShippingInfo, ShippingGroupName } from '@/modules/orders/types'

export const orderStatuses: DropListOption[] = [
  {
    id:   1,
    name: 'Draft Order',
  },
  {
    id:   0,
    name: 'Live Order'
  }
]

export const ddp             = ref<{ value: 1 | 0, valid: boolean }>({ value: 0, valid: true })
export const orderStatus     = ref<{ value: 1 | 0, valid: boolean }>({ value: 1, valid: true })
export const isDraftOrder    = ref( true )
export const isBusinessOrder = ref<{ value: 1 | 0, valid: boolean }>({ value: 0, valid: true })

// ---- [ CREATE ORDER FLOW ]

export const storedStep = ref<Flow>( Flow.CLIENT )
export const activeStep = ref<Flow>( Flow.CLIENT )

// ---- [ CREATE ORDER MODELS ]

const detailsDefaultModel: CreateOrderDetailsInfo = {
  isDdp:           false,
  source:          'client portal',
  isGift:          false,
  groupName:       null,
  paymentType:     'CLIENT',
  giftMessage:     null,
  facilityRule:    null,
  backorderRule:   null,
  holdForRelease:  null,
  clientReference: null,
  isBusinessOrder: false
}

const billingDefaultModel: CreateOrderBillingInfo = {
  zip:         null,
  city:        null,
  email:       null,
  phone:       null,
  state:       null,
  country:     null,
  poNumber:    null,
  lastName:    null,
  address1:    null,
  address2:    null,
  firstName:   null,
  paymentType: null,
  companyName: null
}

const shippingDefaultModel: CreateOrderShippingInfo = {
  zip:         null,
  city:        null,
  email:       null,
  phone:       null,
  state:       null,
  country:     null,
  shipCost:    null,
  shipType:    null,
  lastName:    null,
  address1:    null,
  address2:    null,
  firstName:   null,
  companyName: null
}

const omitDetailsKeys = computed<( keyof CreateOrderDetailsInfo )[]>(() => [
  'isDdp',
  'source',
  'isGift',
  'paymentType',
  'giftMessage',
  'backorderRule',
  'holdForRelease',
  'isBusinessOrder',
  ...( isDraftOrder.value
    ? []
    : [ 'clientReference' ] as ( keyof CreateOrderDetailsInfo )[] )
] )

const omitBillingKeys: ( keyof CreateOrderBillingInfo )[]   = [ 'address2', 'companyName', 'poNumber', 'paymentType', 'email', 'phone' ]
const omitShippingKeys: ( keyof CreateOrderShippingInfo )[] = [ 'address2', 'companyName', 'shipType', 'shipCost', 'email', 'phone' ]

export const details   = ref<Validatable<CreateOrderDetailsInfo>>( convertObjectToValidatable( detailsDefaultModel, null, omitDetailsKeys.value ))
export const billing   = ref<Validatable<CreateOrderBillingInfo>>( convertObjectToValidatable( billingDefaultModel, null, omitBillingKeys ))
export const shipping  = ref<Validatable<CreateOrderShippingInfo>>( convertObjectToValidatable( shippingDefaultModel, null, omitShippingKeys ))

export const orderStepValid    = computed(() => validateModel( details.value ))
export const clientStepValid   = computed(() => validateModel( billing.value ) && validateModel( shipping.value ))
export const productsStepValid = computed(() => selectedItems.value.length > 0 )
export const shippingStepValid = computed(() => checkValue( shipping.value.shipType.value ))

export const isSameInfo  = ref<boolean>( true ) // ----- Boolean to determine if shipping info is the same as billing info.
export const isGiftOrder = ref<boolean>( false ) // ---- Boolean to determine if order is a gift.
export const hasFacility = ref<boolean>( false ) // ---- Boolean to determine if there is a default facility for the client.

// ---- [ CREATE ORDER INVENTORY ]

export const selectedItems      = ref<CreateOrderLineItem[]>( [] )
export const inventoryItems     = ref<InventoryPanelProduct[]>( [] )
export const selectedFacility   = computed(() => details.value.facilityRule.value )

// Boolean flag indicating order creation progress

export const orderCreationPending = ref( false )

/**
 * Map the available quantity of each item based on the selected facility.
 */

export function mapAvailableByFacility() {

  if ( !selectedFacility.value )
    return

  inventoryItems.value = inventoryItems.value.map( item => ({
    ...item,
    locked: false,
    availableQuantity:
      item?.availableByFacility
        ? item.availableByFacility.find( f => f.facilityCode === selectedFacility.value )?.unitsAvailable ?? 0
        : 0
  }))

  selectedItems.value.forEach(( item ) => {

    item.available = item?.availableByFacility
      ? item.availableByFacility.find( f => f.facilityCode === selectedFacility.value )?.unitsAvailable ?? 0
      : 0

    if ( item.requested > item.available )
      item.backordered = item.requested - item.available

    else item.backordered = 0

  })

}

watch( selectedFacility, mapAvailableByFacility )

// ---- [ CREATE ORDER SHIPPING GROUPS ]

export const shippingGroups = reactive<{ name: ShippingGroupName, options: DropListOption[] }[]>( [
  { name: 'Flat Rate', options: [] },
  { name: 'UPS', options: [] },
  { name: 'FedEx', options: [] },
  { name: 'DHL', options: [] },
  { name: 'USPS', options: [] },
  { name: 'Other', options: [] }
] )

export const selectedShippingGroup = ref<ShippingGroupName>( 'Flat Rate' )

// ---- [ CREATE ORDER RESET ]

export function resetCreateOrderState() {

  // Reset the step value to 'clientDetails'

  activeStep.value = Flow.CLIENT

  // Reset the details, billing, and shipping values using default models

  details.value = convertObjectToValidatable( detailsDefaultModel, null, omitDetailsKeys.value )
  billing.value = convertObjectToValidatable( billingDefaultModel, null, omitBillingKeys )
  shipping.value = convertObjectToValidatable( shippingDefaultModel, null, omitShippingKeys )

  // Reset the isSameInfo and isGiftOrder values to false

  isSameInfo.value = true
  isGiftOrder.value = false

  // Reset the selectedItems and inventoryItems values to empty arrays

  selectedItems.value = []
  inventoryItems.value = []

  // Remove sorted shipping groups

  shippingGroups.forEach( group => group.options = [] )

  // Reset the selectedShippingGroup value to 'UPS'

  selectedShippingGroup.value = 'Flat Rate'

  // Reset the has Facility flag

  hasFacility.value = false

  // Reset draft order indicators

  orderStatus.value.value = 1
  isDraftOrder.value = true

}
