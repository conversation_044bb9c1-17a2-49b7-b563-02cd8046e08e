<script setup lang="ts">

import { guard } from '@/plugins/guard'
import { confirm } from '@lib/store/confirm'
import { pageSize } from '@lib/store/table'
import { validateModel } from '@lib/scripts/inputValidation'
import { useRoute, useRouter } from 'vue-router'
import { productDetailsOptions } from '@/store'
import { computed, reactive, ref } from 'vue'
import { setAlertOptions, setNotificationOptions } from '@lib/store/snackbar'
import { checkValue, convertObjectToValidatable, formatCurrency, sanitizeQueryParams, viewSetup } from '@lib/scripts/utils'

import {
  createDraftOrderLineItem,
  deleteDraftOrderLineItem,
  getDraftOrder,
  getDraftOrderLineItemBySku,
  getDraftOrderLineItems,
  updateDraftOrder,
  updateDraftOrderLineItem
} from '@/modules/orders/store'

import Icon from '@lib/components/blocks/Icon.vue'
import Table from '@lib/components/table/Table.vue'
import Input from '@lib/components/inputs/Input.vue'
import Toggle from '@lib/components/inputs/Toggle.vue'
import Select from '@lib/components/inputs/Select.vue'
import Button from '@lib/components/button/Button.vue'
import Sidebar from '@lib/components/blocks/Sidebar.vue'
import Textbox from '@lib/components/inputs/Textbox.vue'

import type { BatchOption } from '@lib/types/tableTypes'
import type { InventoryPanelProduct } from '@/modules/inventory/types'
import type { ImportError, ResolveSchema } from '@/types'
import type { DraftOrder, DraftOrderLineItem, EditableDraftOrderLineItem, EditableLineItem } from '@/modules/orders/types'

const props = defineProps<{
  order:        DraftOrder | null
  editable?:    boolean
  itemsErrors?: ImportError<DraftOrderLineItem>[]
}>()

const emits = defineEmits<{
  ( eventName: 'updateDraftOrder', value: DraftOrder ): void
  ( eventName: 'globalActionsUpdate', value: { total: number, hasOrderLineItems?: boolean }): void
}>()

function setRecordStatus( key: keyof EditableDraftOrderLineItem, record: EditableDraftOrderLineItem ): TableRecordStatus {

  const fieldError = record?.importErrors?.find( e => e.propertyName === key )

  if ( !fieldError )
    return null

  return {
    type:    'error',
    message: fieldError.errorMsg
  }

}

function schema( lineItem: EditableDraftOrderLineItem ): TableSchema<EditableDraftOrderLineItem> {
  return [
    {
      key:    'sku',
      label:  'SKU',
      status: setRecordStatus( 'sku', lineItem )
    },
    {
      key:    'description',
      label:  'Description',
      status: setRecordStatus( 'description', lineItem )
    },
    {
      key:    'requestedQuantity',
      label:  'Count',
      status: setRecordStatus( 'requestedQuantity', lineItem )
    },
    {
      key:    'cost',
      label:  'Cost',
      format: 'currency',
      status: setRecordStatus( 'cost', lineItem )
    },
    {
      key:       null,
      label:     'Total',
      value:     lineItem?.cost * lineItem?.requestedQuantity,
      format:    'custom',
      align:     'right',
      transform: ( value ) => {
        return formatCurrency( value )
      }
    }
  ]
}

const items             = ref<Tablify<EditableDraftOrderLineItem>[]>( [] )
const route             = useRoute()
const total             = ref<number>( 0 )
const router            = useRouter()
const params            = reactive<BaseParams>({ page: 1, pageSize: pageSize.value, ...sanitizeQueryParams( route.query ) })
const maxPages          = ref<number>( 0 )
const itemsTotal        = computed(() => items.value.reduce(( total, item ) => total + ( item.cost * item.requestedQuantity ), 0 ))
const editedItem        = ref<Validatable<EditableLineItem>>( null )
const itemsErrors       = defineModel<ImportError<DraftOrderLineItem>[]>( 'itemsErrors', { default: [] })
const openEditForm      = ref( false )
const shouldUpdate      = ref( false )
const updatePending     = ref( false )
const canConfirmItems   = computed(() => shouldUpdate.value && selectedProducts.value.every( item => checkValue( item.quantity ) && checkValue( item.price )))
const selectedProducts  = ref<InventoryPanelProduct[]>( [] )
const addedProductsMap  = ref<string[]>( [] )
const canSaveEditedItem = computed(() => validateModel( editedItem.value ))

/**
 * Opens the edit item sidebar with the item's details.
 * @param {EditableOrderLineItem} item - The item to edit.
 */

function openEditItemSidebar( item: EditableDraftOrderLineItem ) {

  openEditForm.value = true

  editedItem.value = convertObjectToValidatable({
    id:          item.id,
    sku:         item.sku,
    price:       item.cost,
    quantity:    item.requestedQuantity,
    description: item.description
  }, null, [ 'sku', 'id' ] )

}

/**
 * Closes the edit item sidebar.
 * resets the edited item and the errors schema.
 */

function closeEditItemSidebar() {

  editedItem.value = null
  resolvedId.value = null
  errorsSchema.value = []

  openEditForm.value = false

}

/**
 * Opens the inventory sidebar.
 */

function openInventorySidebar() {
  router.push({ name: 'Draft Order Products' })
}

/**
 * Updates the order line items with the selected products and navigates back to the order details page.
 */

async function updateInventorySidebar() {

  const { error } = await updateOrderLineItems()

  if ( !error ) {

    shouldUpdate.value = false
    selectedProducts.value = []

    emits(
      'globalActionsUpdate',
      {
        total:             itemsTotal.value,
        hasOrderLineItems: items.value.length > 0
      }
    )

    router.push({ name: 'Draft Order Details', params: { id: router.currentRoute.value.params.id } })

  }

}

/**
 * Cancels the inventory sidebar and navigates back to the order details page.
 * Resets the selected products.
 */

function cancelInventorySidebar( block = true ) {

  blockPageUpdate( block )

  shouldUpdate.value = false
  selectedProducts.value = []
  addedProductsMap.value = []

  router.push({ name: 'Draft Order Details', params: { id: router.currentRoute.value.params.id } })

}

/**
 * Retrieves the order line items.
 * @param {BaseParams} viewParams - The view parameters.
 */

async function getOrderProducts( viewParams: BaseParams ) {

  if ( !props.order?.id )
    return

  const { error, payload } = await getDraftOrderLineItems( props.order.id, viewParams )

  if ( !error ) {

    items.value = payload?.stageOrderLineItems ?? []
    total.value = payload?.totalRows ?? 0
    maxPages.value = payload?.totalPages ?? 0
    itemsErrors.value = payload?.stageOrderLineItems?.map( i => i.importErrors )?.filter( i => !!i )?.flat() ?? []

    items.value.forEach(( item ) => {

      if ( item?.importErrorCount ) {

        const statusMessage
        = item?.importErrors?.some( e => e.propertyName === 'record' )
          ? item?.importErrors?.find( e => e.propertyName === 'record' )?.errorMsg
          : `This product has ${item.importErrorCount} import error${item?.importErrorCount > 1 ? 's' : ''}.`

        const status: TableRecordStatus = {
          type:    'error',
          count:   item.importErrorCount,
          message: statusMessage
        }

        item.tableRecordStatus = status

      }

    })

    emits(
      'globalActionsUpdate',
      {
        total:             itemsTotal.value,
        hasOrderLineItems: items.value.length > 0
      }
    )

  }

}

async function refetchDraftOrder() {

  if ( props.order.importStatus === 'Failed' ) {

    const { error, payload } = await updateDraftOrder( props.order.id, { ...props.order, importStatus: 'Draft' })

    if ( !error )
      emits( 'updateDraftOrder', payload )

  }

  else {

    const { error, payload } = await getDraftOrder( props.order.id )

    if ( !error )
      emits( 'updateDraftOrder', payload )

  }

}

/**
 * Updates the order line items with the selected products.
 */

async function updateOrderLineItems() {

  updatePending.value = true

  const requests = []

  selectedProducts.value.forEach(( product ) => {

    const mappedItem = {
      sku:               product.sku,
      cost:              product.price,
      description:       product.title,
      declaredValue:     product.price,
      requestedQuantity: product.quantity,
    }

    if ( !addedProductsMap.value.includes( product.sku ))
      requests.push( updateDraftOrderLineItem( props.order.id, product.id, mappedItem ))

    else
      requests.push( createDraftOrderLineItem( props.order.id, mappedItem ))

  })

  const responses = await Promise.all( requests )

  const error = responses.some( r => r.error )

  if ( !error ) {
    setNotificationOptions({ message: 'All selected products have been successfully added or updated in the order.' })
    cancelInventorySidebar( false )
    refetchDraftOrder()

  }

  updatePending.value = false

  return {
    error,
    payload: null
  }

}

/**
 * Updates the line item with the edited details.
 */

async function updateLineItem() {

  updatePending.value = true

  const data: Partial<DraftOrderLineItem> = {
    sku:               editedItem.value.sku.value,
    cost:              editedItem.value.price.value,
    description:       editedItem.value.description.value,
    requestedQuantity: editedItem.value.quantity.value
  }

  const { error } = await updateDraftOrderLineItem( props.order.id, editedItem.value.id.value, data )

  if ( !error ) {
    await updateView()
    closeEditItemSidebar()
  }

  updatePending.value = false

}

/**
 * Deletes a line item from the order.
 * @param {EditableOrderLineItem} lineItem - The line item to delete.
 */

async function deleteItem( lineItem: EditableDraftOrderLineItem ) {

  if ( !props.order.masterRecordId && ( maxPages.value === 1 && items.value.length === 1 )) {

    setAlertOptions({
      message:  'Your order must contain at least one item.',
      details:  'Ensure your order isn\'t empty by adding a new product before removing this one.',
      severity: 'warning'
    })

    return

  }

  const { error } = await deleteDraftOrderLineItem( props.order.id, lineItem.id )

  if ( !error ) {

    await updateView()

    emits(
      'globalActionsUpdate',
      {
        total:             itemsTotal.value,
        hasOrderLineItems: items.value.length > 0
      }
    )

    setNotificationOptions({ message: 'Order product is deleted successfully.' })

    refetchDraftOrder()

  }

}

/**
 * Deletes selected line items from the order.
 * @param {Partial<InventoryPanelProduct>[]} selectedItems - The selected line items to delete.
 */

async function deleteSelectedLineItems( selectedItems: Partial<InventoryPanelProduct>[] ) {

  if ( !props.order.masterRecordId && ( selectedItems.length === items.value.length && maxPages.value === 1 )) {

    setAlertOptions({
      message:  'Your order must contain at least one item.',
      details:  'Ensure your order isn\'t empty by adding a new product before removing this one.',
      severity: 'warning'
    })

    return

  }

  const requests = []

  selectedItems.forEach(( lineItem ) => {
    requests.push( deleteDraftOrderLineItem( props.order.id, lineItem.id ))
  })

  const response = await Promise.all( requests )

  const error = response.some( r => r.error )

  if ( !error ) {

    if ( selectedItems.length === items.value.length && params.page > 1 )
      params.page = params.page - 1

    setNotificationOptions({ message: 'All selected products have been successfully deleted from the order.' })

    await updateView()

    emits(
      'globalActionsUpdate',
      {
        total:             itemsTotal.value,
        hasOrderLineItems: items.value.length > 0
      }
    )

    refetchDraftOrder()

  }

}

/**
 *
 * Checks if the product already exists in the line items,
 * and if it does it retrieves it as a line item and
 * it maps it as an inventory product and return it,
 * else it adds the product to the inventory selected products.
 *
 * @param {InventoryPanelProduct} product - The product to add.
 * @returns {InventoryPanelProduct} - The product to add.
 *
 */

async function addProduct( product: InventoryPanelProduct ): Promise<InventoryPanelProduct> {

  let lineItemData = null

  lineItemData = await getDraftOrderLineItemBySku( props.order.id, product.sku )

  const lineItem = lineItemData.payload

  shouldUpdate.value = true

  if ( !lineItem ) {
    addedProductsMap.value.push( product.sku )
    return { ...product }
  }

  return {
    id:                  lineItem.id,
    sku:                 lineItem.sku,
    price:               lineItem.cost,
    title:               lineItem.description,
    supplier:            null,
    quantity:            lineItem.requestedQuantity,
    availableQuantity:   lineItem.requestedQuantity,
    availableByFacility: null,

  } as InventoryPanelProduct

}

/**
 * ---- ERRORS RESOLUTION ----
 */

const resolvedId    = ref<number>( null )
const errorsSchema  = ref<ResolveSchema<any>[]>( [] )

const resolveSchema = computed<ResolveSchema<EditableDraftOrderLineItem>[]>(() => [
  {
    key:      'sku',
    type:     'text',
    label:    'SKU',
    required: true
  },
  {
    key:      'description',
    type:     'textbox',
    label:    'Description',
    required: true
  },
  {
    key:      'requestedQuantity',
    type:     'number',
    label:    'Quantity',
    required: true
  },
  {
    key:      'cost',
    type:     'currency',
    label:    'Cost',
    required: true
  }
] )

const canResolveErrors = computed<boolean>(() => errorsSchema.value.every( e => e.valid ))

function resolveErrors( item: EditableDraftOrderLineItem ) {

  resolvedId.value = item.id

  const errors = item?.importErrors

  errors.forEach(( error ) => {

    const err = error.errorMsg
    const key = error.propertyName.split( '.' ).at( -1 )

    const schema = resolveSchema.value.find( s => s.key === key )
    const orderSchema = schema ? { ...schema } : null

    if ( orderSchema ) {
      orderSchema.error = err
      errorsSchema.value.push( orderSchema )
    }

  })

  openEditForm.value = true

}

async function resolveLineItemErrors( itemId: number ) {

  updatePending.value = true

  const data: Partial<EditableDraftOrderLineItem> = {}

  errorsSchema.value.forEach(( error ) => {
    data[error.key] = error.value
  })

  const { error } = await updateDraftOrderLineItem( props.order.id, itemId, data )

  if ( !error ) {

    await updateView()

    closeEditItemSidebar()
    refetchDraftOrder()

    setNotificationOptions({ message: `Product error${errorsSchema.value.length > 1 ? 's' : ''} ${errorsSchema.value.length > 1 ? 'are' : 'is'} resolved successfully.` })

  }

  updatePending.value = false

}

/**
 * Returns the options for the line item.
 * @param lineItem - The line item.
 * @returns The options for the line item.
 */

function itemOptions( lineItem: EditableDraftOrderLineItem ): DropListOption[] {

  return [
    {
      id:   1,
      name: 'Delete',
      icon: {
        name:  'delete',
        color: '#FF4343',
        size:  'm'
      },
      hidden: !guard( 'Order.Write' ) || !props.editable,
      action: () => confirm({
        header:      'Delete product from Order',
        description: `Are you sure you want to remove <span class="text-main font-medium" >${lineItem.sku}</span>?`,
        action:      async () => await deleteItem( lineItem )
      })
    },
    {
      id:     2,
      name:   'Edit',
      hidden: ( lineItem?.importErrorCount > 0 ) || !guard( 'Order.Write' ) || !props.editable,
      icon:   {
        name: 'edit',
        size: 'm'
      },
      action: () => openEditItemSidebar( lineItem )
    },
    {
      id:     3,
      name:   'Resolve',
      hidden: ( lineItem?.importErrors?.some( e => e.propertyName === 'record' ) || lineItem?.importErrorCount === 0 ) || !guard( 'Order.Write' ) || !props.editable,
      icon:   {
        name:  'resolve',
        color: '#2356F6',
        size:  'm'
      },
      action: () => resolveErrors( lineItem )
    },
    {
      id:     4,
      name:   'View Details',
      hidden: true, // TODO: Remove hidden when BE adds the inventoryId to the draft line items.
      icon:   {
        name: 'eye-open',
        size: 'm'
      },
      action: () => {
        productDetailsOptions.id = lineItem.id
        productDetailsOptions.type = 'Live'
      }
    }
  ]

}

function toolbarOptions( disabled: boolean ): ToolbarOption[] {

  return [
    {
      id:     1,
      name:   'Add Product',
      icon:   { name: 'add' },
      action: openInventorySidebar,
      hidden: !guard( 'Order.Write' ),
      disabled
    }
  ]
}

const lineItemsBatchOptions: BatchOption<EditableDraftOrderLineItem>[] = [
  {
    id:             1,
    type:           'negative',
    icon:           'delete',
    group:          'Bulk Actions',
    action:         selectedItems => deleteSelectedLineItems( selectedItems ),
    actionName:     'Delete',
    pendingMessage: 'Deleting items',
  }
]

const { pending, blockPageUpdate, updateView } = viewSetup( 'Draft Order Details', params, router, getOrderProducts )

</script>

<template>

  <div class="w-full h-full md:p-4 md:pb-0 grid grid-rows-[1fr_max-content] overflow-hidden">

    <Table
      name="Draft Order Products"
      icon="product"
      class="shadow-custom"
      :flex="true"
      :schema="schema"
      :params="params"
      :pending="pending"
      :records="items"
      :selectable="guard('Order.Write')"
      record-map-key="id"
      :batch-options="lineItemsBatchOptions"
      :record-options="itemOptions"
      :toolbar-options="toolbarOptions(!editable || (pending || updatePending))"
      :pagination="{
        total,
        maxPages,
      }"
    />

    <div class="w-full h-10 px-4 flex md:hidden items-center justify-end bg-core-10">

      <p class="font-medium">
        <span class="text-core">Total Cost: </span>{{ formatCurrency(itemsTotal) }}
      </p>

    </div>

    <Sidebar
      :open="$route.name === 'Draft Order Products'"
      :strict="shouldUpdate"
      custom-tw-offset="top-0 md:top-[3rem] h-full md:h-[calc(100%-3rem)]"
      @close="cancelInventorySidebar"
    >

      <div class="w-full md:w-[26rem] h-full grid grid-rows-[max-content_1fr_max-content] md:grid-rows-[1fr_max-content] ignore-outside">

        <div class="w-full h-12 pl-4 flex md:hidden items-center space-x-3">

          <Icon name="product" size="m" class="text-main" />

          <p class="text-sm font-medium grow">
            Your Products
          </p>

        </div>

        <RouterView v-slot="{ Component }">

          <Component
            :is="Component"
            v-model:selected-products="selectedProducts"
            :actions="{ add: addProduct }"
            :facility="order?.facilityCode"
            :disabled="updatePending"
            :allow-locking="false"
            :allow-price-update="true"
            match-selected-products-by="sku"
          />

        </RouterView>

        <div
          class="w-full h-12 grid grid-cols-2"
          :class="{ 'md:hidden': !shouldUpdate, 'md:grid': shouldUpdate }"
        >

          <Button
            size="auto"
            mode="secondary"
            :disabled="updatePending"
            @click="() => cancelInventorySidebar()"
          >
            Cancel
          </Button>

          <Button
            size="auto"
            :disabled="!canConfirmItems"
            :pending="updatePending"
            @click="updateInventorySidebar"
          >
            Confirm Products
          </Button>

        </div>

      </div>

    </Sidebar>

    <Sidebar
      :dim="true"
      :strict="true"
      :open="openEditForm"
      @close="closeEditItemSidebar"
    >

      <div v-if="editedItem" class="w-full md:w-[26rem] h-full grid grid-rows-[max-content_1fr_max-content] overflow-hidden">

        <div class="w-full h-12 pl-4 flex items-center border-b border-core-30">

          <p class="text-sm font-medium truncate grow">
            Edit Item <span class="text-main">[{{ editedItem.sku.value }}]</span>
          </p>

          <div class="w-12 h-full border-l border-core-30">

            <Button
              mode="ghost"
              type="box"
              size="auto"
              icon="close"
              class="h-full w-full"
              :disabled="updatePending"
              @click="closeEditItemSidebar"
            />

          </div>

        </div>

        <div
          class="p-4 grid gap-4 content-start overflow-hidden overflow-y-auto"
          :class="{
            'pointer-events-none': updatePending,
          }"
        >

          <Textbox
            v-model="editedItem.description.value"
            v-model:valid="editedItem.description.valid"
            label="Description"
          />

          <Input
            v-model="editedItem.quantity.value"
            v-model:valid="editedItem.quantity.valid"
            label="Quantity"
            type="number"
            :min="1"
          />

          <Input
            v-model="editedItem.price.value"
            v-model:valid="editedItem.price.valid"
            label="Cost"
            type="currency"
            align="right"
          />

        </div>

        <div class="w-full h-12 grid">

          <Button
            size="auto"
            class="h-full px-4"
            :disabled="!canSaveEditedItem"
            :pending="updatePending"
            @click="updateLineItem"
          >
            Save Item
          </Button>

        </div>

      </div>

      <div v-if="resolvedId" class="w-full md:w-[26rem] h-full grid grid-rows-[max-content_1fr_max-content] overflow-hidden">

        <div class="w-full h-12 pl-4 flex items-center space-x-2 border-b border-core-30">

          <Icon name="issue-circle" size="m" class="text-error" />

          <p class="text-sm font-medium truncate grow">
            Unresolved Errors <span class="text-error">[{{ errorsSchema.length }}]</span>
          </p>

          <div class="w-12 h-full border-l border-core-30">

            <Button
              mode="ghost"
              type="box"
              size="auto"
              icon="close"
              class="h-full w-full"
              @click="closeEditItemSidebar"
            />

          </div>

        </div>

        <div class="w-full h-full md:p-4 grid content-start gap-2 overflow-y-auto bg-core-30">

          <div
            v-for="error, index in errorsSchema"
            :key="error.key"
            :class="{
              'pointer-events-none': updatePending,
            }"
          >

            <div class="p-4 bg-core-20">

              <p class="text-sm">
                <span class="text-error">[{{ index + 1 }}]</span> {{ error.error }}
              </p>

            </div>

            <Input
              v-if="error.type === 'text'"
              v-model="error.value"
              v-model:valid="error.valid"
              :label="error.label"
              :required="error.required"
            />

            <Input
              v-if="error.type === 'currency'"
              v-model="error.value"
              v-model:valid="error.valid"
              type="currency"
              :label="error.label"
              :required="error.required"
            />

            <Input
              v-if="error.type === 'number'"
              v-model="error.value"
              v-model:valid="error.valid"
              type="number"
              :label="error.label"
              :required="error.required"
            />

            <Input
              v-if="error.type === 'strict-number'"
              v-model="error.value"
              v-model:valid="error.valid"
              :label="error.label"
              type="strict-number"
              :required="error.required"
            />

            <Textbox
              v-if="error.type === 'textbox'"
              v-model="error.value"
              v-model:valid="error.valid"
              :label="error.label"
              :required="error.required"
            />

            <Select
              v-else-if="error.type === 'select'"
              v-model="error.value"
              v-model:valid="error.valid"
              :label="error.label"
              :options="error.options"
              :required="error.required"
            />

            <Toggle
              v-if="error.type === 'toggle'"
              v-model="error.value"
              v-model:valid="error.valid"
              :label="error.label"
            />

          </div>

        </div>

        <div class="w-full h-12 grid">

          <Button
            v-if="editedItem"
            size="auto"
            class="h-full px-4"
            :disabled="!canSaveEditedItem"
            :pending="updatePending"
            @click="updateLineItem"
          >
            Save Item
          </Button>

          <Button
            v-if="errorsSchema"
            size="auto"
            class="h-full px-4"
            :disabled="!canResolveErrors"
            :pending="updatePending"
            @click="() => resolveLineItemErrors(resolvedId)"
          >
            Resolve Errors
          </Button>

        </div>

      </div>

    </Sidebar>

  </div>

</template>
