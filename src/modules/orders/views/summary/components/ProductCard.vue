<script setup lang="ts">

import Icon from '@lib/components/blocks/Icon.vue'

import type { TopSellingProduct } from '@/modules/orders/types'

defineProps<{
  index?:  number
  product: TopSellingProduct
}>()

</script>

<template>

  <div class="w-full h-[3.75rem] px-3 grid grid-cols-[max-content_max-content_1fr] items-center gap-x-3">

    <p class="text-sm text-main bg-main-20 px-1 rounded-xs">
      {{ index + 1 }}
    </p>

    <div class="w-9 h-9 grid place-content-center bg-core-10 border border-core-30 rounded-xs">
      <Icon name="shaded-product" size="m" class="text-main overflow-visible" />
    </div>

    <div class="truncate">

      <p class="text-sm truncate">
        {{ product.sku }}
      </p>

      <p class="text-xs text-core truncate">
        {{ product.title }}
      </p>

    </div>

  </div>

</template>
