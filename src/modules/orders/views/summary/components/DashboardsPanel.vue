<script setup lang="ts">

import { computed, reactive, ref, watch } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Chart from '@/modules/orders/views/summary/components/Chart.vue'
import Button from '@lib/components/button/Button.vue'

import type { DoughnutChartData, StackedAreaChartData } from '@/modules/orders/types'

const props = defineProps<{
  methods?:              string[]
  methodOrders?:         StackedAreaChartData
  shippedOrders?:        StackedAreaChartData
  channelOrders?:        DoughnutChartData
  methodPending?:        boolean
  channelPending?:       boolean
  ordersChannels?:       string[]
  shippedChannels?:      string[]
  defaultChannelOrders?: DoughnutChartData
}>()

const methodsFilterList = computed<DropListOption[]>(() =>
  props.methods?.map( method => ({
    name:   method,
    id:     method,
    action: () => {
      methodsFilter.value === method
        ? methodsFilter.value = null
        : methodsFilter.value = method
    }
  }))
)

const channelFilterList = computed<DropListOption[]>(() =>
  props.ordersChannels?.map( channel => ({
    name:   channel,
    id:     channel,
    action: () => {
      channelFilter.value === channel
        ? channelFilter.value = null
        : channelFilter.value = channel
    }
  }))
)

const shippedFilterList = computed<DropListOption[]>(() =>
  props.shippedChannels?.map( channel => ({
    name:   channel,
    id:     channel,
    action: () => {
      shippedFilter.value === channel
        ? shippedFilter.value = null
        : shippedFilter.value = channel
    }
  }))
)

const channelOrdersData = ref( props.channelOrders )

const methodsFilter = reactive({
  key:   'group',
  value: null
})

const shippedFilter = reactive({
  key:   'group',
  value: null
})

const channelFilter = reactive({
  key:   'group',
  value: null
})

watch(() => props.channelOrders, () => {
  channelOrdersData.value = props.channelOrders
}, { immediate: true, deep: true })

</script>

<template>

  <div class="w-full h-full md:px-8 md:pt-2 md:pb-8 overflow-hidden overflow-y-auto">

    <div class="w-full h-10 px-3 md:px-0 md:h-14 flex items-center justify-between md:bg-transparent bg-core-20 border-b md:border-b-0 border-core-30">

      <p class="text-sm md:text-base font-medium">
        Dashboards
      </p>

      <p class="text-core text-sm md:text-base font-medium">
        Rolling: <span class="text-main">30 Days</span>
      </p>

    </div>

    <div class="grid xl:grid-cols-2 md:gap-6">

      <Chart
        type="Area"
        name="Orders By Shipping Method"
        class="border-b md:border-b-0 border-core-30"
        :pending="methodPending"
        :data="methodOrders"
        :raw-data="methodOrders"
        :filter="methodsFilter"
        :options="{
          axes: {
            left: {
              title: 'Total Orders',
            },
            bottom: {
              title: 'Created Date',
            },
          },
        }"
      >

        <div class="w-full h-full flex items-center">

          <Button
            mode="ghost"
            size="auto"
            class="w-max h-full"
            :options="methodsFilterList"
            @click.stop
          >

            <template #default="{ active }">

              <div class="pl-4 flex items-center gap-x-2" :class="{ 'pr-4': !methodsFilter.value, 'pr-2': methodsFilter.value }">

                <p v-if="methodsFilter?.value" class="text-sm text-interactive font-medium">
                  <span class="text-core">Method: </span>{{ methodsFilter.value }}
                </p>

                <p v-else class="text-sm text-text-helper">
                  Filter By Method
                </p>

                <Icon v-if="!methodsFilter.value" name="chevron-down" size="s" :class="{ 'rotate-180': active }" />

                <Button
                  v-else
                  size="s"
                  type="box"
                  mode="ghost"
                  icon="close"
                  class="z-1"
                  @click.stop="methodsFilter.value = null"
                />

              </div>

            </template>

          </Button>

        </div>

      </Chart>

      <Chart
        type="Donut"
        name="Orders By Channel"
        class="border-b md:border-b-0 border-core-30"
        :pending="channelPending"
        :data="channelOrdersData"
        :raw-data="defaultChannelOrders"
        :filter="channelFilter"
      >

        <div class="w-full h-full flex items-center">

          <Button
            mode="ghost"
            size="auto"
            class="w-max h-full"
            :options="channelFilterList"
            @click.stop
          >

            <template #default="{ active }">

              <div class="pl-4 flex items-center gap-x-2" :class="{ 'pr-4': !channelFilter.value, 'pr-2': channelFilter.value }">

                <p v-if="channelFilter?.value" class="text-sm text-text-interactive font-medium">
                  <span class="text-text-helper">Channel: </span>{{ channelFilter.value }}
                </p>

                <p v-else class="text-sm text-text-helper">
                  Filter By Channel
                </p>

                <Icon v-if="!channelFilter.value" name="chevron-down" size="s" :class="{ 'rotate-180': active }" />

                <Button
                  v-else
                  size="s"
                  type="box"
                  mode="ghost"
                  icon="close"
                  class="z-1"
                  @click.stop="channelFilter.value = null"
                />

              </div>

            </template>

          </Button>

        </div>

      </Chart>

      <Chart
        type="Area"
        name="Shipped Orders"
        class="xl:col-span-2"
        :pending="channelPending"
        :data="shippedOrders"
        :raw-data="shippedOrders"
        :filter="shippedFilter"
        :options="{
          axes: {
            left: {
              title: 'Total Orders',
            },
            bottom: {
              title: 'Shipped Date',
            },
          },
        }"
      >

        <div class="w-full h-full flex items-center">

          <Button
            mode="ghost"
            size="auto"
            class="w-max h-full"
            :options="shippedFilterList"
            @click.stop
          >

            <template #default="{ active }">

              <div class="pl-4 flex items-center gap-x-2" :class="{ 'pr-4': !shippedFilter.value, 'pr-2': shippedFilter.value }">

                <p v-if="shippedFilter?.value" class="text-sm text-text-interactive font-medium">
                  <span class="text-text-helper">Channel: </span>{{ shippedFilter.value }}
                </p>

                <p v-else class="text-sm text-text-helper">
                  Filter By Channel
                </p>

                <Icon v-if="!shippedFilter.value" name="chevron-down" size="s" :class="{ 'rotate-180': active }" />

                <Button
                  v-else
                  size="s"
                  type="box"
                  mode="ghost"
                  icon="close"
                  class="z-1"
                  @click.stop="shippedFilter.value = null"
                />

              </div>

            </template>

          </Button>

        </div>

      </Chart>

    </div>

  </div>

</template>
