<script setup lang="ts" generic="ChartType extends 'Line' | 'Bar'">

import { theme } from '@lib/scripts/themeUtils'
import { computed, ref, watch } from 'vue'
import { getColorHexFromRoot, growNumberToTarget } from '@lib/scripts/utils'
import { CcvLineChart, CcvSimpleBarChart, ScaleTypes } from '@carbon/charts-vue'

import Icon from '@lib/components/blocks/Icon.vue'

import type { StackedAreaChartData } from '@/modules/orders/types'
import type { BarChartOptions, LineChartOptions } from '@carbon/charts-vue'

const props = withDefaults( defineProps<{
  name:          string
  data?:         StackedAreaChartData
  unit?:         string
  pending?:      boolean
  chartType:     ChartType
  isWarning?:    boolean
  isComingSoon?: boolean
}>(), {
  unit:         'Today',
  isComingSoon: false,
})

const hover    = ref( false )
const target   = ref( null )
const hasData  = computed(() => props.data && props.data.length > 1 )
const cardData = ref<StackedAreaChartData>( null )

// Reactive refs for the colors
const dataset1BarColor  = ref( getColorHexFromRoot( '--icon-disabled' ))
const dataset2BarColor  = ref( getColorHexFromRoot( '--support-caution-major' ))
const dataset1LineColor = ref( getColorHexFromRoot( '--color-icon-disabled' ))
const dataset2LineColor = ref( getColorHexFromRoot( '--support-caution-major' ))

// Update colors when theme changes
watch( theme, () => {
  dataset1BarColor.value = getColorHexFromRoot( '--icon-disabled' )
  dataset2BarColor.value = getColorHexFromRoot( '--support-caution-major' )
  dataset1LineColor.value = getColorHexFromRoot( '--color-icon-disabled' )
  dataset2LineColor.value = getColorHexFromRoot( '--support-caution-major' )
}, { deep: true })

function isDateToday( date: Date ) {

  if ( !date )
    return false

  const today = new Date()

  return date.getFullYear() === today.getFullYear()
    && date.getMonth() === today.getMonth()
    && date.getDate() === today.getDate()

}

function spliceData( chartData: StackedAreaChartData ) {

  if ( !chartData )
    return []

  if ( chartData.length === 0 )
    return []

  if ( chartData.length === 1 ) {
    growNumberToTarget( chartData[0].value, value => target.value = value )
    return chartData
  }

  // Sort the data by date

  chartData.sort(( a, b ) => new Date( a.date ).getTime() - new Date( b.date ).getTime())

  let cData = chartData.length > 4
    ? chartData.slice( -4 ) // ------------- If there are more than 4 take the last 4 data points
    : chartData // ------------------------- get all if less than 4 data points

  // Check if the latest date is today

  if ( isDateToday( cData[cData.length - 1]?.date )) {

    // Set the target value to the latest value

    growNumberToTarget( cData[cData.length - 1].value, value => target.value = value )

    // Add a identical data point to the last one for the second dataset, so the line chart can be connected.

    cData.push({ ...cData[cData.length - 1], group: 'Dataset 2' })

  }

  else {

    // If the latest date is not today, add a new data point with the value of 0
    // Add a identical data point for the second dataset, so the line chart can be connected.

    cData.splice( cData.length - 1, 1, { date: new Date(), value: 0, group: 'Dataset 1' })
    cData.splice( cData.length, 1, { date: new Date(), value: 0, group: 'Dataset 2' })

    // Set the target value to 0, since there is no data for today

    target.value = 0

  }

  // Increment all the values by 1 so we don't have zero values
  // This is only for the chart, the actual chart value is not changed.

  cData = cData.map( item => ({ ...item, value: item.value + 1 }))

  return cData

}

watch(() => props.data, n => cardData.value = spliceData( n ), { immediate: true, deep: true })

// Use computed for the options that incorporate reactive colors
const lineOptions = computed<LineChartOptions>(() => ({
  axes: {
    bottom: {
      visible:   false,
      mapsTo:    'date',
      scaleType: ScaleTypes.LABELS
    },
    left: {
      visible:   false,
      mapsTo:    'value',
      scaleType: ScaleTypes.LINEAR
    }
  },
  height: '40px',
  width:  '30px',
  legend: {
    enabled: false
  },
  toolbar: {
    enabled: false
  },
  grid: {
    x: {
      enabled: false,
    },
    y: {
      enabled: false
    }
  },
  tooltip: {
    enabled: false
  },
  color: {
    scale: {
      'Dataset 1': dataset1LineColor.value,
      'Dataset 2': dataset2LineColor.value
    }
  },
}))

const barOptions = computed<BarChartOptions>(() => ({
  axes: {
    bottom: {
      visible:   false,
      mapsTo:    'date',
      scaleType: ScaleTypes.LABELS
    },
    left: {
      visible:   false,
      mapsTo:    'value',
      scaleType: ScaleTypes.LINEAR
    }
  },
  height: '24px',
  width:  '34px',
  legend: {
    enabled: false
  },
  toolbar: {
    enabled: false
  },
  grid: {
    x: {
      enabled: false,
    },
    y: {
      enabled: false
    }
  },
  tooltip: {
    enabled: false
  },
  color: {
    scale: {
      'Dataset 1': dataset1BarColor.value,
      'Dataset 2': props.isComingSoon ? dataset1BarColor.value : dataset2BarColor.value
    }
  },
}))

</script>

<template>

  <div
    class="w-full h-[5.5rem] grid grid-rows-[max-content_1fr] p-3"
    :class="{
      'bg-layer-02 custom-dashed-border border': isComingSoon,
      'bg-layer-01 hover:bg-layer-hover-01 hover:shadow-custom cursor-pointer hover:z-10 hover:relative': !isComingSoon,
    }"
    @mouseover="hover = true" @mouseleave="hover = false"
  >

    <div class="w-full h-fit pr-1 grid grid-cols-[1fr_max-content] items-center">

      <p class="text-sm text-text-secondary font-medium">
        {{ name }}
      </p>

      <Icon
        :name="isComingSoon ? 'in-progress' : 'go'"
        :class="isComingSoon ? 'text-support-info' : 'text-icon-interactive'"
        size="m"
      />

    </div>

    <div class="w-full grid grid-cols-[1fr_max-content] grid-rows-1 items-end">

      <p
        class="text-3xl overflow-visible"
        :class="{
          'text-interactive': !isWarning,
          'text-error': isWarning,
        }"
      >
        {{ !isComingSoon ? target : null }}
        <span class="text-xs text-text-placeholder">{{ unit }}</span>
      </p>

      <div
        v-if="hasData && !pending"
        class="w-[34px] h-[40px] overflow-visible relative flex items-end"
      >

        <CcvLineChart v-if="chartType === 'Line'" :options="lineOptions" :data="cardData" />
        <CcvSimpleBarChart v-else-if="chartType === 'Bar'" :options="barOptions" :data="cardData" />

      </div>

      <div v-else-if="pending" class="w-6 h-6 grid place-content-center">
        <Icon name="loading" size="m" class="text-core" />
      </div>

    </div>

  </div>

</template>

<style>
.cds--chart-holder svg rect {
  fill: transparent !important;
}
</style>
