<script setup lang="ts">

import { ref } from 'vue'
import { CcvGaugeChart } from '@carbon/charts-vue'
import { getColorHexFromRoot } from '@lib/scripts/utils'

import type { GaugeChartData } from '@/modules/orders/types'
import type { GaugeChartOptions } from '@carbon/charts-vue'

defineProps<{
  statusData?: GaugeChartData
}>()

const statusChartOptions = ref<GaugeChartOptions>({
  resizable: true,
  height:    '87px',

  toolbar: {
    enabled: false
  },
  gauge: {
    type:          'semi',
    status:        'success',
    arcWidth:      8,
    alignment:     'center',
    valueFontSize: () => 0
  },
  color: {
    scale: {
      value: getColorHexFromRoot( '--support-caution-major' )
    }
  }
})

</script>

<template>

  <div class="w-full h-full grid grid-rows-[max-content_1fr] gap-y-px">

    <div class="w-full h-10 px-3 flex items-center bg-layer-01">

      <p class="text-sm text-text-primary font-medium">
        Order Processing Insights
      </p>

    </div>

    <div class="w-full h-[8.75rem] relative flex flex-col bg-layer-01 justify-between items-center">

      <span class="text-sm font-medium text-support-caution-major pt-3 pl-3 place-self-start">Fulfillment Status</span>

      <CcvGaugeChart
        :data="[statusData]"
        :options="statusChartOptions"
      />

      <p class="text-[2rem] text-main absolute left-1/2 -translate-x-1/2 bottom-[1.625rem] leading-[2rem]">
        {{ statusData?.value }}%
      </p>

      <p class="text-xs text-core-60 absolute left-1/2 -translate-x-1/2 bottom-[0.5rem]">
        Today
      </p>

    </div>

  </div>

</template>
