<script setup lang="ts" generic="Type extends 'Line' | 'Donut' | 'Area' | 'Stacked Area'">

import { ScaleTypes } from '@carbon/charts-vue'
import { mergeObjects } from '@lib/scripts/utils'
import { computed, reactive, ref, watch } from 'vue'
import { <PERSON><PERSON><PERSON>, Donut<PERSON><PERSON>, <PERSON><PERSON><PERSON>, StackedAreaChart } from '@carbon/charts'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/button/Button.vue'

import type { AreaChartOptions, DonutChartOptions, LineChartOptions, StackedAreaChartOptions } from '@carbon/charts-vue'

interface OptionsList {
  'Line':         LineChartOptions
  'Area':         AreaChartOptions
  'Donut':        DonutChartOptions
  'Stacked Area': StackedAreaChartOptions
}

const props = defineProps<{
  type:     Type
  name:     string
  data:     Record<string, any>[]
  rawData:  Record<string, any>[]
  pending?: boolean
  filter?: {
    key:   string
    value: any
  }
  options?: OptionsList[Type]
}>()

const chartData       = computed(() => filterData( props?.filter?.value ? props.rawData : props.data, props?.filter?.key, props?.filter?.value ))
const fullScreen      = ref<boolean>( false )
const chartWrapper    = ref<HTMLDivElement | null>( null )
const chartInstance   = ref<DonutChart | AreaChart | LineChart | StackedAreaChart>( null )
const chartContainer  = ref<HTMLDivElement | null>( null )

watch( fullScreen, ( n ) => {

  if ( n )
    chartWrapper.value.style.overflow = 'hidden'

})

const optionsList = reactive<OptionsList>({
  'Line': {},
  'Area': {
    toolbar: {
      enabled: false
    },
    axes: {
      left: {
        scaleType: ScaleTypes.LINEAR,
        mapsTo:    'value',
      },
      bottom: {
        scaleType: ScaleTypes.TIME,
        mapsTo:    'date',
        ticks:     {
          number:   5,
          rotation: 'never' as any
        }
      }
    },
    curve:  'curveMonotoneX',
    height: '100%',
    width:  '100%',
    legend: {
      enabled: false
    },
    fileDownload: {
      fileName: props.name
    }
  },
  'Donut': {
    toolbar: {
      enabled: false
    },
    pie: {
      labels: {
        enabled: true
      },
    },
    donut: {
      alignment: 'center',
      center:    {
        label: 'Total',
      },
    },
    height: '90%',
    width:  '100%',
    legend: {
      enabled: false
    },
    fileDownload: {
      fileName: props.name
    }
  },
  'Stacked Area': {
    toolbar: {
      enabled: false
    },
    axes: {
      left: {
        stacked:   true,
        scaleType: ScaleTypes.LINEAR,
        mapsTo:    'value',
      },
      bottom: {
        scaleType: ScaleTypes.TIME,
        mapsTo:    'date',
        ticks:     {
          number:   5,
          rotation: 'never' as any
        }
      }
    },
    curve:  'curveMonotoneX',
    height: '100%',
    width:  '100%',
    legend: {
      enabled: false
    },
    fileDownload: {
      fileName: props.name
    }
  }
})

function filterData( data: Record<string, any>[], key: string, value: string | number ) {

  if ( !value || !key )
    return data

  return data.filter( item => item[key] === value )

}

watch( [ chartData, chartContainer ], ( n ) => {

  if ( !n[0] || !n[1] )
    return

  if ( chartInstance.value ) {
    chartInstance.value.model.setData( n[0] )
    chartInstance.value.model.setOptions({ ...optionsList[props.type], ...props.options })
  }

  if ( n[0] && n[1] && !chartInstance.value ) {

    if ( props.type === 'Donut' ) {

      chartInstance.value = new DonutChart( n[1], {
        data:    n[0],
        options: props.options ? mergeObjects( optionsList.Donut, props.options ) : optionsList.Donut,
      })

    }

    else if ( props.type === 'Line' ) {

      chartInstance.value = new LineChart( n[1], {
        data:    n[0],
        options: props.options ? mergeObjects( optionsList.Line, props.options ) : optionsList.Line,
      })

    }

    else if ( props.type === 'Area' ) {

      chartInstance.value = new AreaChart( n[1], {
        data:    n[0],
        options: props.options ? mergeObjects( optionsList.Area, props.options ) : optionsList.Area,
      })

    }

    else if ( props.type === 'Stacked Area' ) {

      chartInstance.value = new StackedAreaChart( n[1], {
        data:    n[0],
        options: props.options ? mergeObjects( optionsList['Stacked Area'], props.options ) : optionsList['Stacked Area'],
      })

    }

  }

})

const chartExportOptions = computed<DropListOption[]>(() => [
  {
    id:     1,
    name:   'Export as PNG',
    action: () => chartInstance.value.services.domUtils.exportToPNG()
  },
  {
    id:     2,
    name:   'Export as JPG',
    action: () => chartInstance.value.services.domUtils.exportToJPG()
  },
  {
    id:     3,
    name:   'Export as CSV',
    action: () => chartInstance.value.model.exportToCSV()
  }
] )

</script>

<template>

  <div class="w-full max-w-full h-80 grid grid-rows-[max-content_1fr_max-content] bg-core-10">

    <div class="w-full h-10 pl-4 flex items-center bg-core-20 border-b border-core-30">

      <p class="text-core text-sm font-medium grow">
        {{ name }}
      </p>

      <Button
        size="auto"
        mode="ghost"
        type="box"
        icon="portal-asn"
        class="h-full w-10 hidden md:block"
        @click="() => {
          fullScreen = true
          chartInstance.services.domUtils.toggleFullscreen()
        }"
      />

      <Button
        size="auto"
        mode="ghost"
        class="h-full px-4 gap-x-3"
        :options="chartExportOptions"
        :disabled="!chartInstance"
      >

        <p class="text-sm">
          Export
        </p>
        <Icon name="chevron-down" size="s" />

      </Button>

    </div>

    <div v-if="pending" class="w-full h-full grid place-content-center">
      <Icon name="loading" size="m" class="text-core" />
    </div>

    <div
      v-show="!pending"
      ref="chartWrapper"
      class="w-full h-full p-4 grid items-center grid-cols-1 grid-rows-1 relative"
      @mouseleave="() => {
        if (!chartInstance.services.domUtils.isFullScreenMode()) {
          fullScreen = false
          chartWrapper.style.overflow = 'visible'
        }
      }"
    >

      <div ref="chartContainer" />

    </div>

    <div class="w-full h-10 flex items-center bg-core-20 border-t border-core-30">
      <slot />
    </div>

  </div>

</template>
