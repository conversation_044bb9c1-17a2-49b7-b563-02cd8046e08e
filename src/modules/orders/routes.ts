import { pageSize } from '@lib/store/table'

import type { OrdersParams } from '@/modules/orders/types'

export const ordersParams: OrdersParams = {
  page:     1,
  pageSize: pageSize.value
}

export const ordersRoutes: RouteModel<OrdersParams> = {
  name:      'Orders',
  path:      '/orders',
  redirect:  '/orders/summary',
  component: () => import( '@/modules/orders/views/Orders.vue' ),
  children:  [
    {
      name: 'Orders Summary',
      path: '/orders/summary',
      meta: {
        title: 'Orders Summary - OWD',
        scope: 'Order.Read'
      },
      component: () => import( '@/modules/orders/views/summary/views/Summary.vue' )
    },
    {
      name: 'Live Orders',
      path: '/orders/live',
      meta: {
        queryParams: ordersParams,
        title:       'Live Orders - OWD',
        scope:       'Order.Read'
      },
      component: () => import( '@/modules/orders/views/LiveOrders.vue' ),
      children:  [
        {
          name: 'Live Order Details',
          path: '/orders/live/details/:orderId',
          meta: {
            title: 'Order Details - OWD',
            scope: 'Order.Read'
          },
          component: () => import( '@/modules/orders/views/live-details/views/LiveOrderDetails.vue' ),
          children:  [
            {
              name: 'Live Order Products',
              path: '/orders/live/details/:orderId/add-product',
              meta: {
                title: 'Order Products - OWD',
                scope: [ 'Order.Write', 'Order.Read' ]
              },
              component: () => import( '@/components/InventoryPanel.vue' )
            },
          ]
        },
        {
          name: 'Create Order',
          path: '/orders/create-order',
          meta: {
            title: 'Create Order - OWD',
            scope: [ 'Order.Write', 'Order.Read' ]
          },
          component: () => import( '@/modules/orders/views/create-order/views/CreateOrder.vue' )
        },
      ]
    },
    {
      name:      'Draft Orders',
      path:      '/orders/draft',
      component: () => import( '@/modules/orders/views/DraftOrders.vue' ),
      meta:      {
        params: ordersParams,
        title:  'Draft Orders - OWD',
        scope:  'Order.Read',
      },
      children: [
        {
          name: 'Draft Order Details',
          path: '/orders/draft/details/:orderId',
          meta: {
            title: 'Order Details - OWD',
            scope: 'Order.Read'
          },
          component: () => import( '@/modules/orders/views/draft-details/views/DraftOrderDetails.vue' ),
          children:  [
            {
              name: 'Draft Order Products',
              path: '/orders/draft/details/:orderId/add-product',
              meta: {
                title: 'Order Products - OWD',
                scope: [ 'Order.Write', 'Order.Read' ]
              },
              component: () => import( '@/components/InventoryPanel.vue' )
            }
          ]
        },
        {
          name: 'Upload Bulk Orders',
          path: '/orders/draft/bulk-upload',
          meta: {
            title: 'Upload Bulk Orders - OWD',
            scope: [ 'Order.Write', 'Order.Read' ]
          },
          component: () => import( '@/components/BulkUploadDrawer.vue' )
        }
      ],
    },
  ]
}
