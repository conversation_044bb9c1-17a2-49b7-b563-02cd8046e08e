import type { AvailableStockLevel } from '@/modules/inventory/types'
import type { ImportError, ImportStatus } from '@/types'

export interface OrdersParams extends BaseParams {
  sku?:               string
  isHeld?:            boolean
  isVoid?:            boolean
  facility?:          string
  poNumber?:          string
  groupName?:         string
  isShipped?:         boolean
  isBackorder?:       boolean
  atWarehouse?:       boolean
  owdReference?:      string
  customerCity?:      string
  customerEmail?:     string
  customerState?:     string
  trackingNumber?:    string
  createdEndDate?:    string
  shippedEndDate?:    string
  shippingMethod?:    string
  clientReference?:   string
  createdStartDate?:  string
  shippedStartDate?:  string
  customerLastName?:  string
  customerFirstName?: string
}

export interface DraftOrdersParams extends BaseParams {
  email?:           string
  orderId?:         number
  lastName?:        string
  firstName?:       string
  hasErrors?:       boolean
  facilityCode?:    string
  importStatus?:    ImportStatus
  createdDateTo?:   string
  clientReference?: string
  isBusinessOrder?: boolean
  createdDateFrom?: string
  hasMasterRecord?: boolean
}

export interface OrderLineItemsParams extends BaseParams {
  isRpc?: boolean
}

export type OrdersViewType = 'draft' | 'live'

export interface Order {
  id:                  number
  total:               number
  notes:               string
  status:              string
  clientId:            number
  shipDate:            any
  comments:            any
  createdDate:         string
  owdReference:        string
  receivedDate:        any
  facilityCode:        string
  customerType:        any
  customerEmail:       string
  shippingMethod:      string
  clientReference:     string
  customerLastName:    string
  customerFirstName:   string
  facilityDisplayName: string
}

export interface DraftOrder {
  id:                  number
  isDdp:               boolean
  isGift:              boolean
  source:              string
  billing:             OrderBilling
  shipping:            OrderShipping
  poNumber:            string
  groupName:           string
  createdTs:           string
  modifiedTs:          any
  giftMessage:         string
  importStatus:        ImportStatus
  importErrors:        ImportError<DraftOrder>[]
  facilityCode:        string
  backorderRule:       BackorderRule
  holdForRelease:      boolean
  shippingMethod:      string
  masterRecordId:      number
  clientReference:     string
  isBusinessOrder:     boolean
  shipHandlingFee:     number
  createdByUserId:     string
  importErrorCount:    number
  modifiedByUserId?:   any
  createdByUsername:   string
  modifiedByUsername?: string
}

export type OrderStatus = 'On Hold' | 'Backorder' | 'At Warehouse' | 'Void' | 'Shipped'

export interface PackageShipment {
  rate:                         number
  weight:                       number
  aesItn:                       string
  carrier:                      string
  dimWeight:                    number
  avsOverride:                  boolean
  shippedDate:                  string
  serviceLevel:                 string
  declaredValue:                number
  trackingNumber:               string
  requestSaturdayDelivery:      boolean
  requestSignatureConfirmation: boolean
}

export interface OrderDetails {
  id:                           number
  sla:                          string
  type:                         string
  total:                        number
  notes:                        string
  isCod:                        boolean
  status:                       string
  source:                       string
  events:                       OrderEvent[]
  isVoid:                       boolean
  isGift:                       boolean
  aesItn:                       string
  created:                      string
  shipped:                      any
  billing:                      OrderBilling
  comments:                     string
  shipping:                     OrderShipping
  released:                     any
  packages:                     number
  subtotal:                     number
  poNumber:                     string
  discount:                     number
  salesTax:                     number
  packSlip:                     string
  owdCoupon:                    string
  isCalltag:                    boolean
  groupName:                    string
  checkDate:                    string
  weightLbs:                    number
  hadDcHold:                    boolean
  pickStatus:                   number
  isInternal:                   boolean
  isShipping:                   boolean
  avsOverride:                  boolean
  shipAccount:                  string
  backOrderId:                  any
  shippedTime:                  any
  giftMessage:                  string
  dutyAndTaxes:                 boolean
  owdReference:                 string
  facilityCode:                 string
  shippedLines:                 number
  shippedUnits:                 number
  backorderLevel:               number
  checkDeposited:               number
  trackingNumber:               string
  isInternational:              boolean
  isBusinessOrder:              boolean
  clientReference:              string
  shipHandlingFee:              number
  insuranceAmount:              number
  shipPaymentType:              string
  packageShipment:              PackageShipment[]
  shipLocationCode:             string
  actualShipMethod:             any
  predictedShipCost:            number
  backOrderReference:           string
  requestSaturdayDelivery:      boolean
  requestSignatureConfirmation: boolean
}

export interface UpdateOrderDetailsPostData {
  isGift:                       boolean
  aesItn:                       string
  carrier:                      string
  billing:                      OrderBilling
  shipping:                     Omit<OrderShipping, 'shipAccount' | 'shipMethodRef' | 'shipPaymentType'>
  giftMessage:                  string
  shipAccount:                  string
  avsOverride:                  boolean
  dutyAndTaxes:                 boolean
  declaredValue:                number
  shipPaymentType:              string
  carrierReferenceNumber:       string
  requestSaturdayDelivery:      boolean
  requestSignatureConfirmation: boolean
}

export interface OrderBilling {
  fax?:         string
  zip:          string
  city:         string
  state:        string
  phone:        string
  email:        string
  country:      string
  lastName:     string
  address1:     string
  address2:     string
  poNumber?:    string
  firstName:    string
  companyName:  string
  paymentType?: string
}

export interface OrderShipping {
  fax?:             string
  zip:              string
  city:             string
  state:            string
  phone:            string
  email:            string
  method?:          string
  country:          string
  shipType?:        string
  lastName:         string
  address1:         string
  address2:         string
  firstName:        string
  companyName:      string
  // We use shippingMethod only for showing the error in the draft details page.
  shippingMethod?:  string
  methodReference?: string
}

export interface KitComponent {
  sku:                  string
  quantity:             number
  availableByFacility?: AvailableStockLevel[]
}

export interface OrderLineItem {
  id:                   number
  sku:                  string
  size:                 string
  cost:                 number
  color:                string
  discount:             number
  parentId:             any
  isKitItem:            boolean
  isInternal:           boolean
  inventoryId:          number
  description:          string
  originCountry:        string
  kitComponents:        OrderLineItem[]
  extendedPrice:        number
  declaredValue:        number
  onHandQuantity:       number
  shippedQuantity:      number
  requestedQuantity:    number
  customsDescription:   string
  backorderedQuantity:  number
  availableStockLevels: AvailableStockLevel[]
}

export interface DraftOrderLineItem {
  id:                  number
  sku:                 string
  cost:                number
  createdTs:           string
  modifiedTs:          any
  description:         string
  importErrors:        ImportError<DraftOrderLineItem>[]
  createdByUserId:     string
  modifiedByUserId?:   any
  importErrorCount:    number
  requestedQuantity:   number
  createdByUsername:   string
  modifiedByUsername?: string
}

export interface EditableDraftOrderLineItem extends DraftOrderLineItem {
  pending?:  boolean
  selected?: boolean
  children?: Partial<EditableDraftOrderLineItem>[]
}

export interface EditableLineItem {
  id?:         number
  sku:         string
  price:       number
  quantity:    number
  description: string
}

export interface EditableOrderLineItem extends OrderLineItem {
  pending?:                     boolean
  selected?:                    boolean
  children?:                    Partial<EditableOrderLineItem>[]
  availableInSelectedFacility?: number
}

export interface UpdateLineItem {
  cost:           number | string
  requested:      number | string
  backordered:    number | string
  description:    string
  line_number?:   string
  part_reference: string
}

export interface CreateOrderLineItem extends Omit<UpdateLineItem, 'part_reference'> {
  id?:                  number
  requested:            number
  available?:           number
  partReference:        string
  declaredValue?:       number
  customsDescription?:  number
  kitComponents?:       KitComponent[]
  availableByFacility?: {
    lots:           any
    facilityCode:   string
    facilityName:   string
    unitsAvailable: number
  }[]
}

export interface OrderEvent {
  id:        number
  type:      number
  message:   string
  creator:   string
  createdAt: string
}

export interface AddEventPostData {
  type:     number
  message:  string
  creator?: string
}

export interface CreateOrderShippingInfo {
  zip:         string
  city:        string
  state:       string
  phone:       string
  email:       string
  country:     string
  address1:    string
  address2:    string
  shipType:    string
  shipCost:    number
  lastName:    string
  firstName:   string
  companyName: string
}

export interface CreateOrderBillingInfo {
  zip:          string
  city:         string
  state:        string
  phone:        string
  email:        string
  country:      string
  poNumber:     string
  address1:     string
  address2:     string
  lastName:     string
  firstName:    string
  paymentType:  string
  companyName?: string
}

export type CreateOrderDetailsInfo = Omit<CreateLiveOrderPostData, 'shipping' | 'billing' | 'lineItems' | 'customValues'>

export type CustomValues = Record<string, string>

export type ShippingGroupName = 'UPS' | 'FedEx' | 'DHL' | 'USPS' | 'Other' | 'Flat Rate'

export type BackorderRule = 'PARTIALSHIP' | 'BACKORDER' | 'NOBACKORDER' | 'HOLDORDER'

export interface CreateOrderPostData {
  isDdp:           boolean
  isGift:          boolean
  source:          string
  billing:         CreateOrderBillingInfo
  shipping:        CreateOrderShippingInfo
  groupName?:      string
  giftMessage:     string
  paymentType?:    string
  backorderRule:   BackorderRule
  holdForRelease:  boolean
  clientReference: string
  isBusinessOrder: boolean
}

export interface CreateLiveOrderPostData extends CreateOrderPostData {
  lineItems:    Partial<CreateOrderLineItem>[]
  facilityRule: string
  customValues: CustomValues
}

export interface CreateDraftOrderPostData extends CreateOrderPostData {
  facilityCode: string
}

/* Summary Types */

export type DoughnutChartData = { group: string, value: number }[]
export type StackedAreaChartData = { group: string, date: Date, value: number }[]
export interface GaugeChartData { group: string, value: number }

export interface OrderByChannel {
  orderDate:    string
  orderCount:   number
  orderStatus:  string
  orderChannel: string
}

export interface ShippedOrderByChannel {
  orderCount:   number
  orderChannel: string
  shippingDate: string
}

export interface OrdersByChannelPayload {
  orders:            OrderByChannel[]
  orderChannelKeys:  string[]
  totalNumberOrders: number
}

export interface ShippedOrdersByChannelPayload {
  orders:            ShippedOrderByChannel[]
  orderChannelKeys:  string[]
  totalNumberOrders: number
}

export enum OrderStatusKey {
  VOID = 'voided',
  ON_HOLD = 'onHold',
  SHIPPED = 'shipped',
  UNKNOWN = 'unknown',
  BACKORDER = 'backordered',
  AT_WAREHOUSE = 'atWarehouse'
}

export interface OrdersByStatusOrder {
  orderDate:      string
  orderCount:     number
  orderStatus:    string
  shippingDate:   string
  shippingMethod: string
}

export interface OrdersByStatusPayload {
  onHold:                   OrdersByStatusOrder[]
  voided:                   OrdersByStatusOrder[]
  shipped:                  OrdersByStatusOrder[]
  unknown:                  OrdersByStatusOrder[]
  backordered:              OrdersByStatusOrder[]
  atWarehouse:              OrdersByStatusOrder[]
  totalNumberOrders:        number
  shippingMethodKeys:       string[]
  fulfillmentStatusPercent: number
}

export interface TopSellingProduct {
  sku:          string
  title:        string
  imageUrl:     string
  clientId:     number
  soldCount:    number
  description:  string
  thumbnailUrl: string
}

export interface BulkOrdersResponse {
  totalRowsInserted: number
  totalRowsRejected: number
}
