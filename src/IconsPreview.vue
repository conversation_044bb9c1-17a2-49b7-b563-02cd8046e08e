<script setup lang="ts">

import { iconsList } from '@lib/store/icon'
import { searchModel } from '@lib/scripts/utils'
import { computed, ref } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Input from '@lib/components/inputs/Input.vue'

const icons         = computed(() => iconsList( 'currentColor' ))
const searchQuery   = ref( '' )
const filteredIcons = computed(() => searchModel( icons.value, searchQuery.value, 'name' ))

</script>

<template>

  <div class="md:p-10 bg-core-10">

    <div
      class="font-medium w-full sticky top-0 h-14 px-4 grid grid-cols-[1fr_2.5rem_2.5rem_2.5rem_2.5rem] gap-x-2 items-center bg-core-20 border-b border-core-30"
    >

      <div class="flex items-center space-x-3">

        <p class="whitespace-nowrap">
          Name
        </p>

        <Input
          v-model="searchQuery" size="s" mode="ghost" placeholder="Search Icons" class="font-normal"
          :required="false"
        >

          <template #prefix>

            <div class="pl-3">
              <Icon name="search" size="m" />
            </div>

          </template>

        </Input>

      </div>

      <div class="h-full grid place-content-center">
        <p>s</p>
      </div>

      <div class="h-full grid place-content-center">
        <p>m</p>
      </div>

      <div class="h-full grid place-content-center">
        <p>l</p>
      </div>

      <div class="h-full grid place-content-center">
        <p>xl</p>
      </div>

    </div>

    <div
      v-for="icon in filteredIcons" :key="String(icon.name)"
      class="text-sm w-full h-14 px-4 grid grid-cols-[1fr_2.5rem_2.5rem_2.5rem_2.5rem] gap-x-2 items-center border-b border-core-30"
    >

      <p>{{ icon.name }}</p>

      <div class="h-full grid place-content-center">
        <Icon :name="icon.name" size="s" />
      </div>

      <div class="h-full grid place-content-center">
        <Icon :name="icon.name" size="m" />
      </div>

      <div class="h-full grid place-content-center">
        <Icon :name="icon.name" size="l" />
      </div>

      <div class="h-full grid place-content-center">
        <Icon :name="icon.name" size="xl" />
      </div>

    </div>

  </div>

</template>
