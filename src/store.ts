import { fetchy } from '@/plugins/fetchy'
import { reactive, ref } from 'vue'

import type { AsnDetails } from '@/modules/asn/types'
import type { LiveProduct } from '@/modules/inventory/types'
import type { OrderDetails } from '@/modules/orders/types'
import type { AnnouncementType, Client, Country, Facility, ImportError } from '@/types'

interface ShippingMethod {
  name:  string
  value: string
}

/**
 * Defines the mode of the application.
 * can be 'CLIENT' or 'ADMIN'
 */
export const appMode: 'CLIENT' | 'ADMIN' = import.meta.env.VITE_APP_MODE ?? 'CLIENT'

export const cookieConsent = ref<boolean>( localStorage.getItem( 'cookie-consent' ) ? localStorage.getItem( 'cookie-consent' ) === 'true' : true )

export const importStatusList: DropListOption[] = [
  {
    id:   'Draft',
    name: 'Draft'
  },
  {
    id:   'Pending',
    name: 'Pending'
  },
  {
    id:   'Processing',
    name: 'In Progress'
  },
  {
    id:   'Failed',
    name: 'Failed'
  },
  {
    id:   'Processed',
    name: 'Processed'
  },
]

export const clientsList              = ref<DropListOption[]>( [] )
export const countriesList            = ref<DropListOption[]>( [] )
export const defaultCountry           = ref<string>( null )
export const facilitiesList           = ref<DropListOption[]>( [] )
export const shippingMethods          = ref<ShippingMethod[]>( [] )
export const defaultFacility          = ref<string>( null )
export const facilitiesLookup         = ref<Facility[]>( [] )
export const announcementsList        = ref<AnnouncementType[]>()
export const shippingMethodsList      = ref<DropListOption[]>( [] )
export const currentAnnouncement      = ref<AnnouncementType>( null )
export const productDetailsOptions    = reactive<{ id: number, type: 'Live' | 'Draft' }>({ id: null, type: null })
export const showAnnouncementModal    = ref<boolean>( false )
export const openAnnouncementsDrawer  = ref <boolean>( false )

export async function getClients() {

  const clientsData = fetchy<Client[]>({
    url:     'client-list',
    baseURL: import.meta.env.VITE_MYOWD_BASE_URL,
    headers: { 'owd-api-key': import.meta.env.VITE_API_KEY }
  })

  return clientsData

}

export async function getCountries() {

  return await fetchy<{ countries: Country[] }>({
    url:    `common/countries-list`,
    method: 'GET'
  })
}

export async function getFacilities() {

  return await fetchy<{ defaultFacility: string, facilities: Facility[] }>({
    url:    `facilities`,
    method: 'GET'
  })

}

export async function getAnnouncements() {
  return await fetchy<AnnouncementType[]>({
    url:        'posts?per_page=10',
    baseURL:    'https://owd.com/wp-json/wp/v2/',
    showError:  false,
    useHeaders: false
  })
}

export async function getShippingMethods() {

  const excludedValues = [
    'Passport Priority DDP',
    'Passport Priority DDU',
    'Ground Max',
    'Purolator',
    'AxleHire',
    'DHL GlobalMail Packet Priority',
    'DHL GlobalMail Packet Standard',
    'DHL Parcel International Standard',
    'DHL Parcel International Standard',
    'DHL SmartMail Parcel Expedited',
    'DHL SmartMail Parcel Expedited Max',
    'DHL SmartMail Parcel Ground',
    'DHL SmartMail Parcel Plus Expedited',
    'DHL SmartMail Parcel Plus Expedited Max',
    'DHL SmartMail Parcel Plus Ground',
    'Ontrac Ground'
  ]

  const { error, payload } = await fetchy<{ shippingMethods: ShippingMethod[] }>({
    url: 'shipping/methods'
  })

  if ( !error )
    payload.shippingMethods = payload?.shippingMethods?.filter(({ name }) => !excludedValues.includes( name )) || []

  return {
    error,
    payload
  }

}

export function openAnnouncement( announcement: AnnouncementType ) {
  currentAnnouncement.value = announcement
  showAnnouncementModal.value = true
}

export function closeAnnouncement() {
  currentAnnouncement.value = null
  showAnnouncementModal.value = false
}

/**
 * Handles the errors from the import details.
 * @param key - The key or keys to check for errors.
 * @param errors - The list of errors.
 * @param anchor - The anchor to prepend to the key.
 * @returns The error message if there is an error, null otherwise.
 */

export function handleDetailsError<T>( key: keyof T | ( keyof T )[], errors: ImportError<T>[], anchor?: string ): string {

  if ( !errors || errors.length === 0 )
    return

  const errorsList: string[] = []

  if ( Array.isArray( key )) {

    key.forEach(( k ) => {

      errors.forEach(( err ) => {

        if ( anchor ) {

          if ( err.propertyName === `${String( anchor )}.${String( k )}` )
            errorsList.push( err.errorMsg )

        }

        else {

          if ( err.propertyName === k )
            errorsList.push( err.errorMsg )

        }

      })

    })

    return errorsList.join( '\n' )

  }

  const errorMsg = errors.find( err => err.propertyName === ( anchor ? `${anchor}.${String( key )}` : key ))?.errorMsg ?? null

  return errorMsg

}

/**
 * Converts a boolean value to 'Yes' or 'No'.
 * @param value - value to convert
 * @param invertResponse - if true, the response will be inverted
 * @returns 'Yes' if the value is true, 'No' if the value is false, null otherwise.
 */

export function booleanToYesNo( value: boolean, invertResponse = false ) {

  if ( invertResponse )
    return value === false ? 'Yes' : value ? 'No' : null
  else
    return value ? 'Yes' : value === false ? 'No' : null

}

// ----------------------------------------- [ Details Cache ]

type CacheType = 'order' | 'asn' | 'product'

interface DetailsCacheEntry<T extends AsnDetails | OrderDetails | LiveProduct> {
  id:        number
  type:      CacheType
  data:      T
  timestamp: number
}

type DetailsCache = Record<CacheType, DetailsCacheEntry<AsnDetails | OrderDetails | LiveProduct>[]>

/**
 * Caches the details data.
 * @param cacheType - The type of the cache.
 * @param detailsData - The data to cache.
 * The cache will be stored in the localStorage under the key 'details-cache'.
 * The cache will be an object with keys 'order', 'asn', and 'product'.
 * Each key will have an array of objects with the following properties:
 * - id: The id of the entry.
 * - type: The type of the cache.
 * - data: The data to cache.
 * - timestamp: The timestamp of the cache.
 * If the cache already exists, the entry will be updated with the new data and timestamp.
 * If the cache does not exist, a new entry will be created.
 */

export function cacheDetails<T extends AsnDetails | OrderDetails | LiveProduct>( cacheType: CacheType, detailsData: T ) {

  const cacheTimestamp = new Date().getTime()

  const cache: DetailsCache = localStorage.getItem( 'details-cache' ) ? JSON.parse( localStorage.getItem( 'details-cache' )) : {} as DetailsCache

  const cacheData = cache[cacheType]

  if ( cacheData ) {

    const existingEntry = cacheData.find(( entry: DetailsCacheEntry<T> ) => entry.id === detailsData.id )

    if ( existingEntry ) {

      existingEntry.data = detailsData
      existingEntry.timestamp = cacheTimestamp

    }

    else {
      cacheData.push({
        id:        detailsData.id,
        type:      cacheType,
        data:      detailsData,
        timestamp: cacheTimestamp
      })
    }

  }

  else {

    cache[cacheType] = [ {
      id:        detailsData.id,
      type:      cacheType,
      data:      detailsData,
      timestamp: cacheTimestamp
    } ]

  }

  localStorage.setItem( 'details-cache', JSON.stringify( cache ))

}

/**
 * Handles the cached details.
 * @param status - The status of the request.
 * @param type - The type of the cache.
 * @param entryId - The id of the entry.
 * @returns The data and status.
 * If the status is 200, the data will be null and the status will be 200.
 * If the status is in the range of 400s, the data will be the cached data if it exists, otherwise null, and the status will be 200.
 * Otherwise, the data will be null and the status will be the status.
 */

export function handleCachedDetails<T extends CacheType, DataType = T extends 'asn' ? AsnDetails : T extends 'order' ? OrderDetails : T extends 'product' ? LiveProduct : never>(
  status: number,
  type: T,
  entryId: number
): { data: DataType, status: number } {

  const cache: DetailsCache
  = localStorage.getItem( 'details-cache' ) // ------------------------------------------ Check if there is a cache
    ? JSON.parse( localStorage.getItem( 'details-cache' )) // --------------------------- Parse the cache
    : {} as DetailsCache // ------------------------------------------------------------- If there is no cache, create an empty object as DetailsCache

  if ( status === 200 ) { // ------------------------------------------------------------ If the status is 200

    if ( cache[type] ) { // ------------------------------------------------------------- If there is a cache for the type

      cache[type] = cache[type].filter( entry => entry.id !== entryId ) // -------------- Remove the entry from the cache
      localStorage.setItem( 'details-cache', JSON.stringify( cache )) // ---------------- Save the cache

    }

    return {
      data: null,
      status
    }

  }

  else if ( status >= 400 && status < 500 ) { // --------------------------------------- If the status is in the range of 400s

    const cacheData = cache[type]?.find( entry => entry.id === entryId ) || null // ---- Get the cached data for the entry

    return {
      data:   cacheData?.data as DataType ?? null, // ---------------------------------- Return the cached data if it exists
      status: cacheData ? 200 : status // ---------------------------------------------- Return a status of 200 ( This is important so the view setup doesn't redirect to the 404 page )
    }

  }

  return {
    data: null,
    status
  }

}

/**
 * Clears the old cache entries.
 */

export function clearOldCacheEntries<T extends AsnDetails | OrderDetails | LiveProduct>() {

  const cache: DetailsCache = localStorage.getItem( 'details-cache' )
    ? JSON.parse( localStorage.getItem( 'details-cache' ))
    : {} as DetailsCache

  const oneHourInMilliseconds = 60 * 60 * 1000
  const now = new Date().getTime()

  Object.keys( cache ).forEach(( cacheType ) => {
    cache[cacheType] = cache[cacheType].filter(( entry: DetailsCacheEntry<T> ) => ( now - entry.timestamp ) < oneHourInMilliseconds )
  })

  localStorage.setItem( 'details-cache', JSON.stringify( cache ))

}
