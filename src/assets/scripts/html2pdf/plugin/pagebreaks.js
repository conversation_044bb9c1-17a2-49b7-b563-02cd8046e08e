import Worker from '../worker.js'
import { createElement } from '../utils.js'

/* Pagebreak plugin:

    Adds page-break functionality to the html2pdf library. Page-breaks can be
    enabled by CSS styles, set on individual elements using selectors, or
    avoided from breaking inside all elements.

    Options on the `opt.pagebreak` object:

    mode:   String or array of strings: 'avoid-all', 'css', and/or 'legacy'
            Default: ['css', 'legacy']

    before: String or array of CSS selectors for which to add page-breaks
            before each element. Can be a specific element with an ID
            ('#myID'), all elements of a type (e.g. 'img'), all of a class
            ('.myClass'), or even '*' to match every element.

    after:  Like 'before', but adds a page-break immediately after the element.

    avoid:  Like 'before', but avoids page-breaks on these elements. You can
            enable this feature on every element using the 'avoid-all' mode.
*/

// Refs to original functions.
const orig = {
  toContainer: Worker.prototype.toContainer
}

// Add pagebreak default options to the Worker template.
Worker.template.opt.pagebreak = {
  mode:   [ 'css', 'legacy' ],
  before: [],
  after:  [],
  avoid:  []
}

Worker.prototype.toContainer = function toContainer() {
  return orig.toContainer.call( this ).then( function toContainer_pagebreak() {
    // Setup root element and inner page height.
    const root = this.prop.container
    const pxPageHeight = this.prop.pageSize.inner.px.height

    // Check all requested modes.
    const modeSrc = [].concat( this.opt.pagebreak.mode )
    const mode = {
      avoidAll: modeSrc.includes( 'avoid-all' ),
      css:      modeSrc.includes( 'css' ),
      legacy:   modeSrc.includes( 'legacy' )
    }

    // Get arrays of all explicitly requested elements.
    const select = {}
    const self = this;
    [ 'before', 'after', 'avoid' ].forEach(( key ) => {
      const all = mode.avoidAll && key === 'avoid'
      select[key] = all ? [] : [].concat( self.opt.pagebreak[key] || [] )
      if ( select[key].length > 0 ) {
        select[key] = Array.prototype.slice.call(
          root.querySelectorAll( select[key].join( ', ' ))
        )
      }
    })

    // Get all legacy page-break elements.
    let legacyEls = root.querySelectorAll( '.html2pdf__page-break' )
    legacyEls = Array.prototype.slice.call( legacyEls )

    // Loop through all elements.
    const els = root.querySelectorAll( '*' )
    Array.prototype.forEach.call( els, ( el ) => {
      // Setup pagebreak rules based on legacy and avoidAll modes.
      let rules = {
        before: false,
        after:  mode.legacy && legacyEls.includes( el ),
        avoid:  mode.avoidAll
      }

      // Add rules for css mode.
      if ( mode.css ) {
        // TODO: Check if this is valid with iFrames.
        const style = window.getComputedStyle( el )
        // TODO: Handle 'left' and 'right' correctly.
        // TODO: Add support for 'avoid' on breakBefore/After.
        const breakOpt = [ 'always', 'page', 'left', 'right' ]
        const avoidOpt = [ 'avoid', 'avoid-page' ]
        rules = {
          before: rules.before || breakOpt.includes( style.breakBefore || style.pageBreakBefore ),
          after:  rules.after || breakOpt.includes( style.breakAfter || style.pageBreakAfter ),
          avoid:  rules.avoid || avoidOpt.includes( style.breakInside || style.pageBreakInside )
        }
      }

      // Add rules for explicit requests.
      Object.keys( rules ).forEach(( key ) => {
        rules[key] = rules[key] || select[key].includes( el )
      })

      // Get element position on the screen.
      // TODO: Subtract the top of the container from clientRect.top/bottom?
      const clientRect = el.getBoundingClientRect()

      // Avoid: Check if a break happens mid-element.
      if ( rules.avoid && !rules.before ) {
        const startPage = Math.floor( clientRect.top / pxPageHeight )
        const endPage = Math.floor( clientRect.bottom / pxPageHeight )
        const nPages = Math.abs( clientRect.bottom - clientRect.top ) / pxPageHeight

        // Turn on rules.before if the el is broken and is at most one page long.
        if ( endPage !== startPage && nPages <= 1 ) {
          rules.before = true
        }
      }

      // Before: Create a padding div to push the element to the next page.
      if ( rules.before ) {
        const pad = createElement( 'div', { style: {
          display: 'block',
          height:  `${pxPageHeight - ( clientRect.top % pxPageHeight )}px`
        } })
        el.parentNode.insertBefore( pad, el )
      }

      // After: Create a padding div to fill the remaining page.
      if ( rules.after ) {
        const pad = createElement( 'div', { style: {
          display: 'block',
          height:  `${pxPageHeight - ( clientRect.bottom % pxPageHeight )}px`
        } })
        el.parentNode.insertBefore( pad, el.nextSibling )
      }
    })
  })
}
