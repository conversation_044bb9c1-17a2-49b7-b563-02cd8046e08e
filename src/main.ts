import { t } from '@/localization/t'
import { tooltip } from '@lib/directives/tooltip'
import { tGlobal } from '@lib/localization/tGlobal'
import { createApp } from 'vue'
import { createI18n } from 'vue-i18n'
import { msalPlugin } from '@lib/auth/scripts/msalPlugin'
import { mergeObjects } from '@lib/scripts/utils'
import { cookieConsent } from '@/store'
import { msalInstance, setupMsal } from '@lib/auth/scripts/authConfig'

import Main from '@/Main.vue'
import router from '@/router'
import Clarity from '@microsoft/clarity'

import '@/main.css'
import '@lib/css/fonts.css'
import '@carbon/charts/styles.css'

/*
 * Initialize Clarity before mounting the app
 */

Clarity?.init( import.meta.env.VITE_CLARITY_PID )
Clarity.consent( cookieConsent.value )

/*
 * Initialize MSAL before mounting the app
 */

msalInstance.initialize().then(() => {

  setupMsal( msalInstance )

  const app     = createApp( Main )
  const tMerged = mergeObjects( t, tGlobal )

  const i18n = createI18n({
    legacy:          false,
    locale:          'en',
    messages:        tMerged,
    fallbackLocale:  'en',
    globalInjection: true
  })

  app.use( i18n )
  app.use( router )
  app.use( msalPlugin, msalInstance )
  app.directive( 'tooltip', tooltip )
  app.mount( '#app' )

})
