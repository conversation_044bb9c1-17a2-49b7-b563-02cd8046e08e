export const t = {
  en: {
    navigation: {
      asnDocsLabel:        'ASN Docs',
      myOWDDocsLabel:      'My OWD Docs',
      dashboardLabel:      'Dashboard',
      ordersDocsLabel:     'Orders Docs',
      bulkUploadOrders:    'Bulk Upload - Orders',
      bulkInstructions:    'Bulk Upload Instructions',
      dashboardDocsLabel:  'Dashboard Docs',
      inventoryDocsLabel:  'Inventory Docs',
      documentationLabel:  'Documentation',
      bulkUploadInventory: 'Bulk Upload - Inventory',
      orders:              {
        label:       'Orders',
        summary:     '@:orders.summary.tabLabel',
        liveOrders:  '@:orders.liveOrders.tabLabel',
        createOrder: 'Create Order',
        yourOrders:  'Your Orders'
      },
      asn: {
        label:     'ASNs',
        yourASN:   'Your ASNs',
        createASN: 'Create ASN',
      },
      inventory: {
        label:         'Inventory',
        yourInventory: 'Your Products',
        createProduct: 'Create Product'
      },
      externalTools: {
        label:         'External Tools',
        reportsLabel:  'Reports',
        extranetLabel: 'Extranet',
      },
    },
    dashboard: {
      greeting: {
        hello:   'Hello',
        message: ', see what\'s new today.'
      },
      fulfillment: {
        statusLabel: 'Fulfillment Status',
        KPIs:        {
          header:         'Fulfillment KPIs',
          ordersLabel:    'Today\'s Shipped Orders',
          asnLabel:       'ASNs Arrived',
          inventoryLabel: 'Out Of Stock Inventory'
        },
      },
      operationalStatus: {
        header: 'Operational Status',
        // do we need to translate the operational status per facility coming from an API?
      },
      newsAndAnnouncements: {
        header:       'News and Announcements',
        actionButton: 'Read More'
      },
    },
    orders: {
      summary: {
        tabLabel:     'Summary',
        todaysOrders: {
          header:           'Today\'s Orders',
          atWarehouseLabel: 'At Warehouse',
          shippedLabel:     'Shipped',
          fulfillmentLabel: 'Fulfillment Status',
        },
        ordersByChannel: {
          header:     'Orders by Channel',
          donutChart: {
            mainLabel:   'Orders',
            othersLabel: 'Others'
          }
        },
        topSellingItems: {
          header:  'Top Selling Items',
          rank:    'Rank',
          noItems: 'There are no top selling items at the moment.'
        },
        shippedOrders: {
          header:            'Shipped Orders',
          filterPlaceholder: 'Filter by Channel',
        },
        ordersByShipMethod: {
          header:            'Orders by Ship Method',
          filterPlaceholder: 'Filter by Method'
        },
        rolling:        'Rolling',
        days:           '{num} Days',
        filterAllLabel: 'All',
      },
      liveOrders: {
        tabLabel: 'Live Orders',
        buttons:  {
          newOrder: 'New Order'
        }
      },
      draftOrders: {
        tabLabel:          'Draft Orders',
        columnLabel:       {},
        searchFilterLabel: {},
        buttons:           {
          bulkUpload: 'Bulk Upload',
          publish:    'Publish Selected Orders'

        }
      },
      details: {
        bypassAvsCheckboxText: 'Bypass AVS',
        notes:                 'Notes',
        comments:              'Comments',
        eventsAndComments:     'Events and Comments',
        commentsTextBoxLabel:  'Write your comments here',
        detailsText:           'Details'
      },
      searchFilterLabel: {
        createdStartDate:      'From Date Created',
        createdEndDate:        'To Date Created',
        shippedStartDate:      'From Date Shipped',
        shippedEndDate:        'To Date Shipped',
        facility:              '@:orders.columnLabel.facility',
        sku:                   'SKU',
        clientReferenceNumber: 'Client Reference Number',
        owdReferenceNumber:    'OWD Reference Number',
        customerState:         'Customer State',
        customerFirstName:     'Customer First Name',
        customerLastName:      'Customer Last Name',
        customerEmail:         'Customer Email',
        trackingNumber:        'Tracking Number',
        poNumber:              'PO Number',
        onHold:                'On Hold',
        isShipped:             'Is Shipped',
        atWarehouse:           '@:orders.summary.todaysOrders.atWarehouseLabel',
        backOrderStatus:       'Backordered',
        isVoided:              'Voided',
        startDate:             'Start Date',
        endDate:               'End Date',
        filterByStatus:        'Filter By Status',
        shipped:               'Shipped',
        dateCreated:           'Date Created',
        dateShipped:           'Date Shipped',
        isBusinessOrder:       'Is Business Order',
        isConsumerOrder:       'Is Consumer Order',
        hasErrors:             'Has Errors',
        hasMasterRecord:       'Has Master Record'
      },
      columnLabel: {
        owdReference:    'OWD Reference',
        clientReference: 'Client Reference',
        facility:        'Facility',
        firstName:       'First Name',
        lastName:        'Last Name',
        orderStatus:     'Order Status',
        receivedDate:    'Received Date',
        shipDate:        'Ship Date',
        orderTotal:      'Order Total',
        notes:           '@:orders.details.notes',
        orderType:       'Order Type',
        shippingMethod:  'Shipping Method'
      },
    },
    inventory: {
      details: {
        availableStockLevelsTitle: 'Available Stock Levels',
        knownLotQuantitiesTitle:   'Known Lot Quantities',
        lotQuantity:               'Lot Quantity',
        lotNumber:                 'Lot Number',
      }
    },
    asn: {
      details: {
        confirmCloseModal: {
          header:  'Are you sure you want to exit ASN creation?',
          message: 'Exiting now will result in the loss of all entered data.'
        }
      }
    },
    profile: {
      header:       'profile',
      viewAllLabel: 'View All',
      label:        {
        contactName: 'Contact Name',
        email:       'Email',
        cookies:     'Cookie Preferences'
      },
      billingHistory: {
        header:     'Billing History',
        loaderName: 'Invoices',
      },
      contracts: {
        header:     'Contracts',
        loaderName: 'Contracts'
      },
      cookies: {
        header: 'Consent Preferences Center',
        usage:  {
          title:   'Cookie Usage',
          content: {
            p1: 'Cookies are small data files used to store information on your device. They enhance your browsing experience by allowing websites to remember your actions and preferences.',
            p2: 'Here, you can customize how we use cookies to interact with your device.',
          }
        },
        necessary: {
          title:   'Strictly Necessary Cookies',
          label:   'Always Enabled',
          content: 'These are cookies that the app uses internally in order to work properly. They are essential for you to browse the app and use its features, such as accessing secure areas of the site or remembering user preferences.'
        },
        analytics: {
          title:   'Analytics Cookies',
          content: {
            p1:        'We partner with Microsoft Clarity to capture how you use and interact with our website through behavioral metrics, heatmaps, and session replay to improve and market our products/services.',
            p2:        'Website usage data is captured using first and third-party cookies and other tracking technologies to determine the popularity of products/services and online activity. Additionally, we use this information for site optimization and fraud/security purposes.',
            p3:        'For more information about how Microsoft collects and uses your data, visit the',
            linkLabel: 'Microsoft Privacy Statement'
          }
        },
        moreInfo: {
          title:     'More Information',
          content:   'For more information about our usage of cookies and trackers, or if you have any questions, please read our',
          linkLabel: 'Privacy Policy'
        },
        actionButtons: {
          save:   'Save Preferences',
          reject: 'Reject All',
          accept: 'Accept All'
        }
      },
      documents: {
        billing: {
          header: 'Billing History',
          name:   'Invoices'
        },
        contracts: {
          header: 'Contracts',
          name:   'Contracts'
        },
        label: {
          dateFrom: 'From',
          dateTo:   'To',

        }
      }
    },
    announcements: {
      header:      'News and Announcements',
      newButton:   'New',
      closeButton: 'Done',
      drawer:      {
        loaderName:      'News and Announcements',
        noAnnouncements: 'No news or announcements available at this time.'
      },
      tableView: {
        filter: {
          status: {
            name:  'Status',
            label: 'Filter By Status'
          },
        },
        columnLabel: {
          id:          'ID',
          author:      'Author',
          visibility:  'Visibility',
          file:        'File',
          status:      'Status',
          dateCreated: 'Date',
        },
        rowMenuTooltip: {
          preview: 'Preview',
          details: 'Details',
          delete:  'Delete'
        },
        deleteModal: {
          header:      'Delete Announcement',
          description: 'Are you sure you want to delete this announcement?'
        }
      }
    },
  },
}
