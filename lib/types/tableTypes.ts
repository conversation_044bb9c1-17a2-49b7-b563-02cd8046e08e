import type { IconName } from '@lib/store/icon'

export interface TableReferenceCell {
  hide?:   boolean
  index:   number
  label:   string | TableSchemaItemComponent
  align?:  'left' | 'center' | 'right'
  toggle?: boolean
  sizes: {
    init: number
    flex: number
    drag: number
  }
  lockFlex?: boolean
}

export interface TableReference {
  name:  string
  cells: TableReferenceCell[]
}

export interface StorageTableData {
  route:  string
  tables: TableReference[]
}

export interface RowClickPayload<R> {
  record:   R
  isChild:  boolean
  isParent: boolean
}

export interface BatchOption<T> {
  id:              number
  icon?:           IconName
  type?:           'positive' | 'negative' | 'neutral'
  group:           LooseAutoComplete<'Bulk Actions' | 'Export'>
  filter?:         ( record: T ) => boolean
  action?:         ( records: T[] ) => void | Promise<void>
  actionName:      string
  description?:    string
  pendingMessage?: string
}
