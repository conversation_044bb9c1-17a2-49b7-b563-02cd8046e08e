export type InputType = 'text' | 'number' | 'email' | 'password' | 'phone' | 'file' | 'select' | 'date' | 'toggle' | 'checkbox' | 'textbox' | 'currency' | 'strict-number'
export type DateFormat = 'MM-DD-YYYY' | 'ISO-string' | 'UTC-date' | 'UTC-date-time'
export type CurrencyCode = 'USD' | 'EUR'

export interface InputValidationOptions {
  min?:      number
  max?:      number
  name?:     string
  type?:     InputType
  match?:    string
  regex?:    RegExp
  required?: boolean
}

export interface BaseInputProps<ModelType> extends InputValidationOptions {
  size?:           UtilsSize
  mode?:           Exclude<UtilsMode, 'secondary' | 'skinny'>
  label?:          string
  timeout?:        number
  padding?:        boolean
  debounce?:       number
  disabled?:       boolean
  readonly?:       boolean
  modelValue?:     ModelType
  customError?:    string
  placeholder?:    string
  debouncedValue?: ModelType
}

export interface ValidationWrapperProps<ModelType> extends BaseInputProps<ModelType> {
  valid?:    boolean
  timeout?:  number
  required?: boolean
}

export interface LabelProps<ModelType> {
  input:        ModelType
  push?:        boolean
  size?:        UtilsSize
  name?:        string | null | undefined
  label?:       string | null | undefined
  required?:    boolean
  forceFocus?:  boolean
  placeholder?: string | null | undefined
}

export interface InputWrapperProps<ModelType> extends BaseInputProps<ModelType> {
  regex?:        RegExp
  valid?:        boolean
  focused?:      boolean
  padding?:      boolean
  lockLabel?:    boolean
  disableFocus?: boolean
}

export interface TextInputProps<ModelType> extends BaseInputProps<ModelType> {
  type?:         'text' | 'email' | 'password' | 'number' | 'phone' | 'currency' | 'strict-number'
  /** Tokens: # number | @ letter | * letters & digits */
  mask?:         string
  regex?:        RegExp
  align?:        'left' | 'center' | 'right'
  currency?:     CurrencyCode
  autocomplete?: boolean
}

export interface UploadInputProps<ModelType> extends BaseInputProps<ModelType>, ValidationWrapperProps<ModelType> {
  pending?:      boolean
  existingFile?: string
  fileType?:     string
}

export interface TextboxProps<ModelType> extends BaseInputProps<ModelType> {
  resize?: boolean
}

export interface SelectInputProps<ModelType> extends BaseInputProps<ModelType> {
  options:       DropListOption[]
  nullable?:     boolean
  multiple?:     boolean
  teleport?:     boolean
  showButton?:   boolean
  returnType?:   'id' | 'name'
  listMaxWidth?: string | number
  booleanModel?: boolean
}

export interface DatePickerProps<ModelType> extends BaseInputProps<ModelType> {
  range?:             boolean
  limitTo?:           Date | string
  nullable?:          boolean
  teleport?:          boolean
  limitFrom?:         Date | string
  showButton?:        boolean
  modelValue?:        ModelType
  returnType?:        'date' | DateFormat
  listMaxWidth?:      string | number
  calendarStartFrom?: 'sunday' | 'monday'
}

export interface CheckboxInputProps<ModelType> extends BaseInputProps<ModelType> {
  radio?:   boolean
  reverse?: boolean
  partial?: boolean
}

export interface RadioInputProps<ModelType> extends CheckboxInputProps<ModelType> {
  valid?:      boolean
  options?:    Pick<DropListOption, 'id' | 'name' | 'disabled' | 'hidden'>[]
  nullable?:   boolean
  direction?:  'horizontal' | 'vertical'
  returnType?: 'id' | 'name'
}
