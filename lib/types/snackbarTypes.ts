export type SnackbarType = 'alert' | 'notification'
export type AlertSeverity = 'info' | 'success' | 'warning' | 'error'
export type NotificationSeverity = 'info' | 'success'

export interface AlertOptions<T extends 'notification' | 'alert' = 'alert'> {
  id?:               string
  open:              boolean
  strict?:           boolean
  action?:           () => void
  message?:          string
  details?:          string
  duration?:         number
  severity?:         T extends 'alert' ? AlertSeverity : NotificationSeverity
  timeStamp?:        string
  actionName?:       string
  inlineAction?:     () => void
  inlineActionName?: string
}

export type NotificationOptions = AlertOptions<'notification'>

export interface SnackbarProps extends Omit<AlertOptions, 'open'> {
  type:             SnackbarType
  pause?:           boolean
  modifier?:        LooseAutoComplete<'inverse'>,
  showCloseButton?: boolean
}
