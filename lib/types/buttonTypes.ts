import type { IconProps } from '@lib/store/icon'
import type { RouteLocationRaw } from 'vue-router'

export type ButtonType = 'rect' | 'pill' | 'box' | 'badge'

export interface ButtonProps {
  id?:              string
  size?:            UtilsSize
  icon?:            IconProps
  mode?:            UtilsMode
  type?:            ButtonType
  label?:           string
  options?:         DropListOption[]
  pending?:         boolean
  isActive?:        boolean
  disabled?:        boolean
  tabindex?:        number
  teleportList?:    boolean
  preventDefault?:  boolean
  stopPropagation?: boolean
  toggleOptionsOn?: 'click' | 'hover'
}

export interface TabProps extends ButtonProps {
  to?: RouteLocationRaw
}

interface RadioButtonOption {
  id:        string | number
  link?:     RouteLocationRaw
  label:     string
  action?:   () => void
  disabled?: boolean
}

export interface RadioButtonProps {
  options:     RadioButtonOption[]
  modelValue?: string | number
}
