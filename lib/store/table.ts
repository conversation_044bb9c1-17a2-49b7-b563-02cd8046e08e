import { ref } from 'vue'
import { checkValue, formatCurrency, formatDate } from '@lib/scripts/utils'

import type { RouteLocationNormalizedLoaded } from 'vue-router'
import type { StorageTableData, TableReference } from '@lib/types/tableTypes'

export const pageSize = ref<number>( localStorage.getItem( 'pageSize' ) ? Number.parseInt( localStorage.getItem( 'pageSize' )) : 25 )

/**
 * Formats the value of a record based on the provided record schema
 * @param {R} record - The record object
 * @param {TableSchemaItem<R>} recordSchema - The schema of the record
 * @returns {string} - The formatted value
 */

export function formatRecordValue<R>( record: R, recordSchema: TableSchemaItem<R>, emptyRecord: string = '/' ): string | boolean | TableSchemaItemComponent {

  // If there is a static value provided in the record schema, return it
  // Else extract the value from the record object using the key provided in the schema.

  const value = checkValue( recordSchema?.value, { checkBoolean: false })
    ? recordSchema.value
    : record[recordSchema.key] as string | number | boolean | TableSchemaItemComponent

  // If the raw value is null or undefined, return the empty record string

  const emptyValue = `<span class="text-core-50" >${emptyRecord}</span>`

  if ( !checkValue( value, { checkBoolean: false }))
    return emptyValue

  // If the value is a component, return it as a TableSchemaItemComponent

  if ( typeof value === 'object' && value.hasOwnProperty( 'component' ))
    return value

  if ( typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean' ) {

    const tValue = String( value )
    const nValue = Number.parseFloat( String( value ))
    const format = recordSchema?.format ?? null

    if ( !format )
      return tValue

    if ( format === 'date' )
      return formatDate( tValue, 'MMM DD, YYYY' )

    if ( format === 'currency' )
      return formatCurrency( nValue )

    if ( format === 'custom' ) {

      if ( recordSchema.transform ) {

        const transformedValue = recordSchema.transform( typeof value === 'boolean' ? value : tValue )
        return checkValue( transformedValue, { checkBoolean: false }) ? transformedValue : emptyValue

      }

      return tValue

    }

  }

}

/**
 * Saves the table reference to local storage
 * @param {TableReference} tableReference - The reference to the table to be saved
 * @param {RouteLocationNormalizedLoaded} route - The route path where the table is used
 */

export function saveTableToStorage( tableReference: TableReference, route: RouteLocationNormalizedLoaded ) {

  // Retrieve the stored tables from local storage

  let storedTables = JSON.parse( localStorage.getItem( 'table-data' )) as StorageTableData[]

  // If no stored tables exist, create a new entry and save it to local storage

  if ( !storedTables ) {

    storedTables = [ { route: route.path, tables: [ tableReference ] } ]
    localStorage.setItem( 'table-data', JSON.stringify( storedTables ))
    return

  }

  // Find the tables for the current route, if they exist

  const routeTables = storedTables.find( t => t.route === route?.path )?.tables

  // If no tables exist for the current route, create a new entry and save it to local storage

  if ( !routeTables ) {

    storedTables.push({ route: route.path, tables: [ tableReference ] })
    localStorage.setItem( 'table-data', JSON.stringify( storedTables ))
    return
  }

  // Find the table reference within the route tables

  const table = routeTables.find( t => t.name === tableReference.name )

  // If the table does not exist, add it to the route tables

  if ( !table ) {

    storedTables.forEach(( t ) => {

      if ( t.route === route?.path )
        t.tables.push( tableReference )

    })

    localStorage.setItem( 'table-data', JSON.stringify( storedTables ))
    return

  }

  // If the table exists, update it in the route tables

  storedTables.forEach(( t ) => {

    if ( t.route === route.path ) {

      const index = t.tables.findIndex( t => t.name === tableReference.name )
      t.tables[index] = tableReference

    }

  })

  localStorage.setItem( 'table-data', JSON.stringify( storedTables ))

}

/**
 * Removes the table reference from local storage.
 * @param {TableReference} tableReference - The reference to the table to be removed.
 * @param {RouteLocationNormalizedLoaded} route - The route path where the table is used.
 */

export function removeTableFromStorage( tableReference: TableReference, route: RouteLocationNormalizedLoaded ) {

  // Retrieve the stored tables from local storage

  const storedTables = JSON.parse( localStorage.getItem( 'table-data' )) as StorageTableData[]

  if ( !storedTables ) // --------------- If no stored tables exist, return
    return

  // Find the tables for the current route, if they exist

  const routeTables = storedTables.find( t => t.route === route?.path )?.tables

  if ( !routeTables ) // ---------------- If no tables exist for the current route, return
    return

  // Find the table reference within the route tables

  const table = routeTables.find( t => t.name === tableReference.name )

  if ( !table ) // ---------------------- If the table does not exist, return
    return

  storedTables.forEach(( t ) => { // ---- Remove the table from the route tables

    if ( t.route === route.path ) {

      const index = t.tables.findIndex( t => t.name === tableReference.name )
      t.tables.splice( index, 1 )

    }

  })

  // Save the updated tables to local storage

  localStorage.setItem( 'table-data', JSON.stringify( storedTables ))

}

/**
 * Type helper to extract the record type from a paginated response or a record.
 */
type ExtractRecordType<PayloadType, K extends string> =
  PayloadType extends PaginatedResponse<K, infer R>
    ? R
    : PayloadType extends Record<K, infer B>
      ? B
      : PayloadType

/**
 * Remaps a response to a nested request payload.
 * This is a helper utility for the Table nested async mode.
 * It takes a payload, error, and status code and returns a paginated response.
 *
 * @param {PayloadType} payload - The payload to be remapped.
 * @param {PayloadError[]} error - The error from the original payload.
 * @param {number} status - The status code of the original payload.
 * @param {Key} key - The key to be used for remapping. If not provided, the payload will be treated as a record.
 * It can be a key to extract the records from paginated response, or from a payload that has the records nested in an object.
 *
 * @returns {Payload<PaginatedResponse<'records', ExtractRecordType<PayloadType, Key>>>} - The remapped payload.
 */
export function remapToNestedRequestPayload<PayloadType extends PaginatedResponse<string, any> | Record<string, any>, Key extends string>(

  payload: PayloadType,
  error: PayloadError[],
  status: number,
  key?: Key

): Awaited<ReturnType<NestedRequest<ExtractRecordType<PayloadType, Key>>>> {

  // If the payload is null or undefined,
  // return empty payload with the original request error and status.
  // This is necessary for handling errors in the nested view.

  if ( !payload )
    return { payload: null, error, status }

  // Default pagination options,
  // to be used when the payload does not have pagination properties.

  const paginationOptions: PaginationOptions = {
    nextPage:    0,
    pageSize:    0,
    totalRows:   0,
    isLastPage:  false,
    totalPages:  0,
    currentPage: 0,
  }

  // If the payload is a paginated response, or an object with nested records,
  // check if the payload has the key provided.
  // If it does, return the payload with the records and pagination options.

  if ( payload.hasOwnProperty( key as Key )) {

    return {
      payload: {
        records:     payload[key],
        nextPage:    payload?.nextPage ?? paginationOptions.nextPage,
        pageSize:    payload?.pageSize ?? paginationOptions.pageSize,
        totalRows:   payload?.totalRows ?? paginationOptions.totalRows,
        isLastPage:  payload?.isLastPage ?? paginationOptions.isLastPage,
        totalPages:  payload?.totalPages ?? paginationOptions.totalPages,
        currentPage: payload?.currentPage ?? paginationOptions.currentPage,
      },
      error,
      status,
    }

  }

  // If the payload is a list of records,
  // return the payload with the records and pagination options.

  return {
    payload: {
      records: payload as ExtractRecordType<PayloadType, Key>,
      ...paginationOptions,
    },
    error,
    status,
  }

}
