import { ref } from 'vue'

import type { ConfirmOptions } from '@lib/types/confirmTypes'

const defaultConfirmOptions: ConfirmOptions = {
  open:           false,
  strict:         true,
  header:         'Confirm',
  action:         null,
  description:    'Are you sure?',
  cancelCtaText:  'Cancel',
  confirmCtaText: 'Confirm',
}

export const confirmOptions = ref<ConfirmOptions>( defaultConfirmOptions )

export function confirm( options: Omit<ConfirmOptions, 'open'> ) {
  confirmOptions.value = { ...confirmOptions.value, ...options }
  confirmOptions.value.open = true
}

export function resetConfirm() {
  confirmOptions.value = defaultConfirmOptions
}
