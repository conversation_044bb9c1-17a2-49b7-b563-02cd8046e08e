import {
  BrowserCacheLocation,
  EventType,
  LogLevel,
  PublicClientApplication,
} from '@azure/msal-browser'

import type { TokenClaims } from '@azure/msal-common'
import type { AuthenticationResult, Configuration } from '@azure/msal-browser'

const baseURL = import.meta.env.VITE_B2C_BASE_URL ?? window.location.origin

// Config object to be passed to <PERSON><PERSON> on creation

export const msalConfig: Configuration = {

  auth: {
    clientId:                  import.meta.env.VITE_B2C_CLIENT_ID,
    authority:                 import.meta.env.VITE_B2C_AUTHORITY,
    redirectUri:               `${baseURL}/auth-success`, // Must be registered as a SPA redirectURI on your app registration
    knownAuthorities:          [ import.meta.env.VITE_B2C_KNOWN_AUTHORITIES ],
    navigateToLoginRequestUrl: true
  },

  cache: {
    cacheLocation:          BrowserCacheLocation.SessionStorage,
    storeAuthStateInCookie: false,
  },

  system: {
    loggerOptions: {
      loggerCallback: (
        level: LogLevel,
        message: string,
        containsPii: boolean
      ) => {

        if ( containsPii )
          return

        switch ( level ) {
          case LogLevel.Error:
            console.error( message )
            return
          case LogLevel.Info:
            return
          case LogLevel.Verbose:
            return
          case LogLevel.Warning:
            console.warn( message )
            break

        }
      },
      logLevel: LogLevel.Verbose,
    },
  },

  telemetry: {
    application: {
      appName:    import.meta.env.VITE_B2C_APP_NAME,
      appVersion: '1.0.0',
    },
  }

}

export const msalInstance = new PublicClientApplication( msalConfig )

// Add here scopes for id token to be used at MS Identity Platform endpoints.

export const loginRequest = {

  scopes: (() => {

    const b2cType = import.meta.env.VITE_B2C_TYPE || ''
    const scopeString = import.meta.env.VITE_B2C_SCOPE || ''
    const scopes = scopeString.split( ',' ).map(( scope: string ) => scope.trim()).filter( Boolean )

    const essentialScopes = [ ...scopes, `${b2cType === 'policy' ? import.meta.env.VITE_B2C_CLIENT_ID : ''}` ]

    essentialScopes.forEach(( scope ) => {

      if ( !scopes.includes( scope ))
        scopes.push( scope )

    })

    return scopes

  })()
}

export function setupMsal( instance: PublicClientApplication ) {

  const accounts = instance.getAllAccounts()

  if ( accounts.length > 0 )
    instance.setActiveAccount( accounts[0] )

  instance.addEventCallback(( event ) => {

    if ( event.eventType === EventType.LOGIN_SUCCESS && event.payload ) {

      const payload = event.payload as AuthenticationResult
      const account = payload.account

      instance.setActiveAccount( account )

    }

    else if ( event.eventType === EventType.LOGOUT_END || event.eventType === EventType.LOGOUT_SUCCESS ) {
      localStorage.removeItem( 'userName' )
    }

  })

}

/**
 * Returns active user id
 */

export function getUserClaims(): TokenClaims {
  return msalInstance.getActiveAccount()?.idTokenClaims
}
