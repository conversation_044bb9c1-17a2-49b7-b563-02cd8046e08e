import { InteractionStatus } from '@azure/msal-browser'
import { getCurrentInstance, toRefs } from 'vue'

import type { Ref } from 'vue'
import type { AccountInfo, PublicClientApplication } from '@azure/msal-browser'

export interface MsalContext {
  instance:   PublicClientApplication
  accounts:   Ref<AccountInfo[]>
  inProgress: Ref<InteractionStatus>
}

export function useMsal(): MsalContext {

  const internalInstance = getCurrentInstance()

  if ( !internalInstance )
    throw new Error ( 'useMsal() cannot be called outside the setup() function of a component' )

  const { instance, accounts, inProgress } = toRefs( internalInstance.appContext.config.globalProperties.$msal )

  if ( !instance?.value || !accounts?.value || !inProgress?.value )
    throw new Error ( 'Please install the msalPlugin' )

  if ( inProgress.value === InteractionStatus.Startup )
    instance.value.handleRedirectPromise()

  return {
    instance: instance.value,
    accounts,
    inProgress
  }

}
