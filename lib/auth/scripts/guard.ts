import { ref } from 'vue'
import { loginRequest, msalInstance } from '@lib/auth/scripts/authConfig'

import type { RouteLocationNormalized, Router } from 'vue-router'
import type { PublicClientApplication, RedirectRequest } from '@azure/msal-browser'

export const scopes = ref( null )

/**
 * Registers the guard to the router.
 * @param router - The router instance.
 */

export function registerGuard<ScopeModel, AccessScope extends string = any>(

  router: Router,
  accessGuard?: AccessGuard<ScopeModel, AccessScope>,
  scopesProvider?: ScopesProvider<ScopeModel>

): void {

  // Check if the user is authenticated and has
  // the required permissions before each route.

  router.beforeEach( async ( to: RouteLocationNormalized ) => {

    const routeMetadata = to.meta as RouteModelMeta<Record<string, any>, AccessScope>

    if ( typeof to.meta.title === 'string' )
      document.title = to.meta.title || 'OWD Client Portal'

    if ( routeMetadata?.requiresAuth ) {

      const request = {
        ...loginRequest,
        redirectStartPage: to.fullPath,
      }

      const accessScope   = routeMetadata?.scope
      const shouldProceed = await isAuthenticated( msalInstance, request, accessScope, accessGuard, scopesProvider )

      return shouldProceed

    }

    return true

  })

}

/**
 * Checks if the user is authenticated and has the required permissions.
 *
 * @param instance - The MSAL instance.
 * @param loginRequest - The login request.
 * @param accessScope - The access scope.
 * @param accessGuard - The access guard.
 * @returns A boolean value or a string to redirect the user.
 */

export async function isAuthenticated<ScopeModel, AccessScope extends string = any>(

  instance: PublicClientApplication,
  loginRequest: RedirectRequest,
  accessScope?: AccessScope | AccessScope[],
  accessGuard?: AccessGuard<ScopeModel, AccessScope>,
  scopeProvider?: ScopesProvider<ScopeModel>

): Promise<boolean | void | string> {

  return instance.handleRedirectPromise().then( async () => {

    try {

      const { idToken } = instance.getActiveAccount()

      if ( !scopes.value && scopeProvider ) {

        const { error, payload } = await scopeProvider( idToken )
        scopes.value = payload

        if ( error )
          instance.logoutRedirect()

      }

      // If there is a token available, check
      // if the user has the required permissions.

      const hasAccess = accessGuard ? accessGuard( accessScope, scopes.value, instance ) : true

      // If the user doesn't have the required permissions,
      // redirect the user to the access denied page.

      if ( !hasAccess )
        return '/access-denied'

      // Else allow the user to proceed.

      return true

    }

    // If there is no token available,
    // redirect the user to the login page.

    catch { await instance.loginRedirect( loginRequest ) }

  }).catch( async () => await instance.loginRedirect( loginRequest ))

}
