<script setup lang="ts">

import { computed } from 'vue'

import type { RadioButtonProps } from '@lib/types/buttonTypes'

const props = defineProps<RadioButtonProps>()

const emits = defineEmits<{
  'update:modelValue': [ value: string | number ]
}>()

const input = computed<string | number>({
  get: () => props.modelValue,
  set: value => emits( 'update:modelValue', value )
})

function handleClick( option: RadioButtonProps['options'][0] ) {

  if ( option.link )
    return

  input.value = option.id

  if ( !option?.action )
    return

  option.action()

}

</script>

<template>

  <div class="flex items-center">

    <Component
      :is="option.link ? 'router-link' : 'button'"
      v-for="option in options"
      :key="option.id"
      :to="option.link"
      class="px-4 h-6 last:rounded-r first:rounded-l flex items-center"
      :class="{
        'border-t border-b border-l last:border-r border-core-30 text-main': input !== option.id,
        'active-radio-button-link': input === option.id,
        'opacity-50 pointer-events-none': option.disabled,
      }"
      exact-active-class="active-radio-button-link"
      @click="handleClick(option)"
    >
      <span class="text-xs font-medium">{{ option.label }}</span>
    </Component>

  </div>

</template>
