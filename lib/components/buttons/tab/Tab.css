.default-design-system {

    .tab {
        @apply leading-4.5 text-sm px-4 relative bg-transparent hover:bg-background-hover hover:cursor-pointer flex items-center justify-center select-none 
        after:absolute after:w-full after:bottom-0 after:left-0 after:h-px after:bg-border-subtle-00 hover:after:bg-border-strong-01;

        &:has(.tab-icon):has(.tab-label) {
            @apply gap-2;
        }

        &.box {
            @apply px-0;
        }

        /* Sizes  */
        &.s {
            @apply h-8;

            &.box {
                @apply w-8;
            }
        }

        &.m {
            @apply h-10;

            &.box {
                @apply w-10;
            }
        }

        &.l {
            @apply h-12;

            &.box {
                @apply w-12;
            }
        }

        &.tab-active {
            @apply bg-background-active font-medium after:absolute after:w-full after:bottom-0 after:left-0 after:h-0.5 after:bg-border-interactive hover:after:bg-border-interactive;
        }

        &:focus-visible {
            @apply ring-2 ring-focus ring-inset outline-none after:bg-focus;
        }

        &.disabled {
            @apply text-button-disabled pointer-events-none after:bg-border-disabled after:h-0.5 px-4;
        }

        &.inverse {

            @apply hover:bg-background-hover-inverse text-text-primary-inverse
            after:bg-border-subtle-00-inverse hover:after:bg-border-strong-01-inverse;
    
            &.tab-active {
                @apply bg-background-active-inverse after:bg-border-interactive-inverse hover:after:bg-border-interactive-inverse;
            }
    
            &:focus-visible {
                @apply ring-focus-inverse after:bg-focus-inverse;
            }
    
            &.disabled {
                @apply text-button-disabled-inverse after:bg-border-disabled-inverse;
            }
    
        }

    }


}