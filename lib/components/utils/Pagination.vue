<script setup lang="ts">

import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { computed, onMounted, watch } from 'vue'
import { pageSize as storedPageSize } from '@lib/store/table'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/button/Button.vue'

interface Props {
  name?:            string
  page?:            number
  total?:           number
  compact?:         boolean
  pageSize?:        number
  maxPages?:        number
  disabled?:        boolean
  dataLength?:      number
  compactSize?:     number
  statePagination?: boolean
}

const props   = withDefaults( defineProps<Props>(), { name: 'Records', page: 1, compactSize: 25 })

const { t }       = useI18n()
const route       = useRoute()
const router      = useRouter()
const displaySize = computed(() => props.statePagination ? props.pageSize : storedPageSize.value )

const sizeList = computed<DropListOption[]>(() => [
  {
    id:     5,
    name:   `${t( 'global.label.show' )} ${props.total < 5 ? props.total : 5} ${props.name}`,
    action: () => setSize( 5 )
  },
  {
    id:     10,
    hidden: props.total < 5,
    name:   `${t( 'global.label.show' )} ${props.total < 10 ? 'All' : 10} ${props.name}`,
    action: () => setSize( 10 )
  },
  {
    id:     25,
    hidden: props.total < 10,
    name:   `${t( 'global.label.show' )} ${props.total < 25 ? 'All' : 25} ${props.name}`,
    action: () => setSize( 25 )
  },
  {
    id:     50,
    hidden: props.total < 25,
    name:   `${t( 'global.label.show' )} ${props.total < 50 ? 'All' : 50} ${props.name}`,
    action: () => setSize( 50 )
  }

] )

const currentPage = defineModel<number>( 'page' )
const currPageSize = defineModel<number>( 'pageSize' )

function prevPage() {

  currentPage.value -= 1

  if ( !props.statePagination )
    router.push({ path: route.path, query: { ...route.query, page: currentPage.value, pageSize: props.pageSize } })

}

function nextPage() {

  currentPage.value += 1

  if ( !props.statePagination )
    router.push({ path: route.path, query: { ...route.query, page: currentPage.value, pageSize: props.pageSize } })

}

/*
 * When size is changed, reset the page to 1,
 * and save the preferred size value to localStorage
*/

function setSize( size: number ) {

  currPageSize.value = size

  if ( !props.statePagination ) {

    storedPageSize.value = size
    localStorage.setItem( 'pageSize', String( size ))
    router.push({ path: route.path, query: { ...route.query, page: 1, pageSize: size } })

  }

}

watch(() => props.compact, ( n ) => {

  if ( n )
    setSize( props.compactSize )

}, { immediate: true })

/*
 * If the current page is greater than the max pages,
 * set the current page to the max pages and update the route
*/

watch( [
  currentPage,
  () => props.maxPages
], ( n ) => {

  if ( n[0] > n[1] ) {

    currentPage.value = n[1]

    if ( !props.statePagination )
      router.push({ path: route.path, query: { ...route.query, page: currentPage.value, pageSize: props.pageSize } })

  }

}, { immediate: true })

function generatePageOptions(): DropListOption[] {

  const pages: DropListOption[] = []

  for ( let i = 1; i <= props.maxPages; i++ ) {

    pages.push({
      id:     i,
      name:   `${t( 'global.label.page' )} ${i}`,
      action: () => {

        currentPage.value = i

        if ( !props.statePagination )
          router.push({ path: route.path, query: { ...route.query, page: i } })

      }
    })

  }

  return pages

}

onMounted(() => {

  if ( !props.statePagination )
    currentPage.value = route.query?.page ? Number( route.query.page ) : 1

})

</script>

<template>

  <div
    class="h-full flex items-center"
    :class="{ 'separator-l': !props.compact }"
  >

    <Button
      v-if="!props.compact"
      size="s"
      mode="naked"
      :label="$t('global.label.pageSize')"
      :options="total > 5 ? sizeList : null"
      :disabled="disabled"
    >

      <template #default="{ active }">

        <div class="h-full px-4 flex items-center space-x-2">

          <Icon v-if="total > 5" :name="active ? 'caret-up' : 'caret-down'" />

          <p class="text-sm">
            {{ total > displaySize ? dataLength < displaySize ? dataLength : displaySize : total }} <span class="text-text-placeholder">of</span> {{ total }}
          </p>

        </div>

      </template>

    </Button>

    <div v-if="maxPages > 1" class="h-full flex items-center">

      <div class="grid place-content-center separator-l">

        <Button
          size="s"
          type="box"
          mode="naked"
          icon="caret-left"
          :disabled="currentPage === 1 || disabled"
          @click="prevPage"
        />

      </div>

      <div class="grid place-content-center separator-l">

        <Button
          v-slot="{ active }"
          size="s"
          mode="naked"
          class="text-sm px-2 flex items-center space-x-2"
          :label="$t('global.label.page')"
          :options="generatePageOptions()"
          :disabled="disabled"
        >

          <Icon :name="active ? 'caret-up' : 'caret-down'" />

          <p>{{ currentPage }} / {{ maxPages }}</p>

        </Button>

      </div>

      <div class="grid place-content-center separator-l">

        <Button
          size="s"
          type="box"
          mode="naked"
          icon="caret-right"
          :disabled="currentPage === Number(maxPages) || disabled"
          @click="nextPage"
        />

      </div>

    </div>

  </div>

</template>
