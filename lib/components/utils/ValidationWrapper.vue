<script setup lang="ts">

import { checkValue } from '@lib/scripts/utils'
import { validateInput } from '@lib/scripts/inputValidation'
import { computed, onMounted, ref, watch } from 'vue'

import type { InputValidationOptions, ValidationWrapperProps } from '@lib/types/inputTypes'

defineOptions({
  name: 'ValidationWrapper'
})

const props = withDefaults( defineProps<ValidationWrapperProps<any>>(), {
  timeout: 1000
})

const emits = defineEmits<{
  'update:valid':  [ value: boolean ]
  'exposeElement': [ value: HTMLDivElement ]
}>()

const valid = computed({
  get: () => props.valid,
  set: value => emits( 'update:valid', value )
})

const used              = ref<boolean>( false )
const error             = ref<string>( null )
const element           = ref<HTMLDivElement>( null )
const validationTimeout = ref<NodeJS.Timeout>( null )

watch( [
  () => props.modelValue,
  () => props.required
], ( n ) => {

  error.value = null
  clearTimeout( validationTimeout.value )

  const validationOptions: InputValidationOptions = {
    min:      props.min ?? null,
    max:      props.max ?? null,
    name:     props.name ?? props.label ?? props.placeholder ?? 'Input',
    type:     props.type ?? null,
    match:    props.match ?? null,
    regex:    props.regex ?? null,
    required: props.required
  }

  const validation = validateInput( n[0], validationOptions )
  valid.value = validation.valid

  if ( checkValue( n[0] ))
    used.value = true

  if ( used.value ) {

    validationTimeout.value = setTimeout(() => {

      if ( validation.error )
        error.value = props.customError ?? validation.error

      else error.value = null

    }, props.timeout )

  }

}, { immediate: true })

onMounted(() => emits( 'exposeElement', element.value ))

</script>

<template>

  <div class="w-full">

    <div
      ref="element"
      class="w-full relative after:w-full after:h-full after:absolute after:left-0 after:top-0 after:pointer-events-none"
      :class="{
        'after:border-b': mode === 'primary',
        'after:border-error': !!error,
        'after:border-core-30 focus-within:after:border-main': !error,
        'opacity-50 pointer-events-none': disabled,
        'pointer-events-none': readonly,
      }"
    >

      <slot />

    </div>

    <slot name="error">
      <div v-if="!!error" class="pt-2 px-4">
        <p class="text-left text-xs text-error">
          {{ error }}
        </p>
      </div>
    </slot>

  </div>

</template>
