
.snackbar-container {
    @apply relative flex items-center justify-center sm:justify-end;
}

.snackbar-container-inner {
    @apply w-full pl-3.5 pb-[0.938rem] grid grid-cols-[max-content_1fr_max-content] gap-x-[0.875rem] border-[0.063rem] border-l-3 leading-4.5;

    &.message-only {
        @apply pb-0;

        .snackbar-message {
            @apply font-normal pb-[0.938rem];
        }
    }

    /* Notifications with actions or timestamp have shadows and no top and left border. */
    &.has-bottom-element {
        @apply pb-[0.938rem] shadow-[0px_2px_6px_0px_rgba(0,0,0,0.3)] border-y-0 border-r-0;
    }

}

.snackbar-container:not(:has(.snackbar-action)) {
    @apply w-full;
}

.snackbar-message {
    @apply text-sm font-semibold wrap-anywhere;
}

.snackbar-details {
    @apply text-sm;
}

.snackbar-action-timestamp {
    @apply mt-6 text-sm;
}

.snackbar-icon {
    @apply pt-4;
}

.snackbar-content {
    @apply pt-3.5 h-fit overflow-hidden;
}

.snackbar-action-button {
    @apply mt-6 text-sm;
}

.snackbar-inline-action {
    @apply text-button-tertiary font-medium hover:cursor-pointer select-none pl-4;
}

.snackbar-message-action-container {
    @apply flex justify-between text-sm;
}

/* Color schemes */
.snackbar-error {

    @apply bg-notification-error-background border-notification-error-border border-l-support-error;

    .snackbar-icon{
        @apply text-support-error;
    }

    &.inverse {
        @apply bg-notification-error-background-inverse border-y-notification-error-border-inverse border-l-support-error-inverse border-r-notification-error-border-inverse text-text-inverse;

        .snackbar-icon{
            @apply text-support-error-inverse;
        }

        .snackbar-inline-action{
            @apply text-link-inverse;
        }
    }

}

.snackbar-warning {

    @apply bg-notification-warning-background border-notification-warning-border border-l-support-warning;

    .snackbar-icon{
        @apply text-support-warning;
    }

    &.inverse {
        @apply bg-notification-warning-background-inverse border-y-notification-warning-border-inverse border-l-support-warning-inverse border-r-notification-warning-border-inverse text-text-inverse;

        .snackbar-icon{
            @apply text-support-warning-inverse;
        }

        .snackbar-inline-action{
            @apply text-link-inverse;
        }
    }

}

.snackbar-info {

    @apply bg-notification-info-background border-notification-info-border border-l-support-info;

    .snackbar-icon{
        @apply text-support-info;
    }

    &.inverse {
        @apply bg-notification-info-background-inverse border-y-notification-info-border-inverse border-l-support-info-inverse border-r-notification-info-border-inverse text-text-inverse;

        .snackbar-icon{
            @apply text-support-info-inverse;
        }

        .snackbar-inline-action{
            @apply text-link-inverse;
        }
    }

}

.snackbar-success {

    @apply bg-notification-success-background border-notification-success-border border-l-support-success;

    .snackbar-icon{
        @apply text-support-success;
    }

    &.inverse {
        @apply bg-notification-success-background-inverse border-y-notification-success-border-inverse border-l-support-success-inverse border-r-notification-success-border-inverse text-text-inverse;

        .snackbar-icon{
            @apply text-support-success-inverse;
        }

        .snackbar-inline-action{
            @apply text-link-inverse;
        }
    }

}
