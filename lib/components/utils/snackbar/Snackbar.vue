<script setup lang="ts">

import { alertQueue, notificationQueue } from '@lib/store/snackbar'
import { computed, onMounted, ref, watch } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Button from '@lib/components/button/Button.vue'

import type { SnackbarProps } from '@lib/types/snackbarTypes'

const props = withDefaults( defineProps<SnackbarProps>(), {
  pause:           false,
  showCloseButton: true
})

function resetSnackbarOptions() {

  if ( props.type === 'alert' )
    alertQueue.value = alertQueue.value.filter( alert => alert.id !== props.id )

  else
    notificationQueue.value = notificationQueue.value.filter( notification => notification.id !== props.id )

}

const messageOnly      = computed(() => props.message && !props.details )
const remainingTime    = ref( 0 )
const lastStartTime    = ref( 0 )
const snackbarTimeout  = ref<NodeJS.Timeout>( null )
const hasBottomElement = computed(() => props.action || props.actionName || props.timeStamp )

function startTimeout() {

  clearTimeout( snackbarTimeout.value )

  snackbarTimeout.value = setTimeout(() => {

    resetSnackbarOptions()

  }, remainingTime.value )

  lastStartTime.value = Date.now()

}

function pauseTimeout() {

  clearTimeout( snackbarTimeout.value )

  const elapsedTime = Date.now() - lastStartTime.value

  if ( remainingTime.value > elapsedTime )
    remainingTime.value -= elapsedTime

}

function restartTimeout() {

  clearTimeout( snackbarTimeout.value )

  if ( remainingTime.value > 0 )
    startTimeout()

}

onMounted(() => {

  if ( !props.strict ) {

    remainingTime.value = props.duration
    startTimeout()

  }

  else { remainingTime.value = 0 }

})

// Timer should be paused from outside if the alert container is expanded
watch(() => props.pause, () => {

  if ( props.type === 'alert' ) {
    props.pause ? pauseTimeout() : restartTimeout()
  }

})

</script>

<template>

  <div class="snackbar-container">

    <div
      class="snackbar-container-inner"
      :class="{
        'inverse': modifier === 'inverse',
        'message-only': messageOnly,
        'has-bottom-element': hasBottomElement,
        [`snackbar-${severity}`]: type,
      }"
      @mouseenter="pauseTimeout"
      @mouseleave="!props.pause && restartTimeout()"
    >

      <!-- Icon container -->
      <div
        v-if="['info', 'success', 'warning', 'error'].includes(severity)"
        class="snackbar-icon"
      >

        <Icon v-if="severity === 'info'" name="information-filled" size="m" />
        <Icon v-if="severity === 'error'" name="error-filled" size="m" />
        <Icon v-if="severity === 'warning'" name="warning-filled" size="m" />
        <Icon v-if="severity === 'success'" name="checkmark-filled" size="m" />

      </div>

      <!-- Message, details and button container -->
      <div class="snackbar-content">

        <div class="snackbar-message-action-container">
          <p v-if="message" class="snackbar-message">
            {{ message }}
          </p>

          <span
            v-if="props.inlineAction && props.inlineActionName"
            class="snackbar-inline-action"
            @click.prevent.stop="() => {
              inlineAction()
              resetSnackbarOptions()
            }"
          >{{ inlineActionName }}</span>
        </div>

        <p v-if="details" class="snackbar-details">
          {{ details }}
        </p>

        <Button
          v-if="action && actionName && !inlineAction"
          size="s"
          mode="ghost"
          class="snackbar-action-button"
          modifier="inverse"
          @click.prevent.stop="() => {
            action()
            resetSnackbarOptions()
          }"
        >
          {{ actionName }}
        </Button>

        <p v-if="timeStamp && !action" class="snackbar-action-timestamp">
          {{ timeStamp }}
        </p>

      </div>

      <div v-if="showCloseButton">

        <Button
          size="l"
          type="box"
          mode="naked"
          class="snackbar-close-button"
          modifier="inverse"
          @click.prevent.stop="resetSnackbarOptions"
        >
          <Icon name="close-thin" size="s" />
        </Button>

      </div>

    </div>

  </div>

</template>
