<script setup lang="ts">

import { computed } from 'vue'
import { checkValue } from '@lib/scripts/utils'

import type { LabelProps } from '@lib/types/inputTypes'

const props = withDefaults( defineProps<LabelProps<any>>(), {
  push: true
})

const moveLabel     = computed(() => checkValue( props.input ))
const dynamicLabel  = computed(() => props.label || props.placeholder || props.name || 'Input' )

</script>

<template>

  <label
    class="group/label relative w-full h-max grid grid-cols-[1fr_max-content]"
    :class="{
      'min-h-[3.375rem]': size === 'l' || size === 'auto',
      'min-h-[2.5rem]': size !== 'l',
    }"
  >

    <div class="truncate w-full h-full">

      <div class="w-full h-0 px-4 relative flex items-end pointer-events-none">

        <p
          v-if="!!label && size === 'l'"
          class="truncate text-core-70 grid grid-cols-[1fr_max-content] gap-x-0.5 group-focus-within/label:text-[0.625rem] group-focus-within/label:uppercase group-focus-within/label:translate-y-[1.5rem] transition-all select-none"
          :class="{
            'translate-y-[2.4rem]': !moveLabel && !forceFocus,
            'text-[0.625rem] uppercase translate-y-[1.5rem]': moveLabel || forceFocus,
          }"
        >
          <span class="truncate">{{ dynamicLabel }}</span> <span v-if="required" class="text-main relative">*</span>
        </p>

        <p
          v-else-if="!checkValue(input)"
          class="truncate text-core-70 transition-all select-none"
          :class="{
            'translate-y-[2.4rem]': size === 'l' || size === 'auto',
            'translate-y-[2rem]': size !== 'l',
          }"
        >{{ dynamicLabel }} <span v-if="required" class="text-main relative">*</span></p>

      </div>

      <div
        class="relative w-full h-full"
        :class="{
          'pt-2': (!!label && size === 'l' && moveLabel && push) || forceFocus,
          'group-focus-within/label:pt-2': !!label && size === 'l' && push,
        }"
      >
        <slot />
      </div>

    </div>

    <slot name="icon" />

  </label>

</template>
