<script setup lang="ts">

import { ref, watch } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'
import Loader from '@lib/components/blocks/Loader.vue'

const props = defineProps<{
  name?:            string
  total?:           number
  finished?:        boolean
  updateAction?:    () => Promise<void>
  initDataLoading?: boolean
}>()

const containerElement = ref<HTMLDivElement>( null )
const intersectElement = ref<HTMLDivElement>( null )

const options: IntersectionObserverInit = {
  root:      containerElement.value,
  threshold: 0.95
}

const pending       = ref<boolean>( false )
const totalReached  = ref<boolean>( false )

const observer = new IntersectionObserver( async ( entries ) => {

  if ( entries[0].isIntersecting ) {

    if ( !pending.value && !props.finished ) {
      pending.value = true
      await props.updateAction()
      pending.value = false
    }

    else if ( props.finished ) {
      totalReached.value = true
    }

  }

}, options )

watch( intersectElement, ( n ) => {

  if ( n )
    observer.observe( n )

})

</script>

<template>

  <div
    ref="containerElement"
    class="w-full h-full relative overflow-y-auto"
  >

    <div class="min-h-full" :class="{ 'pb-14': totalReached }">

      <Transition name="view" mode="out-in">
        <div
          v-if="initDataLoading"
          class="w-full py-10 flex items-center justify-center space-x-4"
        >
          <Loader :name="name" />
        </div>

        <div
          v-else-if="!total && !pending"
          class="w-full p-10 flex items-center justify-center space-x-4"
        >
          <Icon size="m" name="folder" class="text-main" />
          <p class="text-sm">
            {{ $t('global.phrase.noRecordsFound', { name: name ?? $t('global.label.records') }) }}
          </p>
        </div>

        <slot v-else />
      </Transition>

    </div>

    <div
      ref="intersectElement"
      class="w-full"
      :class="{
        'h-0 sticky bottom-0': totalReached,
        'h-14 relative': !totalReached,
      }"
    >

      <Transition name="route">

        <div
          v-if="pending"
          class="w-full h-14 relative flex items-center justify-center space-x-4 bg-core-20"
          :class="{
            'absolute bottom-0': totalReached,
          }"
        >
          <Loader :name="name" />
        </div>

        <div
          v-else-if="totalReached && total"
          class="w-full h-14 flex items-center justify-center space-x-4 bg-core-20"
          :class="{
            'absolute bottom-0': totalReached,
          }"
        >
          <Icon size="m" name="no-more-records" class="text-main" />
          <p class="text-sm">
            {{ $t('global.phrase.noMoreRecords', { name: name ?? $t('global.label.records') }) }}
          </p>
        </div>

      </Transition>

    </div>

  </div>

</template>
