<script setup lang="ts">

import { computed, onBeforeUnmount, onMounted, ref } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'

interface Props {
  index:     number
  total:     number
  option:    DropListOption
  selected?: boolean
}

const props = defineProps<Props>()

const emits = defineEmits<{
  close: []
}>()

const focus         = ref<boolean>( false )
const optionElement = ref<HTMLElement>( null )

const pending     = ref<boolean>( false )
const actionError = ref<string>( null )

const iconPosition = computed(() => props.option?.icon?.position ?? 'left' )

async function handleOptionAction() {

  if ( !props.option?.action )
    return

  pending.value = true

  const actionData = await props.option.action()

  actionError.value = actionData?.error ?? null

  if ( !actionError.value )
    emits( 'close' )

  pending.value = false

}

function handleTab( e: KeyboardEvent ) {

  if ( e.key === 'Tab' ) {

    if ( !focus.value )
      return

    const currentTarget = e.target as HTMLButtonElement

    if ( currentTarget.name === `option-${props.total - 1}` ) {

      e.preventDefault()

      const option = document.getElementsByName( 'search-option' )[0] ?? document.getElementsByName( 'option-0' )[0]
      option.focus()

    }

  }

}

function handleArrowDown( e: KeyboardEvent ) {

  if ( e.key === 'ArrowDown' ) {

    e.preventDefault()

    const currentTarget = e.target as HTMLButtonElement
    const nextSibling = currentTarget.nextSibling as HTMLButtonElement

    if ( currentTarget.name === `option-${props.index}` || currentTarget.name === 'search-option' )
      document.getElementsByName( 'option-0' )[0].focus()

    if ( currentTarget.name === `option-${props.total - 1}` ) {
      const option = document.getElementsByName( 'search-option' )[0] ?? document.getElementsByName( 'option-0' )[0]
      option.focus()
    }

    if ( nextSibling ) {
      if ( nextSibling?.name?.match( 'option' ))
        nextSibling.focus()
    }

  }

}

function handleArrowUp( e: KeyboardEvent ) {

  if ( e.key === 'ArrowUp' ) {

    e.preventDefault()

    const currentTarget = e.target as HTMLButtonElement
    const prevSibling = currentTarget.previousSibling as HTMLButtonElement

    if ( currentTarget.name === 'option-0' ) {
      const option = document.getElementsByName( 'search-option' )[0] ?? document.getElementsByName( `option-${props.index}` )[0]
      option.focus()
    }

    if ( currentTarget.name === 'search-option' )
      document.getElementsByName( `option-${props.index}` )[0].focus()

    if ( prevSibling ) {
      if ( prevSibling?.name?.match( 'option' ))
        prevSibling.focus()

    }

  }

}

function cycleFocus( e: KeyboardEvent ) {

  handleTab( e )
  handleArrowUp( e )
  handleArrowDown( e )

}

onMounted(() => {
  window.addEventListener( 'keydown', cycleFocus )
})

onBeforeUnmount(() => window.removeEventListener( 'keydown', cycleFocus ))

</script>

<template>

  <button
    ref="optionElement"
    class="text-left relative w-full flex items-start cursor-pointer border-l border-b border-b-core-20 hover:bg-core-20 focus-visible:bg-core-20 focus-visible:outline-1 focus-visible:outline-dashed focus-visible:outline-main-30 clicked-active-state"
    :class="{
      'border-l-main': selected,
      'border-l-core-10': !selected,
      'opacity-50': option?.disabled,
      'pointer-events-none': pending || option?.disabled,
    }"
    :name="`option-${index}`"
    :disabled="option?.disabled"
    @blur="() => focus = false"
    @focus="() => focus = true"
    @click.stop="handleOptionAction"
  >

    <div v-if="option?.icon && iconPosition === 'left'" class="w-12 min-w-[3rem] min-h-[3.5rem] max-h-[3.5rem] grid place-content-center">
      <Icon
        :name="option?.icon?.name"
        :size="option?.icon?.size ?? 'm'"
        class="text-text"
      />
    </div>

    <div
      class="h-full grow py-4 grid content-center"
      :class="{
        'pl-4': option?.icon?.position !== 'left',
        'pr-4': option?.icon?.position !== 'right',
      }"
    >
      <p
        :class="{
          'text-base': !selected,
          'text-core-120': !selected,
          'text-main font-semibold': selected,
        }"
        class="truncate"
      >
        {{ option.name }}
      </p>
      <p class="text-sm text-core-70">
        {{ option.description }}
      </p>
    </div>

    <div v-if="(option?.icon && iconPosition === 'right') || pending" class="w-12 min-w-[3rem] min-h-[3.5rem] max-h-[3.5rem] grid place-content-center">
      <Icon
        :name="pending ? 'loading' : option.icon?.name"
        :size="option?.icon?.size ?? 'm'"
        :class="{
          'text-main': pending,
          'text-base': !pending,
        }"
      />
    </div>

  </button>

</template>
