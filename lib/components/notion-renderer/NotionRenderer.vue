<script setup lang="ts">

import { marked } from 'marked'
import { computed, onMounted, ref, watch } from 'vue'

import Loader from '@lib/components/blocks/Loader.vue'

import type { Block, NotionBlocksResponse, RichText } from '@lib/components/notion-renderer/types'

const props = defineProps<{
  pageId:    string
  pageTitle: string
  getBlocks: ( blockId: string, params?:  Partial<Record<'start_cursor', string>> ) => Promise<Payload<NotionBlocksResponse>>
}>()

const notionContent = ref( '' )
const blocks = ref<Block[]>( [] )

// Block content fetching with recursion handling
async function getAllBlockContent( blockId: string, recursionLevel = 0, topLevelOnly = false ) {
  try {
    // Prevent infinite recursion
    if ( recursionLevel > 3 ) {
      console.warn( 'Maximum recursion level reached for block:', blockId )
      return []
    }

    // Make API request with pagination support
    let allBlocks: Block[] = []
    let hasMore = true
    let startCursor: string | undefined

    while ( hasMore ) {
      // Build query parameters for pagination
      const params: Partial<Record<'start_cursor', string>> = {}
      if ( startCursor )
        params.start_cursor = startCursor

      const { payload } = await props.getBlocks( blockId, params )

      // Parse response based on format
      let responseBlocks: Block[] = []
      if ( payload.results ) {
        responseBlocks = payload.results
        hasMore = payload.has_more || false
        startCursor = payload.next_cursor
      }
      else if ( Array.isArray( payload )) {
        responseBlocks = payload
        hasMore = false
      }
      else {
        console.warn( 'Unexpected response format:', payload )
        hasMore = false
      }

      // Add blocks to our collection
      allBlocks = [ ...allBlocks, ...responseBlocks ]

      // If no more pages or we're at the pagination limit, exit the loop
      if ( !hasMore || allBlocks.length > 1000 )
        break

    }

    // Check for blocks that need children
    const needsChildren = recursionLevel <= 2 || ( !topLevelOnly && recursionLevel < 3 )

    if ( needsChildren ) {
      // Create a promise array for parallel fetching
      const fetchPromises: Promise<void>[] = []

      // Find blocks that need their children
      for ( let i = 0; i < allBlocks.length; i++ ) {
        const block = allBlocks[i]

        // Skip blocks without children
        if ( !block.has_children && block.type !== 'table' )
          continue

        // Handle each block's children
        const processBlock = async () => {
          try {
            // Special handling for tables and toggles
            if ( block.type === 'table' || block.type === 'toggle'
              || (( [ 'heading_1', 'heading_2', 'heading_3' ].includes( block.type ))
              )) {

              // Direct API call for important blocks to ensure we get all children
              const directResponse =  await props.getBlocks( block.id )

              let childBlocks: any[] = []

              if ( directResponse && directResponse.payload.results ) {
                childBlocks = directResponse.payload.results

                // For toggle blocks or table blocks at level 0, also fetch one more level of children
                if ( recursionLevel === 0 && ( block.type === 'toggle' || block.type === 'table'
                  || (( [ 'heading_1', 'heading_2', 'heading_3' ].includes( block.type ))
                    && block[block.type]?.is_toggleable ))) {

                  // Create a subpromise array for this block's children
                  const childPromises: Promise<void>[] = []

                  for ( let j = 0; j < childBlocks.length; j++ ) {
                    if ( childBlocks[j].has_children ) {
                      // Add a promise to fetch this child's children
                      childPromises.push(( async () => {
                        try {
                          const nestedChildren = await getAllBlockContent( childBlocks[j].id, recursionLevel + 2, false )
                          childBlocks[j].children = nestedChildren
                        }

                        catch ( err ) {
                          childBlocks[j].children = []
                          console.error( err )
                        }
                      })())
                    }
                  }

                  // Wait for all child fetches to complete
                  if ( childPromises.length > 0 )
                    await Promise.all( childPromises )

                }
              }

              block.children = childBlocks
            }
            // For other blocks with children
            else if ( block.has_children ) {
              const childBlocks = await getAllBlockContent( block.id, recursionLevel + 1, true )
              block.children = childBlocks
            }
          }
          catch ( err ) {
            console.error( `Error fetching children for ${block.type} block:`, err )
            block.children = []
          }
        }

        // Add this block's processing to our promises array
        fetchPromises.push( processBlock())
      }

      // Wait for all block processing to complete in parallel
      if ( fetchPromises.length > 0 )
        await Promise.all( fetchPromises )

    }

    return allBlocks
  }
  catch ( error ) {
    console.error( 'Error fetching block content:', error )
    throw error
  }
}

// Conversion functions
function blocksToMarkdown( blocks: Block[] ): string {
  if ( !blocks || !blocks.length )
    return ''

  let markdown = ''

  blocks.forEach(( block ) => {
    markdown += blockToMarkdown( block )
  })

  return markdown
}

function blockToMarkdown( block: Block ): string {
  if ( !block )
    return ''

  try {
    // Handle different possible block formats
    const type = block.type || ''
    const value = block[type] || block

    let markdown = ''

    // Try to extract text from various possible structures
    const getRichText = ( obj ): string => {
      if ( !obj )
        return ''
      if ( obj.rich_text )
        return richTextToMarkdown( obj.rich_text )
      if ( obj.text )
        return richTextToMarkdown( obj.text )
      if ( obj.content )
        return obj.content
      if ( obj.title )
        return obj.title
      if ( typeof obj === 'string' )
        return obj
      return ''
    }

    // Handle various block types
    switch ( type ) {
      case 'paragraph':
        markdown += `${getRichText( value )}\n\n`
        break
      case 'heading_1':{
        const h1IsToggleable = value.is_toggleable || false
        const h1Content = getRichText( value )

        if ( h1IsToggleable ) {
          // Create a unique ID for the toggle
          const toggleId = ` id="toggle-h1-${block.id.substring( 0, 8 )}"`

          // Start toggle HTML - set open by default
          markdown += `<details class="notion-toggle notion-toggle-h1"${toggleId} open>\n<summary class="notion-toggle-summary notion-h1">${h1Content}</summary>\n<div class="notion-toggle-content">\n`

          // Add children content if available
          if ( block.children && block.children.length ) {
            // Process children
            const childrenContent = blocksToMarkdown( block.children )

            // Add the content
            markdown += childrenContent
          }
          else {
            // If no children are available yet
            markdown += '<div class="notion-empty-content"></div>'
          }

          markdown += '</div>\n</details>\n\n'
        }
        else {
          markdown += `# ${h1Content}\n\n`
        }
        break
      }
      case 'heading_2':{
        const h2IsToggleable = value.is_toggleable || false
        const h2Content = getRichText( value )

        if ( h2IsToggleable ) {

          // Create a unique ID for the toggle
          const toggleId = ` id="toggle-h2-${block.id.substring( 0, 8 )}"`

          // Start toggle HTML - set open by default
          markdown += `<details class="notion-toggle notion-toggle-h2"${toggleId} open>\n<summary class="notion-toggle-summary notion-h2">${h2Content}</summary>\n<div class="notion-toggle-content">\n`

          // Add children content if available
          if ( block.children && block.children.length ) {
            // Process children
            const childrenContent = blocksToMarkdown( block.children )

            // Add the content
            markdown += childrenContent
          }
          else {
            // If no children are available yet
            markdown += '<div class="notion-empty-content"></div>'
          }

          markdown += '</div>\n</details>\n\n'
        }
        else {
          markdown += `## ${h2Content}\n\n`
        }
        break
      }
      case 'heading_3':{
        const h3IsToggleable = value.is_toggleable || false
        const h3Content = getRichText( value )

        if ( h3IsToggleable ) {

          // Create a unique ID for the toggle
          const toggleId = ` id="toggle-h3-${block.id.substring( 0, 8 )}"`

          // Start toggle HTML - set open by default
          markdown += `<details class="notion-toggle notion-toggle-h3"${toggleId} open>\n<summary class="notion-toggle-summary notion-h3">${h3Content}</summary>\n<div class="notion-toggle-content">\n`

          // Add children content if available
          if ( block.children && block.children.length ) {
            // Process children
            const childrenContent = blocksToMarkdown( block.children )

            // Add the content
            markdown += childrenContent
          }
          else {
            // If no children are available yet
            markdown += '<div class="notion-empty-content"></div>'
          }

          markdown += '</div>\n</details>\n\n'
        }
        else {
          markdown += `<h3>${h3Content}</h3>\n\n`
        }
        break
      }
      case 'bulleted_list_item':
        markdown += `- ${getRichText( value )}\n\n`

        // Add children content if available (for nested lists)
        if ( block.children && block.children.length ) {
          const childContent = blocksToMarkdown( block.children )
            .split( '\n' )
            .map(( line: string ) => line ? `  ${line}` : line )
            .join( '\n' )
          markdown += `${childContent}\n`
        }
        break
      case 'numbered_list_item':
        markdown += `1. ${getRichText( value )}\n`

        // Add children content if available (for nested lists)
        if ( block.children && block.children.length ) {
          const childContent = blocksToMarkdown( block.children )
            .split( '\n' )
            .map(( line: string ) => line ? `  ${line}` : line )
            .join( '\n' )
          markdown += `${childContent}\n`
        }
        break
      case 'to_do':{
        const checkbox = ( value.checked || value.isChecked ) ? '[x]' : '[ ]'
        markdown += `${checkbox} ${getRichText( value )}\n`

        // Add children content if available
        if ( block.children && block.children.length ) {
          const childContent = blocksToMarkdown( block.children )
            .split( '\n' )
            .map(( line: string ) => line ? `  ${line}` : line )
            .join( '\n' )
          markdown += `${childContent}\n`
        }
        break
      }
      case 'toggle':{
        const toggleContent = getRichText( value )

        // Create a unique ID for the toggle
        const toggleId = ` id="toggle-${block.id.substring( 0, 8 )}"`

        // Start toggle HTML - set open by default
        markdown += `<details class="notion-toggle"${toggleId} open>\n<summary class="notion-toggle-summary">${toggleContent}</summary>\n<div class="notion-toggle-content">\n`

        // Add children content if available
        if ( block.children && block.children.length ) {
          // Process children
          const childrenContent = blocksToMarkdown( block.children )

          // Add the content
          markdown += childrenContent
        }
        else {
          // If no children are available yet
          markdown += '<div class="notion-empty-content"></div>'
        }

        markdown += '</div>\n</details>\n\n'
        break
      }
      case 'code':{
        const language = value.language || ''
        markdown += `\`\`\`${language}\n${getRichText( value )}\n\`\`\`\n\n`
        break
      }
      case 'quote':
        markdown += `> ${getRichText( value )}\n\n`
        break
      case 'divider':
        markdown += '---\n\n'
        break
      case 'callout':{
        // Extract icon if available
        let iconHtml = ''
        if ( value.icon ) {
          if ( value.icon.type === 'emoji' && value.icon.emoji )
            iconHtml = `<span class="notion-callout-emoji">${value.icon.emoji}</span>`
          else if ( value.icon.type === 'external' && value.icon.external?.url )
            iconHtml = `<img src="${value.icon.external.url}" class="notion-callout-icon" alt="Icon">`
          else if ( value.icon.type === 'file' && value.icon.file?.url )
            iconHtml = `<img src="${value.icon.file.url}" class="notion-callout-icon" alt="Icon">`

        }

        // Get background color if available
        const bgColor = value.color || 'default'

        // Create callout HTML
        markdown += `<div class="notion-callout notion-callout-${bgColor}">
                      <div class="notion-callout-content">
                        ${iconHtml}
                        <div class="notion-callout-text">${getRichText( value )}</div>
                      </div>
                      </div>\n\n`
        break
      }
      case 'table_of_contents':
        // Table of contents will be generated client-side based on headings
        markdown += '<div class="notion-table-of-contents"></div>\n\n'
        break
      case 'image':{
        let imageUrl = ''
        if ( value.type === 'external' )
          imageUrl = value.external?.url || ''
        else if ( value.file )
          imageUrl = value.file.url || ''
        else if ( value.url )
          imageUrl = value.url

        const caption = value.caption ? getRichText( value.caption ) : 'Image'
        if ( imageUrl ) {
          // Add a special class to the image for toggle containers
          const isInToggle = ''

          markdown += `<figure class="notion-image-figure${isInToggle}">
            <img src="${imageUrl}" alt="${caption}" class="notion-image">
            ${caption !== 'Image' ? `<figcaption class="notion-image-caption">${caption}</figcaption>` : ''}
            </figure>\n\n`
        }
        else {
          markdown += `<div class="notion-empty-image">Image could not be loaded</div>\n\n`
        }
        break
      }
      case 'video':{
        let videoUrl = ''
        if ( value.type === 'external' )
          videoUrl = value.external?.url || ''
        else if ( value.file )
          videoUrl = value.file.url || ''
        else if ( value.url )
          videoUrl = value.url

        if ( videoUrl ) {
          // Use HTML for video embed since markdown doesn't have native video support
          markdown += `<figure class="notion-video-figure">
                        <video class="notion-video" width="100%" controls>
                          <source src="${videoUrl}" type="video/mp4"/>
                        </video>
                        </figure>\n\n`
        }
        break
      }
      case 'embed':{
        const embedUrl = value.url || ''
        if ( embedUrl ) {
          // Handle YouTube, Vimeo and other common embed sources
          if ( embedUrl.includes( 'youtube.com' ) || embedUrl.includes( 'youtu.be' )) {
            markdown += `<div class="notion-embed notion-embed-youtube">
                           <iframe width="560" height="315" src="${embedUrl}" frameborder="0" allowfullscreen></iframe>
                        </div>\n\n`
          }
          else if ( embedUrl.includes( 'vimeo.com' )) {
            markdown += `<div class="notion-embed notion-embed-vimeo">
                           <iframe src="${embedUrl}" width="640" height="360" frameborder="0" allowfullscreen></iframe>
                         </div>\n\n`
          }
          else {
            // Generic embed
            markdown += `<div class="notion-embed">
                           <iframe src="${embedUrl}" width="100%" height="400" frameborder="0"></iframe>
                         </div>\n\n`
          }
        }
        break
      }
      case 'file':{
        let fileUrl = ''
        if ( value.type === 'external' )
          fileUrl = value.external?.url || ''
        else if ( value.file )
          fileUrl = value.file.url || ''
        else if ( value.url )
          fileUrl = value.url

        if ( fileUrl ) {
          markdown += `<div class="notion-file">
                         <a href="${fileUrl}" target="_blank" class="notion-file-link">
                           <span class="notion-file-name">${value.name}</span>
                         </a>
                       </div>\n\n`
        }
        break
      }
      case 'table':{
        // Start table with proper class - use div wrapper to handle overflow
        markdown += '<div class="notion-table-container">\n'
        markdown += '<table class="notion-table">\n'

        // Get table properties
        const hasColumnHeader = block.table?.has_column_header || false
        const hasRowHeader = block.table?.has_row_header || false

        // Get the table width either from the table object or from the first row
        let tableWidth = 0
        if ( block.table && block.table.table_width )
          tableWidth = block.table.table_width

        // Add table rows from children
        if ( block.children && block.children.length > 0 ) {
          // First check to determine table width from the first row if not found earlier
          if ( tableWidth === 0 ) {
            for ( let i = 0; i < block.children.length; i++ ) {
              const row = block.children[i]
              if ( row.type === 'table_row' && row.table_row?.cells ) {
                tableWidth = row.table_row.cells.length
                break
              }
            }
          }

          // If we still don't have table width, default to a reasonable value
          if ( tableWidth === 0 )
            tableWidth = 3

          let rowCount = 0
          // Process each row
          block.children.forEach(( row: any, rowIndex: number ) => {
            if ( row.type === 'table_row' && row.table_row ) {
              rowCount++
              markdown += '  <tr>\n'

              const cells = row.table_row.cells || []

              // If the row has fewer cells than the table width, add empty cells
              const actualCellCount = Math.max( cells.length, tableWidth )

              for ( let cellIndex = 0; cellIndex < actualCellCount; cellIndex++ ) {
                // Determine if this cell should be a header cell
                const isHeaderCell = ( hasColumnHeader && rowIndex === 0 )
                  || ( hasRowHeader && cellIndex === 0 )
                const cellTag = isHeaderCell ? 'th' : 'td'

                // Get the actual cell or create empty content
                const cell = cellIndex < cells.length ? cells[cellIndex] : null

                // Process cell content - ensure we're handling both string and array cases
                let cellContent = ''

                if ( Array.isArray( cell ) && cell.length > 0 ) {
                  // Each cell is an array of rich text objects
                  cellContent = richTextToMarkdown( cell )
                }
                else if ( typeof cell === 'string' ) {
                  cellContent = cell
                }
                else if ( cell && typeof cell === 'object' ) {
                  // Handle potential single rich text object case
                  cellContent = richTextToMarkdown( [ cell ] )
                }
                else {
                  // Explicitly set empty cell content to non-breaking space to maintain cell dimensions
                  cellContent = '&nbsp;'
                }

                // Add the cell with proper tag
                markdown += `    <${cellTag}>${cellContent}</${cellTag}>\n`
              }

              markdown += '  </tr>\n'
            }
          })

          // If no actual rows were processed, add a placeholder row
          if ( rowCount === 0 ) {
            markdown += `  <tr>\n`
            markdown += `    <td colspan="${tableWidth}">Table data is loading...</td>\n`
            markdown += `  </tr>\n`
          }
        }
        else {
          // If no children but we know it's a table, try to create a minimal structure
          if ( tableWidth > 0 ) {
            markdown += '  <tr>\n'
            for ( let i = 0; i < tableWidth; i++ )
              markdown += `    <td>&nbsp;</td>\n`

            markdown += '  </tr>\n'
          }
          else {
            // Add a placeholder row if no children or width information is found
            markdown += '  <tr><td>Table data is loading... Please wait.</td></tr>\n'
          }
        }

        // End table
        markdown += '</table>\n'
        markdown += '</div>\n\n'
        break
      }
      default:{
        // Try to extract content for unknown block types
        const content = getRichText( value )
        if ( content )
          markdown += `${content}\n\n`
      }

    }

    // Handle nested children based on various possible structures
    // Only process children if they haven't been processed in the specific block type handler
    const children = block.children || block.content || value.children || []
    if ( children && children.length > 0
      && ![ 'toggle', 'bulleted_list_item', 'numbered_list_item', 'to_do', 'heading_1', 'heading_2', 'heading_3', 'table' ].includes( type )) {
      markdown += blocksToMarkdown( children )
    }

    return markdown
  }
  catch ( error ) {
    console.error( 'Error converting block to markdown:', error, block )
    return ''
  }
}

function richTextToMarkdown( richTextArray: RichText[] ): string {
  try {
    // Handle string input
    if ( typeof richTextArray === 'string' )
      return richTextArray

    // Handle empty or invalid input
    if ( !richTextArray )
      return ''

    // Empty array
    if ( !richTextArray.length )
      return ''

    // Process array of rich text objects
    return richTextArray.map(( richText: any ) => processRichTextObject( richText )).join( '' )
  }
  catch ( error ) {
    console.error( 'Error in richTextToMarkdown:', error, richTextArray )
    return String( richTextArray ) || ''
  }
}

// Helper function to process a single rich text object
function processRichTextObject( richText: RichText ): string {

  if ( !richText )
    return ''

  try {
    // Handle different potential structures
    let text = ''

    if ( richText.plain_text ) {
      text = richText.plain_text
    }
    else if ( typeof richText === 'string' ) {
      text = richText
    }
    else {
      return ''
    }

    // Apply annotations if present
    const annotations = richText.annotations

    if ( annotations ) {

      if ( annotations.bold )
        text = `<strong>${text}</strong>`
      if ( annotations.italic )
        text = `<em>${text}</em>`
      if ( annotations.strikethrough )
        text = `<del>${text}</del>`
      if ( annotations.code )
        text = `<code>${text}</code>`
      if ( annotations.underline )
        text = `<u>${text}</u>`

      // Apply color if present
      if ( annotations.color && annotations.color !== 'default' )
        text = `<span class="notion-${annotations.color}">${text}</span>`

    }
    return text
  }
  catch ( error ) {
    console.error( 'Error processing rich text object:', error, richText )
    return String( richText ) || ''
  }
}

// Rendered content
const renderedContent = computed(() => {
  if ( !notionContent.value )
    return ''
  return marked( notionContent.value )
})

// Initialize content
async function fetchNotionContent() {
  try {
    const allBlocks = await getAllBlockContent( props.pageId )
    blocks.value = allBlocks
    notionContent.value = blocksToMarkdown( allBlocks )
  }
  catch ( error ) {

    console.error( 'Error fetching Notion content:', error )
  }
}

// Watch for page ID changes
watch(() => props.pageId, ( newPageId ) => {
  if ( newPageId )
    fetchNotionContent()

})

// Initial setup
onMounted( async () => {
  await fetchNotionContent()

  // Post-rendering setup

  cleanupPlaceholderContent()
  generateTableOfContents()
  setupToggleElements()

})

// Watch for content changes to update rendering
watch(() => notionContent.value, () => {
  // Allow time for the DOM to update

  cleanupPlaceholderContent()
  generateTableOfContents()
  setupToggleElements()

})

// Helper functions
function cleanupPlaceholderContent() {
  // Check for and remove "Something" placeholders
  const emptyElements = document.querySelectorAll( '.notion-toggle-content > p:only-child' )
  emptyElements.forEach(( element ) => {
    if ( element.textContent === 'Something' || element.textContent === 'No content available' )
      element.textContent = ''

  })

  // Fix any empty toggle content
  const emptyToggles = document.querySelectorAll( '.notion-toggle-content:empty' )
  emptyToggles.forEach(( element ) => {
    // Remove the open attribute from parent details
    const parentDetails = element.closest( 'details' )
    if ( parentDetails && parentDetails.hasAttribute( 'open' ))
      parentDetails.removeAttribute( 'open' )

  })
}

function generateTableOfContents() {
  const tocElements = document.querySelectorAll( '.notion-table-of-contents' )
  if ( !tocElements.length )
    return

  const headings = document.querySelectorAll( '.notion-content h1, .notion-content h2, .notion-content h3' )
  if ( !headings.length ) {
    tocElements.forEach(( toc ) => {
      toc.innerHTML = '<p class="text-gray-500">No headings found</p>'
    })
    return
  }

  const toc = document.createElement( 'ul' )
  toc.className = 'notion-toc-list'

  headings.forEach(( heading, index ) => {
    // Add id to heading if it doesn't have one
    if ( !heading.id )
      heading.id = `heading-${index}`

    const level = Number.parseInt( heading.tagName.substring( 1 ))
    const listItem = document.createElement( 'li' )
    listItem.style.marginLeft = `${( level - 1 ) * 16}px`

    const link = document.createElement( 'a' )
    link.href = `#${heading.id}`
    link.textContent = heading.textContent || ''
    link.className = 'notion-toc-link'

    listItem.appendChild( link )
    toc.appendChild( listItem )
  })

  tocElements.forEach(( tocElement ) => {
    tocElement.innerHTML = ''
    tocElement.appendChild( toc.cloneNode( true ))
  })
}

function setupToggleElements() {
  // Get all toggle elements
  const detailsElements = document.querySelectorAll<HTMLDetailsElement>( 'details.notion-toggle' )

  detailsElements.forEach(( details ) => {

    // Get the toggle content
    const content = details.querySelector<HTMLElement>( '.notion-toggle-content' )

    if ( !content )
      return

    // Make all toggles closed by default by removing the open attribute
    if ( details.hasAttribute( 'open' ))
      details.removeAttribute( 'open' )

    // Set the initial visibility
    content.style.display = 'none'

    // Add the toggle event listener
    details.addEventListener( 'toggle', () => {
      // Toggle visibility
      content.style.display = details.open ? 'block' : 'none'
    })

    // Apply color styling if the toggle has notion-color classes
    const summary = details.querySelector( 'summary' )
    if ( summary ) {
      // Look for color classes in the summary
      const colorClasses = Array.from( summary.classList ).filter( cls =>
        cls.startsWith( 'notion-' )
        && ( cls.endsWith( '_background' )
          || [ 'notion-gray', 'notion-brown', 'notion-orange', 'notion-yellow', 'notion-green', 'notion-blue', 'notion-purple', 'notion-pink', 'notion-red' ].includes( cls ))
      )

      // First, fix the red elements (this should be the same code as before)
      const redElements = summary.querySelectorAll( '.notion-red' )
      if ( redElements.length > 1 ) {
        // Use the first element as the container
        const firstRedElement = redElements[0]

        // Extract the actual content from all red elements (removing code tags)
        let combinedContent = ''
        for ( let i = 0; i < redElements.length; i++ ) {
          // Create a temporary div to parse the HTML
          const tempDiv = document.createElement( 'div' )
          tempDiv.innerHTML = redElements[i].innerHTML

          // Extract content from code tags if they exist
          const codeElements = tempDiv.querySelectorAll( 'code' )
          if ( codeElements.length > 0 ) {
            for ( const codeEl of codeElements )
              combinedContent += codeEl.innerHTML

          }
          else {
            // If no code tags, use the content as is
            combinedContent += redElements[i].innerHTML
          }
        }

        // Replace the content of first element with all content inside a single code tag
        firstRedElement.innerHTML = `<code>${combinedContent}</code>`

        // Remove all other red elements
        for ( let i = 1; i < redElements.length; i++ )
          redElements[i].remove()

      }

      // Now handle the text elements after the status indicators
      // Find all strong tags left in the summary
      const strongTags = summary.querySelectorAll( 'strong' )
      let titleStrongElement = null
      let descriptionText = ''

      // Find the strong element with the title and any text that follows it
      for ( let i = 0; i < strongTags.length; i++ ) {
        const text = strongTags[i].textContent.trim()
        if ( text.includes( 'Export' ) || text.includes( 'View' )) {
          titleStrongElement = strongTags[i]

          // Look for the text node that follows this strong element
          let nextNode = titleStrongElement.nextSibling
          while ( nextNode ) {
            if ( nextNode.nodeType === Node.TEXT_NODE && nextNode.textContent.trim()) {
              descriptionText = nextNode.textContent.trim()
              // Remove this text node as we'll combine it into the h2
              nextNode.remove()
              break
            }
            nextNode = nextNode.nextSibling
          }
          break
        }
      }

      // Also look for any standalone text nodes that might be the description
      if ( !descriptionText ) {
        const textNodes = Array.from( summary.childNodes ).filter( node =>
          node.nodeType === Node.TEXT_NODE && node.textContent.trim()
        )

        for ( const textNode of textNodes ) {
          const text = textNode.textContent.trim()
          if ( text.startsWith( 'Get' ) || text.includes( 'invoice' ) || text.includes( 'shipped' )) {
            descriptionText = text
            textNode.remove()
            break
          }
        }
      }

      if ( titleStrongElement ) {
        // Create an h2 element
        const h2Element = document.createElement( 'p' )

        // Move the strong element inside
        h2Element.appendChild( titleStrongElement.cloneNode( true ))

        // Add the description text
        if ( descriptionText )
          h2Element.appendChild( document.createTextNode( ` ${descriptionText}` ))

        // Replace the original strong element with the new h2
        if ( titleStrongElement.parentNode ) {
          titleStrongElement.parentNode.replaceChild( h2Element, titleStrongElement )
        }
        else {
          // If for some reason the strong element is no longer in the DOM,
          // append the h2 element to the summary
          summary.appendChild( h2Element )
        }
      }

      // Clean up any remaining empty text nodes or unnecessary strong spaces
      const emptyStrongs = Array.from( summary.querySelectorAll( 'strong' )).filter(
        strong => strong.textContent.trim() === ''
      )
      emptyStrongs.forEach( strong => strong.remove())

      // Clean up text nodes that are just spaces
      Array.from( summary.childNodes ).forEach(( node ) => {
        if ( node.nodeType === Node.TEXT_NODE && node.textContent.trim() === '' )
          node.remove()

      })

      // Apply the first found color class to the entire toggle if available
      if ( colorClasses.length > 0 ) {
        const colorClass = colorClasses[0]
        details.classList.add( colorClass )

        // For background colors, add special styling
        if ( colorClass.endsWith( '_background' ))
          details.classList.add( 'notion-toggle-colored' )

      }
    }

    // Process images to make sure they load properly
    const images = content.querySelectorAll( 'img' )
    if ( images.length > 0 ) {
      images.forEach(( img ) => {
        // Set image src directly instead of waiting for a click
        if ( img.dataset.src && !img.src )
          img.src = img.dataset.src

        if ( !img.complete ) {
          img.onload = () => {
            // Refresh toggle display when image loads
            if ( details.open )
              content.style.display = 'block'

          }
        }
      })
    }

    // Also handle videos and iframes
    const media = content.querySelectorAll<HTMLVideoElement | HTMLIFrameElement>( 'video, iframe' )
    if ( media.length > 0 ) {
      media.forEach(( element ) => {
        if ( element.dataset.src && !element.src )
          element.src = element.dataset.src

      })
    }
  })
}
</script>

<template>

  <div v-if="renderedContent" class="notion-content flex h-full wax-w-full justify-center items-center">

    <div class="md:max-w-[50rem] flex flex-col px-4 max-w-full">
      <h1 class="page-title">
        {{ pageTitle }}
      </h1>
      <div class="flex flex-col" v-html="renderedContent" />
    </div>

  </div>

  <div v-else class="w-full h-[calc(100vh-4rem)] flex items-center justify-center space-x-2">
    <Loader name="Documentation" />
  </div>

</template>

<style>

/* Notion-like styling for content */

/* Typography */
.notion-content h1 {
  font-weight: 600;
  font-size: 1.875rem;
  line-height: 1.3;
  margin-top: 2.5rem;
  margin-bottom: 1rem;
  background: rgba(55, 53, 47, 0.1);
}

.notion-content h1.page-title{
  background: none;
  font-size: 2rem;
}

.notion-content h2 {
  font-weight: 600;
  font-size: 1.5rem;
  line-height: 1.3;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.notion-content h3 {
  font-weight: 600;
  font-size: 1.25rem;
  line-height: 1.3;
  margin-top: 1.2rem;
  margin-bottom: 0.8rem;
}

.notion-content p {
  margin: 1rem 0;
  white-space: pre-wrap;
  overflow-wrap: break-word;
}

/* Lists */
.notion-content ul {
  margin: 0.6rem 0;
  padding-left: 1.5rem;
  list-style-type: disc;
}

.notion-content ol {
  margin: 0.6rem 0;
  padding-left: 1.5rem;
  list-style-type: decimal;
}

.notion-content li {
  margin: 0.4rem 0;
  padding-left: 0.4rem;
}

/* Checkboxes */
.notion-content .notion-checkbox {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 3px;
  border: 1px solid rgba(55, 53, 47, 0.35);
  margin-right: 0.5rem;
  position: relative;
  top: 2px;
}

.notion-content .notion-checkbox-checked {
  background-color: rgb(35, 131, 226);
  border-color: transparent;
}

.notion-content .notion-checkbox-checked::after {
  content: "";
  position: absolute;
  top: 3px;
  left: 6px;
  width: 3px;
  height: 6px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* Links */
.notion-content a {
  color: #0b6bcb;
  text-decoration: underline;
  text-underline-offset: 1px;
  transition: opacity 0.2s ease;
}

.notion-content a:hover {
  opacity: 0.8;
}

/* Blockquotes */
.notion-content blockquote {
  margin: 0.6rem 0;
  padding-left: 1rem;
  border-left: 3px solid rgba(55, 53, 47, 0.2);
  color: rgba(55, 53, 47, 0.65);
  font-style: italic;
}

/* Code blocks */
.notion-content pre {
  font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
  background: rgba(247, 246, 243, 0.7);
  padding: 16px;
  border-radius: 3px;
  overflow-x: auto;
  margin: 1rem 0;
  font-size: 0.875rem;
}

.notion-content code {
  font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
  background: rgba(135, 131, 120, 0.1);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.85em;
  color: #eb5757;
}

.notion-content pre code {
  background: transparent;
  padding: 0;
  font-size: 0.875rem;
  color: inherit;
}

/* Tables */
.notion-table-container {
  width: 100%;
  overflow-x: auto;
  margin: 1rem 0;
  border-radius: 4px;
  box-shadow: rgba(15, 15, 15, 0.1) 0px 0px 0px 1px;
}

.notion-table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
  table-layout: auto;
  font-size: 0.875rem;
}

.notion-table th,
.notion-table td {
  border: 1px solid rgba(55, 53, 47, 0.2);
  padding: 0.5rem 0.75rem;
  text-align: left;
  vertical-align: top;
  min-width: 120px;
  word-break: break-word;
  position: relative;
}

.notion-table th {
  background-color: rgba(247, 246, 243, 0.7);
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 1;
}

.notion-table tr:nth-child(even) {
  background-color: rgba(247, 246, 243, 0.3);
}

.notion-table tr:hover {
  background-color: rgba(247, 246, 243, 0.5);
}

/* Dividers */
.notion-content hr {
  height: 1px;
  background-color: rgba(55, 53, 47, 0.09);
  border: none;
  /* margin: 2rem 0; */
}

/* Images */
.notion-content img {
  max-width: 100%;
  border-radius: 3px;
  margin: 0.5rem 0;
}

.notion-image-figure {
  margin: 1.5rem 0;
}

.notion-image {
  display: block;
  max-width: 100%;
  border-radius: 3px;
  height: auto; /* Ensure proper scaling */
}

.notion-image-in-toggle {
  margin: 0.75rem 0;
}

.notion-image-caption {
  color: rgba(55, 53, 47, 0.6);
  font-size: 0.875rem;
  text-align: center;
  margin-top: 0.5rem;
}

.notion-empty-image {
  background-color: #f7f6f3;
  padding: 1rem;
  text-align: center;
  color: #888;
  border-radius: 3px;
  font-style: italic;
  margin: 1rem 0;
}

/* Media embeds */
.notion-content video {
  max-width: 100%;
  border-radius: 3px;
  margin: .5rem 0;
  box-shadow: rgba(15, 15, 15, 0.1) 0px 0px 0px 1px, rgba(15, 15, 15, 0.1) 0px 2px 4px;
}

.notion-content iframe {
  max-width: 100%;
  border-radius: 3px;
  margin: 1rem 0;
  border: none;
  box-shadow: rgba(15, 15, 15, 0.1) 0px 0px 0px 1px, rgba(15, 15, 15, 0.1) 0px 2px 4px;
}

.notion-video {
  display: block;
  width: 100%;
  border-radius: 3px;
}

.notion-video-caption {
  color: rgba(55, 53, 47, 0.6);
  font-size: 0.875rem;
  text-align: center;
  margin-top: 0.5rem;
}

/* Callout blocks */
.notion-callout {
  display: flex;
  width: 100%;
  padding: 16px 16px 16px 12px;
  border-radius: 3px;
  margin: 1rem 0;
}

.notion-callout-content {
  display: flex;
  align-items: center;
  column-gap: .5rem;
  width: 100%;
}

.notion-callout-emoji {
  font-size: 1.25rem;
  line-height: 1;
  margin-right: 8px;
}

.notion-callout-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
  flex-shrink: 0;
}

.notion-callout-text {
  flex: 1;
}

.notion-callout-default {
  background-color: rgba(235, 236, 237, 0.3);
}

.notion-callout-gray_background {
  background-color: rgba(235, 236, 237, 0.6);
}

.notion-callout-brown_background {
  background-color: rgba(233, 229, 227, 0.6);
}

.notion-callout-orange_background {
  background-color: rgba(250, 235, 221, 0.6);
}

.notion-callout-yellow_background {
  background-color: rgba(251, 243, 219, 0.6);
}

.notion-callout-green_background {
  background-color: rgba(221, 237, 226, 0.6);
}

.notion-callout-blue_background {
  background-color: rgba(221, 235, 241, 0.6);
}

.notion-callout-purple_background {
  background-color: rgba(234, 228, 242, 0.6);
}

.notion-callout-pink_background {
  background-color: rgba(244, 223, 235, 0.6);
}

.notion-callout-red_background {
  background-color: rgba(251, 228, 228, 0.6);
}

/* Table of Contents */
.notion-table-of-contents {
  padding: 1rem;
  background-color: rgba(247, 246, 243, 0.3);
  border-radius: 3px;
  margin: 1rem 0;
}

.notion-table-of-contents ul {
  margin: 0;
  padding-left: 1.2rem;
}

.notion-table-of-contents li {
  margin: 0.25rem 0;
  padding: 0;
}

.notion-toc-link {
  color: #37352f;
  opacity: 0.8;
  text-decoration: none;
  transition: opacity 0.2s ease;
}

.notion-toc-link:hover {
  opacity: 1;
  text-decoration: underline;
}

/* Toggle blocks */
.notion-toggle {
  margin: 1rem 0;
  border-radius: 4px;
  width: 100%;
  background-color: #ffffff;
  border: 1px solid rgba(55, 53, 47, 0.09);
  box-shadow: rgba(15, 15, 15, 0.05) 0px 0px 0px 1px,
              rgba(15, 15, 15, 0.05) 0px 1px 3px;
  transition: box-shadow 0.2s ease;
}

.notion-toggle:hover {
  box-shadow: rgba(15, 15, 15, 0.05) 0px 0px 0px 1px,
              rgba(15, 15, 15, 0.05) 0px 3px 6px;
}

.notion-toggle-summary {
  cursor: pointer;
  padding: 0.75rem 1rem;
  user-select: none;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap:.5rem;
  border-bottom: 1px solid transparent;
  font-size:14px;
}

.notion-toggle-summary p{
    margin:0;
}

.notion-toggle[open] .notion-toggle-summary {
  border-bottom: 1px solid rgba(55, 53, 47, 0.09);
}

.notion-toggle-summary::before {
  content: "▶";
  display: inline-block;
  font-size: 0.75rem;
  color: rgba(55, 53, 47, 0.65);
  transition: transform 0.15s ease;
}

.notion-toggle[open] .notion-toggle-summary::before {
  transform: rotate(90deg);
}

.notion-toggle-content {
  padding:.5rem;
  /* Don't set display here - it will be set by JavaScript */
  width: auto; /* Allow content to fill the toggle container */
  /* line-height: 1.5; */
}

.notion-toggle-h1 .notion-toggle-summary {
  font-size: 1.5rem;
  font-weight: 600;
  padding-bottom: 0.5rem;
}

.notion-toggle-h2 .notion-toggle-summary {
  font-size: 1.25rem;
  font-weight: 600;
}

.notion-toggle-h3 .notion-toggle-summary {
  font-size: 1.125rem;
  font-weight: 600;
}

/* Custom styling for code tags inside toggles */
.notion-toggle code {
  font-size: 0.9em;
  padding: 0.15em 0.4em;
  border-radius: 4px;
}

/* Better styling for Notion colors in toggles */
.notion-toggle .notion-green,
.notion-toggle .notion-red,
.notion-toggle .notion-blue {
  font-weight: inherit;
}

/* Videos inside toggles */
.notion-toggle .notion-video-figure {
  margin: 1rem 0;
  width: 100%;
}

.notion-toggle .notion-video {
  width: 100%;
  border-radius: 4px;
  box-shadow: rgba(15, 15, 15, 0.1) 0px 0px 0px 1px,
              rgba(15, 15, 15, 0.1) 0px 2px 4px;
}

/* Colored toggles */
.notion-toggle-colored {
  border-color: transparent;
}

/* Apply color styles to the entire toggle */
.notion-toggle.notion-gray_background .notion-toggle-summary {
  background-color: rgba(241, 241, 239, 0.6);
}
.notion-toggle.notion-brown_background .notion-toggle-summary {
  background-color: rgba(244, 238, 238, 0.6);
}
.notion-toggle.notion-orange_background .notion-toggle-summary {
  background-color: rgba(251, 236, 221, 0.6);
}
.notion-toggle.notion-yellow_background .notion-toggle-summary {
  background-color: rgba(251, 243, 219, 0.6);
}
.notion-toggle.notion-green_background .notion-toggle-summary {
  background-color: rgba(237, 243, 236, 0.6);
}
.notion-toggle.notion-blue_background .notion-toggle-summary {
  background-color: rgba(231, 243, 248, 0.6);
}
.notion-toggle.notion-purple_background .notion-toggle-summary {
  background-color: rgba(244, 240, 247, 0.6);
}
.notion-toggle.notion-pink_background .notion-toggle-summary {
  background-color: rgba(249, 238, 243, 0.6);
}
.notion-toggle.notion-red_background .notion-toggle-summary {
  background-color: rgba(253, 235, 236, 0.6);
}

/* Content for colored toggles */
.notion-toggle.notion-gray_background .notion-toggle-content {
  background-color: rgba(241, 241, 239, 0.3);
}
.notion-toggle.notion-brown_background .notion-toggle-content {
  background-color: rgba(244, 238, 238, 0.3);
}
.notion-toggle.notion-orange_background .notion-toggle-content {
  background-color: rgba(251, 236, 221, 0.3);
}
.notion-toggle.notion-yellow_background .notion-toggle-content {
  background-color: rgba(251, 243, 219, 0.3);
}
.notion-toggle.notion-green_background .notion-toggle-content {
  background-color: rgba(237, 243, 236, 0.3);
}
.notion-toggle.notion-blue_background .notion-toggle-content {
  background-color: rgba(231, 243, 248, 0.3);
}
.notion-toggle.notion-purple_background .notion-toggle-content {
  background-color: rgba(244, 240, 247, 0.3);
}
.notion-toggle.notion-pink_background .notion-toggle-content {
  background-color: rgba(249, 238, 243, 0.3);
}
.notion-toggle.notion-red_background .notion-toggle-content {
  background-color: rgba(253, 235, 236, 0.3);
}

/* For text color on the toggles */
.notion-toggle.notion-gray .notion-toggle-summary::before,
.notion-toggle.notion-brown .notion-toggle-summary::before,
.notion-toggle.notion-orange .notion-toggle-summary::before,
.notion-toggle.notion-yellow .notion-toggle-summary::before,
.notion-toggle.notion-green .notion-toggle-summary::before,
.notion-toggle.notion-blue .notion-toggle-summary::before,
.notion-toggle.notion-purple .notion-toggle-summary::before,
.notion-toggle.notion-pink .notion-toggle-summary::before,
.notion-toggle.notion-red .notion-toggle-summary::before {
  color: inherit;
  opacity: 0.7;
}

/* File blocks */
.notion-file {
  margin: 1rem 0;
  padding: 0.75rem 1rem;
  border: 1px solid rgba(55, 53, 47, 0.16);
  border-radius: 3px;
  transition: background-color 0.2s;
}

.notion-file:hover {
  background-color: rgba(55, 53, 47, 0.03);
}

.notion-file-link {
  display: flex;
  align-items: center;
  color: #37352f;
  text-decoration: none;
}

.notion-file-icon {
  margin-right: 0.75rem;
  font-size: 1.25rem;
}

.notion-file-name {
  font-size: 0.875rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Embed blocks */
.notion-embed {
  margin: 1.5rem 0;
  border-radius: 3px;
  overflow: hidden;
}

.notion-embed iframe {
  width: 100%;
  margin: 0;
}

/* Text colors */
.notion-gray {
  color: rgba(120, 119, 116, 1);
}

.notion-brown {
  color: rgba(159, 107, 83, 1);
}

.notion-orange {
  color: rgba(217, 115, 13, 1);
}

.notion-yellow {
  color: rgba(203, 145, 47, 1);
}

.notion-green {
  color: rgba(68, 131, 97, 1);
}

.notion-blue {
  color: rgba(51, 126, 169, 1);
}

.notion-purple {
  color: rgba(144, 101, 176, 1);
}

.notion-pink {
  color: rgba(193, 76, 138, 1);
}

.notion-red {
  color: rgba(212, 76, 71, 1);
}

/* Background colors */
.notion-gray_background {
  background-color: rgba(241, 241, 239, 1);
}

.notion-brown_background {
  background-color: rgba(244, 238, 238, 1);
}

.notion-orange_background {
  background-color: rgba(251, 236, 221, 1);
}

.notion-yellow_background {
  background-color: rgba(251, 243, 219, 1);
}

.notion-green_background {
  background-color: rgba(237, 243, 236, 1);
}

.notion-blue_background {
  background-color: rgba(231, 243, 248, 1);
}

.notion-purple_background {
  background-color: rgba(244, 240, 247, 0.8);
}

.notion-pink_background {
  background-color: rgba(249, 238, 243, 0.8);
}

.notion-red_background {
  background-color: rgba(253, 235, 236, 1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .notion-content h1 {
    font-size: 1.5rem;
  }

  .notion-content h2 {
    font-size: 1.25rem;
  }

  .notion-content h3 {
    font-size: 1.125rem;
  }

  .notion-content iframe,
  .notion-content video {
    max-height: 240px;
  }
}

/* Page content spacing */
.notion-content > *:first-child {
  margin-top: 0;
}

.notion-content > *:last-child {
  margin-bottom: 0;
}

/* Empty content placeholder */
.notion-empty-content {
  color: #888;
  font-style: italic;
  padding: 0.5rem 0;
  font-size: 0.9rem;
}

</style>
