// User type
export interface User {
  object: 'user'
  id:     string
}

// Block type
export interface Block {
  object: 'block'
  id:     string
  parent: {
    type:    string
    page_id: string
  }
  created_time:     string
  last_edited_time: string
  created_by:       User
  last_edited_by:   User
  has_children:     boolean
  archived:         boolean
  in_trash:         boolean
  type:             string
  [key: string]:    any
}

// Rich text content
export interface RichText {
  type: string
  text: {
    content: string
    link:    { url: string } | null
  }
  annotations: {
    bold:          boolean
    italic:        boolean
    strikethrough: boolean
    underline:     boolean
    code:          boolean
    color:         string
  }
  plain_text: string
  href:       string | null
}

// Basic content with rich text
export interface TextContent {
  rich_text:     RichText[]
  color?:        string
  [key: string]: any
}

// Generic block content
export interface BlockContent {
  [key: string]: any
}

// Main response type
export interface NotionBlocksResponse {
  object:      'list'
  results:     Block[]
  next_cursor: null | string
  has_more:    boolean
  type:        'block'
  block:       Record<string, unknown>
  request_id:  string
}
