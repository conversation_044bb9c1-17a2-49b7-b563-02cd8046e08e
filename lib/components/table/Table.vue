<script setup lang="ts" generic="Para<PERSON> extends BaseParams, R, C = R">

import { useRoute } from 'vue-router'
import { searchModel } from '@lib/scripts/utils'
import { formatRecordValue, removeTableFromStorage, saveTableToStorage } from '@lib/store/table'
import { computed, defineComponent, h, onBeforeUnmount, onMounted, ref, render, watch } from 'vue'

import Row from '@lib/components/table/Row.vue'
import Cell from '@lib/components/table/Cell.vue'
import Icon from '@lib/components/blocks/Icon.vue'
import Loader from '@lib/components/blocks/Loader.vue'
import Button from '@lib/components/button/Button.vue'
import Toolbar from '@lib/components/utils/Toolbar.vue'
import Checkbox from '@lib/components/inputs/Checkbox.vue'
import Pagination from '@lib/components/utils/Pagination.vue'
import NestedView from '@lib/components/table/NestedView.vue'
import TableSearchBar from '@lib/components/utils/TableSearchBar.vue'
import BatchOptionsBar from '@lib/components/table/BatchOptionsBar.vue'

import type { Ref } from 'vue'
import type { IconName } from '@lib/store/icon'
import type { RouteLocationRaw } from 'vue-router'
import type { BatchOption, RowClickPayload, StorageTableData, TableReference, TableReferenceCell } from '@lib/types/tableTypes'

type TableRecord = Tablify<R, C>

interface PaginationOptions {
  total:            number
  compact?:         boolean // ------------------- If enabled, the pagination will have only page controls.
  maxPages:         number
  compactSize?:     number // -------------------- The page size overwrite.
  statePagination?: boolean // ------------------- The pagination is managed by the state, there will be no params in the route.
}

interface SearchOptions {
  enabled:         boolean
  searchKey?:      keyof Params // --------------- The key to search in the params.
  placeholder?:    string // --------------------- The placeholder for the search input.
  stateSearch?:    boolean // -------------------- If enabled, the search will filter the records instead of changing a param in the route query.
  stateSearchKey?: keyof R | ( keyof R )[] // ---- The key to search in the records.
}

interface TableProps {

  name:                 string // ----------------- The name of the table. This prop is required for the table to be stored properly.
  grow?:                boolean // ---------------- If enabled, the table will grow to the available height of it's container.
  icon?:                IconName // --------------- The icon next to the table name.
  flex?:                boolean // ---------------- If enabled, the table cells will flex to the available size.
  nested?:              boolean // ---------------- If true, this table is a nested table.
  pending?:             boolean
  disabled?:            boolean
  selectable?:          boolean // ---------------- If true, the table will have a checkbox column.
  hideLabels?:          TwMediaQueries | boolean
  hideHeader?:          boolean
  customEmptyMessage?:  string
  enableColumnChooser?: boolean

  schema:           ( record: R ) => TableSchema<R>
  params?:          Params
  records:          TableRecord[]
  recordMapKey:     keyof R
  batchOptions?:    BatchOption<R>[]
  recordOptions?:   ( record: R ) => DropListOption[]
  toolbarOptions?:  ToolbarOption[]
  sharedReference?: TableReference
  selectedRecords?: R[]

  search?:     SearchOptions
  pagination?: PaginationOptions

  /**
   * The name of the current resource in singular. eg: Product or Order.
   * This is used to determine the resource name in the batch options and improve the wording.
   */
  resourceName?: LooseAutoComplete<'Order' | 'Product' | 'Asn' | 'Item' | 'Record'>

}

const props = withDefaults( defineProps<TableProps>(), { flex: true, resourceName: 'Record', icon: 'bulk' })

defineEmits<{ onRowClick: [ payload: RowClickPayload<R> ] }>()

const root: HTMLElement         = document.querySelector( ':root' )
const route                     = useRoute()
const parser                    = new DOMParser()
const sharedRef                 = defineModel<TableReference>( 'sharedReference' )
const reference                 = props.sharedReference ? sharedRef : ref<TableReference>()
const tableElement              = ref<HTMLDivElement>( null )
const referenceElement          = ref<HTMLDivElement>( null )
const tableElementWidth         = ref<number>( 0 )
const areAllColumnsSelected     = ref<boolean>( null )
const areSomeColumnsSelected    = ref<boolean>( null )

/*
* The selected records are managed in the scope of the table.
* You can access them trough the slot props in the batch selection slot.
* However there is an option to bind the selected records to a parent component,
* which can be used to manage the selected records outside of the table.
* If the state and bounded records are not matching, the state is updated to match the bounded records.
*/

const selected                  = defineModel<R[]>( 'selectedRecords', { default: [] })
const selectAll                 = computed<boolean>(() => hasSelectableRecords.value && displayedRecords.value.filter( r => !r.tableRecordSelectDisabled ).every( item => stateSelectedRecords.value.map( s => s[props.recordMapKey] ).includes( item[props.recordMapKey] )))
const stateSearchQuery          = ref<string>( null )
const hasSelectableRecords      = computed<boolean>(() => displayedRecords.value.filter( r => !r.tableRecordSelectDisabled ).length > 0 )
const stateSelectedRecords      = ref<TableRecord[]>( [] ) as Ref<TableRecord[]>
const filteredSelectedRecords   = ref<TableRecord[]>( [] ) as Ref<TableRecord[]>
const areSelectedRecordsInSync  = computed<boolean>(() => selected.value.length === stateSelectedRecords.value.length )

watch( areSelectedRecordsInSync, ( n ) => {

  if ( !n ) {
    const records = tableRecords.value.filter( r => selected.value.map( s => s[props.recordMapKey] ).includes( r[props.recordMapKey] ))
    stateSelectedRecords.value = records
  }

})

/*
 * Table Globals
 */

const viewParams        = defineModel<Params>( 'params', { default: {} })
const tableRecords      = computed<TableRecord[]>(() => Array.isArray( props.records ) ? props.records : [] )
const showBatchOptions  = computed<boolean>(() => stateSelectedRecords.value.length > 0 )

const displayedRecords  = computed<TableRecord[]>(() =>

  filteredSelectedRecords.value.length > 0
    ? searchModel( filteredSelectedRecords.value, stateSearchQuery.value, props?.search?.stateSearchKey )
    : searchModel( tableRecords.value, stateSearchQuery.value, props?.search?.stateSearchKey )

)

/*
 * The following offsets are used to calculate the width of the table cells.
 * The offsets are used to calculate the width of the table cells based on the
 * expandable rows, selectable rows, and status rows.
 */

const selectOffset = computed(() => props.selectable ? 40 : 0 ) // ---------------------------------------------------------------- 40px offset if there is selectable rows in the table.
const expandOffset = computed(() => checkExpandOffset( tableRecords.value ) ? 40 : 0 ) // ----------------------------------------- 40px offset if there is expandable rows in the table with default trigger.
const statusOffset = computed(() => tableRecords.value.some( r => r.tableRecordStatus ) ? 40 : 0 ) // ----------------------------- 40px offset if there is rows with status.
const stickyLength = computed(() => props.schema( null ).filter( c => c.sticky ).length ) // -------------------------------------- Borders offset from the sticky cells.
const totalOffsets = computed(() => stickyLength.value + statusOffset.value + selectOffset.value + expandOffset.value + 1 ) // ---- Total offset of the table.

/*
 * ---- TABLE NESTED METHODS
 */

/**
 * Check if the table has expandable rows with default trigger.
 * @param {TableRecord[]} records - The records to check.
 * @returns {boolean} - True if the table has expandable rows with default trigger, false otherwise.
 */
function checkExpandOffset( records: TableRecord[] ): boolean {

  const hasNested = records.some( r => !!r.nested ) // ---------------------------------------- Check if the table has nested options.

  if ( !hasNested ) // ------------------------------------------------------------------------ If there are no nested options, return false.
    return false

  const triggerTypes = [] // ------------------------------------------------------------------ Array to store the trigger types.

  records.forEach(( record ) => { // ---------------------------------------------------------- For each record,

    const nested = record?.nested || null // -------------------------------------------------- Get the nested options.

    if ( !nested ) // ------------------------------------------------------------------------- If there are no nested options, return.
      return

    if ( Array.isArray( nested )) { // -------------------------------------------------------- If the nested options are an array,

      nested.forEach(( n ) => { // ------------------------------------------------------------ For each nested option,

        if ( n.trigger?.type ) // ------------------------------------------------------------- If the nested option has a trigger type,
          triggerTypes.push( n.trigger.type ) // ---------------------------------------------- Add the trigger type to the array.

        else if ( !n?.trigger ) // ------------------------------------------------------------ If the nested option does not have a trigger,
          triggerTypes.push( 'default' ) // --------------------------------------------------- Add the default trigger type to the array.

      })

    }

    else if ( nested.trigger?.type ) { // ----------------------------------------------------- If the nested options are not an array and have a trigger type,
      triggerTypes.push( nested.trigger.type ) // --------------------------------------------- Add the trigger type to the array.
    }

    else if ( !nested?.trigger ) { // --------------------------------------------------------- If the nested option does not have a trigger,
      triggerTypes.push( 'default' ) // ------------------------------------------------------- Add the default trigger type to the array.
    }

  })

  const hasTriggerType    = triggerTypes.length > 0 // ---------------------------------------- Check if there is a trigger type.
  const hasDefaultTrigger = hasTriggerType ? triggerTypes.includes( 'default' ) : false // ---- Check if there is a default trigger type.

  if ( hasTriggerType ) // -------------------------------------------------------------------- If there is a trigger type,
    return hasDefaultTrigger // --------------------------------------------------------------- return true if there is a default trigger type.

  return true // ------------------------------------------------------------------------------ If there are nested options but no trigger type, return true.

}

/**
 * Maps the nested options to a list.
 * Removes nested options with the same name.
 * @param {TableRecord} record - The record to map.
 * @returns {Nested<R, C, Record<string, any>>[]} - The mapped nested options.
 */
function mapNestedToList( record: TableRecord ): Nested<R, C, Record<string, any>>[] {

  if ( !record.nested )
    return []

  if ( Array.isArray( record.nested )) { // ------------------------------------------------ If the nested options are an array,

    // Remove nested options with the same name.
    // This will allow different triggers for the
    // same nested option, if they share the same name.

    const uniqueNested = new Map<string, Nested<R, C, Record<string, any>>>() // ----------- Create a map to store unique nested options.

    record.nested.forEach(( item ) => { // ------------------------------------------------- For each nested option,

      if ( !uniqueNested.has( item.name )) { // -------------------------------------------- If the map does not have the nested option,
        uniqueNested.set( item.name, item ) // --------------------------------------------- Add the nested option to the map.
      }

    })

    return Array.from( uniqueNested.values()) // ------------------------------------------- Convert the map to an array.

  }

  else {
    return [ record.nested ] // ------------------------------------------------------------ If the nested options are not an array, return it as an array.
  }

}

/*
  * ---- TABLE TOOLBAR
  *
  * The toolbar options are displayed in the top right corner of the table.
  * They are used for displaying the table search bar if enabled,
  * and external options if provided trough the toolbarOptions prop.
  */

/**
 * Generates the table toolbar options based on the props.
 * @returns {ToolbarOption[]} - The generated toolbar options.
 */

function generateTableToolbar(): ToolbarOption[] {

  let options: ToolbarOption[] = []

  if ( props?.search?.enabled ) {

    options.push({
      id:      'table-search',
      name:    'Search',
      icon:    { name: 'search', size: 'm' },
      slot:    'table-search',
      compact: true,
    })

  }

  if ( props?.toolbarOptions ) {
    options = [ ...options, ...props.toolbarOptions ]
  }

  return options

}

const tableToolbarOptions = computed(() => generateTableToolbar())

/*
 * ---- TABLE OPTIONS
 *
 * The table options are scoped to the table.
 * They are used for resetting the rows and similar actions.
 */

/**
 * Reset Column Sizes
 * Resets the entire table reference.
 */

function resetColumnSizes() {

  removeTableFromStorage( reference.value, route )

  reference.value = null

  generateLabelsReference()
  generateTableReference()
  resizeTableReference()

}

const tableOptions = computed<DropListOption[]>(() => [
  {
    id:   1,
    name: 'Reset Column Sizes',
    icon: {
      name: 'reset-columns',
      size: 'm'
    },
    action: () => resetColumnSizes()
  }
] )

/**
 * Extracts a table from the storage based on the route and table name
 * @returns The extracted table reference
 */

function extractTableFromStorage(): TableReference {

  // Retrieve stored tables from local storage

  const storedTables = JSON.parse( localStorage.getItem( 'table-data' )) as StorageTableData[]

  // Create an empty table reference with the given name

  const emptyTable: TableReference = {
    name:  props.name,
    cells: []
  }

  // If no stored tables exist, return the empty table

  if ( !storedTables )
    return emptyTable

  // Find the tables for the current route

  const tables = storedTables.find( t => t.route === route.path )?.tables

  // If no tables are found for the route, return the empty table

  if ( !tables )
    return emptyTable

  // Find the table with the given name, or return the empty table if not found

  return tables.find( t => t.name === props.name ) ?? emptyTable

}

/**
 * Calculates the size of a cell based on the cell options
 * @param {string | TableSchemaItemComponent} value - The value of the cell
 * @param {TableSchemaItem<R>} options - The cell options
 * @param {boolean} isLabel - Whether the cell is a label cell
 * @returns The calculated cell size
 */

function calculateCellSize( value: string | boolean | TableSchemaItemComponent, options: TableSchemaItem<R>, isLabel: boolean ): number {

  // Create a temporary element to calculate the width of the cell

  const cellElementRef = document.createElement( 'div' )
  const fixedCellWidth = options?.fixedSize ? typeof options.fixedSize === 'string' ? options.fixedSize : `${options.fixedSize}px` : null

  // If the cell has a sort key, we need to add an offset to the cell width

  const sortButtonOffset = options?.sortKey ? 28 : 4

  // If the cell has a status type, we need to add an offset to the cell width

  const statusOffset = options?.status?.type ? 40 : 0

  // If the cell has a fixed width, we can just set the width of the cell

  if ( fixedCellWidth ) {

    const child = document.createElement( 'div' )

    child.style.width = fixedCellWidth
    child.style.display = 'block'
    child.style.minWidth = fixedCellWidth
    child.style.maxWidth = fixedCellWidth

    cellElementRef.appendChild( child )
    referenceElement.value.appendChild( cellElementRef )

    return Math.min( cellElementRef.clientWidth + sortButtonOffset, 400 )

  }

  // If the cell is a component cell, we need to render
  // the component with vue to get the width of the cell.

  if ( typeof value !== 'boolean' && typeof value !== 'string' && value?.hasOwnProperty( 'component' )) {

    // Define a temporary component to render the cell
    // This is necessary because vue needs scope defined component to preserve reactivity.

    const cellComponent = defineComponent({
      render() { return h( value.component, value.props, value.slots ) }
    })

    // Append the component to the cell element and get the width

    render( h( cellComponent ), cellElementRef )

    cellElementRef.classList.add( 'px-4' )
    referenceElement.value.appendChild( cellElementRef )

    return Math.min( cellElementRef.clientWidth + sortButtonOffset, 400 )

  }

  // If the cell is a text cell, we can just append the text to the cell element

  const child = parser.parseFromString( String( value ), 'text/html' ).body.firstChild

  cellElementRef.appendChild( child )
  cellElementRef.classList.add( 'w-max', 'px-4', 'truncate', isLabel ? 'font-medium' : 'font-normal' )

  referenceElement.value.appendChild( cellElementRef )

  return Math.min( cellElementRef.clientWidth + sortButtonOffset + statusOffset, 400 )

}

/**
 * Sets the size of a cell
 * @param {number} size - The size of the cell
 * @param {number} index - The index of the cell
 * @param {TableReferenceCell} cell - The cell reference
 * @param {TableSchemaItem<R>} cellOptions - The cell options
 * @returns The updated cell reference
 */

function setCellSize( size: number, index: number, cell: TableReferenceCell, cellOptions: TableSchemaItem<R> ) {

  const newCell: TableReferenceCell = {
    hide:     cellOptions.hidden,
    index,
    sizes:    { init: 0, flex: 0, drag: 0 },
    align:    cellOptions.align,
    label:    cellOptions.label,
    toggle:   true,
    lockFlex: cellOptions.fixedSize ? true : cellOptions.lockFlex
  }

  cell = cell ?? newCell
  cell.align = cellOptions?.format === 'currency' ? 'right' : 'left'

  cell.sizes.init = Math.max( cell.sizes.init, size )

  return cell

}

/**
 * Generates labels reference
 * @param {TableReference} tableReference - Optional table reference
 */

function generateLabelsReference( tableReference?: TableReference ) {

  // If tableReference is not provided, extract table from storage

  tableReference = tableReference ?? extractTableFromStorage()

  // Iterate through table schema

  const labelsSchema = props.schema( null )

  for ( let index = 0; index < labelsSchema.length; index++ ) {

    const cellOptions = labelsSchema[index]

    // Calculate initial size of cell

    const size = calculateCellSize( cellOptions.label, cellOptions, true )

    // Set cell size

    tableReference.cells[index] = setCellSize( size, index, tableReference.cells[index], cellOptions )

  }

  // Update the table reference

  reference.value = tableReference

  // Check if all columns are selected(visible)

  areAllColumnsSelected.value = reference.value?.cells?.every( c => c.toggle )

}

/**
 * Generates a table reference based on the provided records and schema
 */

function generateTableReference() {

  if ( !tableRecords.value )
    return

  if ( tableRecords.value.length === 0 )
    return

  const ref = reference.value ?? extractTableFromStorage()

  // Measure Data Cells

  for ( const record of tableRecords.value ) {

    const recordSchema = props.schema( record )

    for ( let index = 0; index < props.schema( record ).length; index++ ) {

      const schemaCell  = recordSchema[index]
      const recordValue = formatRecordValue( record, schemaCell )

      // If there is align property set, use it,
      // otherwise align the currency format to right.

      schemaCell.align = !schemaCell?.align && schemaCell.format === 'currency' ? 'right' : 'left'

      // Calculate the size of the cell

      const size = calculateCellSize( recordValue, schemaCell, false )

      // Set the cell size

      ref.cells[index] = setCellSize( size, index, ref.cells[index], schemaCell )

    }

  }

  // Update the reference value and reset the reference element

  reference.value = ref
  referenceElement.value = null

  resizeTableReference()

}

/**
 * Resizes the table reference based on the given offset.
 * @param {number} offset - The offset for resizing the table reference.
 */

function resizeTableReference( offset?: number ) {

  // Get the table element width.

  tableElementWidth.value = tableElement.value?.clientWidth
  root.style.setProperty( '--table-width', `${tableElementWidth.value}px` )

  // If props.flex is falsy, return early

  if ( !props.flex )
    return

  // If reference.value is falsy, return early

  if ( !reference.value )
    return

  const cells = reference.value.cells.filter( cell => !cell.hide && cell.toggle )

  // Set the offsetRow to the given offset or to rowOffset.value
  // The offset comes from the expand and select boxes in the front of the row.
  // If the row is expandable, 40px offset is added, same if selectable.
  // If both are enabled, 80px of offset is added.

  const offsetRow = offset ?? totalOffsets.value

  // Calculate the total width of the cells in the reference table

  const cellsWidth = cells.reduce(( acc, cell ) => acc + ( cell.sizes.drag ? cell.sizes.drag : cell.sizes.init ), 0 )

  // Count the number of cells that are stuck (not resizable)

  const stuckCellsLength = cells.filter( cell => cell.sizes.init > cell.sizes.flex ).length

  // Count the number of cells that are locked (not resizable)

  const lockedCellsLength = cells.filter( cell => cell.sizes.drag || cell.lockFlex ).length

  // Calculate the remaining flex width available for resizing

  const remainingFlexWidth = ( tableElementWidth.value - offsetRow ) - cellsWidth

  // Calculate the flex amount that each resizable cell should have

  const flexAmount = remainingFlexWidth / ( cells.length - ( stuckCellsLength + lockedCellsLength ))

  // Resize each resizable cell based on the flex amount

  for ( const cell of cells ) {

    const cellFlex = ( flexAmount + cell.sizes.init ) // - stickyLength.value
    cell.sizes.flex = Math.max( cellFlex, cell.sizes.init )

  }

}

/**
 * Save and resize table when column is toggled and checks if all columns are selected.
 */

function toggleColumnVisibility() {

  areAllColumnsSelected.value = reference?.value?.cells?.every( e => e?.toggle )
  areSomeColumnsSelected.value = reference?.value?.cells?.some( e => e?.toggle )

  saveTableToStorage( reference.value, route )
  resizeTableReference()

}

/**
 * Selects all columns and makes them visible.
 */

function selectAllColumns() {

  reference.value.cells.forEach( c => c.toggle = true )

  saveTableToStorage( reference.value, route )
  resizeTableReference()

}

/**
 * Deselects a record from the selected records
 * @param {R} record - The record to deselect
 */

function deselectRecord( record: R ) {

  const index = selected.value.findIndex( r => r[props.recordMapKey] === record[props.recordMapKey] )

  if ( index > -1 )
    selected.value.splice( index, 1 )

  const stateIndex = stateSelectedRecords.value.findIndex( r => r[props.recordMapKey] === record[props.recordMapKey] )

  if ( stateIndex > -1 )
    stateSelectedRecords.value.splice( stateIndex, 1 )

}

/**
 * Toggles the selection of a record
 * @param {boolean} isSelected - Whether the record is selected
 * @param {TableRecord} record - The record to select
 */

function toggleSelectRecord( isSelected: boolean, record: TableRecord ) {

  if ( isSelected ) {
    selected.value.push( record )
    stateSelectedRecords.value.push( record )
  }

  else { deselectRecord( record ) }

}

/**
 * Deselects all records from the selected records
 */

function deselectAll() {
  tableRecords.value.forEach( item => deselectRecord( item ))
}

/**
 * Cancels the batch actions
 */

function cancelBatchActions() {
  selected.value = []
  stateSelectedRecords.value = []
}

/**
 * Toggles the selection of all records
 */

function toggleSelectAll() {

  if ( selectAll.value ) {
    deselectAll()
  }

  else {

    deselectAll()

    selected.value = [ ...selected.value, ...tableRecords.value.filter( r => !r.tableRecordSelectDisabled ) ]
    stateSelectedRecords.value = [ ...stateSelectedRecords.value, ...tableRecords.value.filter( r => !r.tableRecordSelectDisabled ) ]

  }

}

/**
 * Resize the table reference when the window is resized
 */

const resizeObserver = new ResizeObserver(() => resizeTableReference( null ))

/**
 * Resize the table when some of the offsets are changed.
 */

watch( totalOffsets, n => resizeTableReference( n[0] ))

watch(() => props.schema( null ), ( n ) => {

  n.forEach(( column, index ) => {
    reference.value.cells[index].hide = column.hidden
  })

  resizeTableReference()

})

/**
 * Watch the records and reference element and generate table reference and resize it when the records change
 */

const recordsMap = computed(() => tableRecords.value.map( r => r[props.recordMapKey] ).join( '-' ))

watch( [ recordsMap, referenceElement ], ( n, o ) => {

  if (( n[0] !== o[0] ) && n[1] ) {
    generateTableReference()
    resizeTableReference()
  }

}, { immediate: true })

/**
 * If the search key is not present in the route, set it to null
 * @param {RouteLocationRaw} URLParams - The route location
 */

function handleSearchKeyOnRouteChange( URLParams: RouteLocationRaw, oldURLParams: RouteLocationRaw ) {

  if ( !props?.search?.searchKey ) // ------------------------------- If there is no search key prop, return
    return

  if ( !oldURLParams[String( props.search.searchKey )] ) // --------- If the search key is not present in the old URL params, return
    return

  if ( !URLParams[String( props.search.searchKey )] )
    viewParams.value[String( props.search.searchKey )] = null // ---- If the search key is not present in the new URL params, set it to null

}

/**
 * Watch the route and scroll to the top of the table when the route changes.
 */

watch( route, ( n, o ) => {
  handleSearchKeyOnRouteChange( n.query, o.query )
  tableElement.value.scrollTop = 0
})

onMounted(() => {
  generateLabelsReference( reference.value )
  generateTableReference()
  resizeObserver.observe( tableElement.value )
})

onBeforeUnmount(() => resizeObserver.unobserve( tableElement.value ))

</script>

<template>

  <div
    class="w-full h-full md:max-h-full grid grid-rows-[max-content_max-content_1fr_max-content] md:grid-rows-[max-content_max-content_max-content_1fr] bg-layer-01 overflow-hidden"
    :class="{ 'md:h-full': grow, 'md:h-fit': !grow }"
  >

    <div ref="referenceElement" class="fixed -top-10 -left-[100vw] h-0 flex overflow-hidden opacity-0" />

    <!-- Table Header -->

    <div v-if="!hideHeader" class="w-full h-10 grid grid-cols-[max-content_1fr] bg-layer-01 border md:border-0 md:border-b border-border-subtle-00">

      <slot name="table-title">

        <div class="text-sm font-medium w-full h-10 px-4 flex items-center space-x-2">

          <Icon
            :name="icon"
            class="text-icon-interactive"
          />

          <p>Data: <span class="text-support-info">[{{ name }}]</span></p>

        </div>

      </slot>

      <Toolbar
        :options="tableToolbarOptions"
        @close-slot="() => {
          stateSearchQuery = null
          viewParams[String(search?.searchKey)] = null
        }"
      >

        <template #prefix>

          <div
            v-if="enableColumnChooser"
            v-tooltip="{ content: 'Column Chooser', wait: 500 }"
            class="h-full w-10 before:h-4 before:w-full separator-l"
          >

            <Button
              :options="[]"
              size="m"
              type="box"
              mode="ghost"
              icon="column"
              class="text-text-primary relative"
            >

              <template #droplist>

                <div class="text-sm">

                  <div class="px-4 py-2 sticky top-0 bg-layer-02 border-b border-border-subtle-00">

                    <Checkbox
                      v-model="areAllColumnsSelected"
                      :partial="areSomeColumnsSelected"
                      :disabled="areAllColumnsSelected"
                      @update:model-value="selectAllColumns"
                    >
                      Select All
                    </Checkbox>

                  </div>

                  <div v-for="col in reference.cells.filter(c => !c.hide)" :key="col.index" class="px-4 py-2">

                    <Checkbox v-model="col.toggle" class="text-sm" @update:model-value="toggleColumnVisibility">
                      {{ typeof col.label === 'string' ? col.label : col.label?.name }}
                    </Checkbox>

                  </div>

                </div>

              </template>

            </Button>

          </div>

        </template>

        <template #table-search>

          <TableSearchBar
            v-if="search?.stateSearch"
            v-model="stateSearchQuery"
            :name="name"
            :debounce="0"
            :expanded-search="true"
            :show-clear-button="false"
          />

          <TableSearchBar
            v-else
            :name="name"
            :model-value="viewParams[String(search?.searchKey)]"
            :expanded-search="true"
            :show-clear-button="false"
            @update:model-value="viewParams[String(search?.searchKey)] = $event"
          />

        </template>

        <template #filter>

          <div class="w-full text-sm h-full px-4 flex items-center gap-x-2 border-l border-border-subtle-00 bg-layer-02">

            <Icon name="filter" />
            <p class="truncate">
              Slot for Filters
            </p>

          </div>

        </template>

      </Toolbar>

      <div class="h-full">
        <slot name="table-head-helper" />
      </div>

    </div>

    <!-- Table Neck -->

    <div class="w-full h-auto">
      <slot name="table-neck" />
    </div>

    <!-- Batch Actions -->

    <div
      class="w-full relative transition-[height] row-start-4 md:row-start-auto"
      :class="{
        'h-0': !showBatchOptions,
        'h-10': showBatchOptions,
      }"
    >

      <BatchOptionsBar
        v-if="showBatchOptions"
        v-model:filtered="filteredSelectedRecords"
        :total="pagination?.total"
        :records="stateSelectedRecords"
        :page-total="tableRecords.length"
        :batch-options="batchOptions"
        :resource-name="String(resourceName)"
        :all-on-page-selected="selectAll"
        @close="cancelBatchActions"
        @select-all="toggleSelectAll"
      />

    </div>

    <!-- Table Records -->

    <div
      ref="tableElement"
      class="w-full h-full grid overflow-auto"
      :class="{ 'overflow-hidden': pending }"
    >

      <div
        v-if="reference"
        class="w-full h-full"
      >

        <!-- Labels Row -->

        <Row
          :record="null"
          :child="nested"
          :options="tableOptions"
          :head-row="true"
          :selected="selectAll"
          :table-name="name"
          :selectable="selectable"
          :expandable="expandOffset > 0"
          :has-status="statusOffset > 0"
          :disable-row-select="!hasSelectableRecords"
          :class="{
            'hidden': hideLabels === true,
            'sm:block hidden': hideLabels === 'sm',
            'md:block hidden': hideLabels === 'md',
            'lg:block hidden': hideLabels === 'lg',
            'xl:block hidden': hideLabels === 'xl',
            '2xl:block hidden': hideLabels === '2xl',
          }"
          @select="toggleSelectAll"
        >

          <template #default="{ headRow, rowSelected }">

            <Cell
              v-for="column, columnIndex in schema(null)"
              :key="column.key"
              v-model:cell="reference.cells[columnIndex]"
              v-model:params="viewParams"
              :flex="flex"
              :align="column.align ?? reference.cells[columnIndex].align"
              :value="column.label"
              :sticky="column.sticky"
              :resize="column.resize"
              :status="column.status"
              :nested="nested"
              :sort-key="column.sortKey"
              :head-cell="headRow"
              :reference="reference"
              :row-selected="rowSelected"
              cell-class="font-medium"
              @dragging="resizeTableReference"
            />

          </template>

        </Row>

        <!-- Data Rows -->

        <Transition name="view" mode="out-in">

          <div
            v-if="pending"
            :style="{ width: `${tableElementWidth}px` }"
            class="min-h-[calc(100%-5rem)] sticky left-0 p-10 flex items-center justify-center space-x-4"
          >

            <Loader :name="name" />

          </div>

          <div
            v-else-if="!pending && records.length === 0"
            :style="{ width: `${tableElementWidth}px` }"
            class="min-h-[calc(100%-5rem)] sticky left-0 p-10 flex items-center justify-center space-x-4"
          >

            <Icon class="text-icon-interactive" name="folder" size="m" />

            <p class="text-sm">
              {{ customEmptyMessage ?? $t('global.phrase.noRecordsFound', { name: name ?? $t('global.label.records') }) }}
            </p>

          </div>

          <div
            v-else
            class="w-full min-h-[calc(100%-5rem)] md:min-h-0"
          >

            <slot :reference>

              <Row
                v-for="record in displayedRecords"
                :key="String(record[props.recordMapKey])"
                :child="nested"
                :record="record"
                :options="recordOptions && !showBatchOptions ? recordOptions(record) : []"
                :selected="stateSelectedRecords.map(r => r[props.recordMapKey]).includes(record[props.recordMapKey])"
                :has-status="statusOffset > 0"
                :table-name="name"
                :selectable="selectable"
                :expandable="expandOffset > 0"
                @select="(isSelected) => toggleSelectRecord(isSelected, record)"
                @row-click="() => $emit('onRowClick', { record, isChild: nested, isParent: !!record?.nested })"
              >

                <template #default="{ headRow, rowSelected, triggers, expand, expandedByKey, toggleExpand }">

                  <Cell
                    v-for="column, columnIndex in schema(record)"
                    :key="column.key"
                    v-model:cell="reference.cells[columnIndex]"
                    :flex="flex"
                    :link="column.link"
                    :trigger="triggers.find(t => t.key === column.key) ?? null"
                    :align="column.align ?? reference.cells[columnIndex].align"
                    :value="formatRecordValue(record, column)"
                    :resize="column.resize"
                    :sticky="column.sticky"
                    :status="column.status"
                    :nested="nested"
                    :head-cell="headRow"
                    :reference="reference"
                    :row-selected="rowSelected"
                    :row-expanded="expand"
                    :row-expanded-by-key="expandedByKey"
                    :cell-class="column.class"
                    @dragging="resizeTableReference"
                    @trigger="toggleExpand"
                  />

                </template>

                <template #drawer="{ expandViewName }">

                  <NestedView
                    v-for="n in mapNestedToList(record).filter(n => n.name === expandViewName)"
                    :key="n.name"
                    :type="n.type"
                    :name="n?.name ?? `nested-${name}`"
                    :schema="n.type === 'nested' ? n?.schema : null"
                    :request="n.type !== 'slot' ? n?.request : null"
                    :component="n.type === 'slot' ? n?.component : null"
                    :lock-width="n?.lockWidth"
                    :nested-props="n.type === 'slot' ? n?.props : null"
                    :parent-width="tableElementWidth"
                    :parent-schema="schema"
                    :nested-records="n.type !== 'slot' ? n?.records : null"
                    :record-map-key="n.type !== 'slot' ? n?.recordMapKey : null"
                    :record-options="n.type !== 'slot' ? n?.recordOptions : null"
                    :table-reference="reference"
                    :has-expand-offset="expandOffset > 0"
                    :has-status-offset="statusOffset > 0"
                    :has-select-offset="selectOffset > 0"
                  />

                </template>

              </Row>

            </slot>

          </div>

        </Transition>

        <div
          v-if="pagination?.total > 1 && filteredSelectedRecords.length === 0"
          :style="{ width: `${tableElementWidth}px` }"
          class="h-auto sticky left-0 bottom-0 z-1 flex items-center justify-end bg-layer-01 border-t border-border-subtle-00"
        >

          <Pagination
            v-model:page="viewParams.page"
            v-model:page-size="viewParams.pageSize"
            :name="name"
            :total="pagination?.total"
            :compact="pagination?.compact"
            :disabled="pending || disabled"
            :max-pages="pagination?.maxPages"
            :data-length="records?.length"
            :compact-size="pagination?.compactSize"
            :state-pagination="pagination?.statePagination"
          />

        </div>

      </div>

    </div>

  </div>

</template>
