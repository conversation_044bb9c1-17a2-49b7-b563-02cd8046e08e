<script setup lang="ts">

import { checkValue } from '@lib/scripts/utils'

import Icon from '@lib/components/blocks/Icon.vue'

defineProps<{ status: TableRecordStatus }>()

</script>

<template>

  <div
    v-if="checkValue(status?.count)"
    v-tooltip:[status?.type].right="{ content: status?.message }"
    class="h-4 px-1 grid place-content-center border rounded-full cursor-default"
    :class="{
      'text-tag-color-red bg-tag-background-red border-tag-border-red': status?.type === 'error' && status?.count > 0,
      'text-tag-color-green bg-tag-background-green border-tag-border-green': status?.type === 'success' && status?.count > 0,
      'text-tag-color-yellow bg-tag-background-yellow border-tag-border-yellow': status?.type === 'warning' && status?.count > 0,
      'text-tag-color-cool-gray bg-tag-background-cool-gray border-tag-border-cool-gray opacity-50': checkValue(status?.count) && status?.count === 0,
    }"
  >

    <p class="text-xs font-semibold pointer-events-none">
      {{ status?.count }}
    </p>

  </div>

  <div
    v-else
    v-tooltip:[status?.type].bottom="{ content: status?.message }"
    class="h-4 w-4 grid place-content-center border rounded-full cursor-default"
    :class="{
      'text-tag-color-red bg-tag-background-red border-tag-border-red': status?.type === 'error',
      'text-tag-color-green bg-tag-background-green border-tag-border-green': status?.type === 'success',
      'text-tag-color-yellow bg-tag-background-yellow border-tag-border-yellow': status?.type === 'warning',
    }"
  >

    <Icon
      :name="status?.type === 'success' ? 'checkmark' : 'exclamation-mark'"
      size="s"
    />

  </div>

</template>
