<script setup lang="ts">

import type { RadioInputProps } from '@lib/types/inputTypes'

defineOptions({
  name: 'OptionsGroup'
})

withDefaults( defineProps<RadioInputProps<string | number>>(), {
  direction: 'vertical'
})

</script>

<template>

  <div class="px-4 py-2">

    <p v-if="!!label" class="text-[0.625rem] uppercase text-core pb-2">
      {{ label }} <span v-if="required" class="text-main relative">*</span>
    </p>

    <div

      :class="{
        'grid gap-y-2 place-items-start': direction === 'vertical',
        'flex flex-wrap gap-x-4 gap-y-2': direction === 'horizontal',
      }"
    >
      <slot />
    </div>

  </div>

</template>
