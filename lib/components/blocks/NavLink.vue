<script setup lang="ts">

import type { RouteLocationRaw } from 'vue-router'

interface Props {
  to: RouteLocationRaw
}

defineProps<Props>()

</script>

<template>

  <RouterLink
    :to="to"
    class="w-full h-8 hover:bg-core-30 px-4 flex items-center border-l-2 border-transparent focus-visible:outline-dashed outline-main"
    active-class="active-nav-link"
  >
    <slot />
  </RouterLink>

</template>

<style scoped>

  .active-nav-link {
    @apply font-semibold border-main bg-core-40 pointer-events-none;
  }

</style>
