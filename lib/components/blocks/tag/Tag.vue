<script setup lang="ts">
import { computed } from 'vue'
import Icon from '@lib/components/blocks/Icon.vue'
import type { IconName } from '@lib/store/icon'

type ColorScheme = 'red'
  | 'blue'
  | 'cyan'
  | 'teal'
  | 'gray'
  | 'green'
  | 'purple'
  | 'magenta'
  | 'cool-gray'
  | 'warm-gray'

const props = withDefaults( defineProps<{
  size?:           's' | 'm' | 'l' // ----------------------------------------- Tag size
  label?:          string // -------------------------------------------------- Label to be used in the tag component
  variant?:        'filter' | 'readonly' | 'selectable' | 'operational' // ---- Variant of tag functionality
  skeleton?:       boolean // ------------------------------------------------- Skeleton used when loading
  disabled?:       boolean // ------------------------------------------------- Disabled by property or if skeleton
  renderIcon?:     IconName // ------------------------------------------------ Optional prop to render a custom icon
  isSelected?:     boolean // ------------------------------------------------- Whether the tag is in selected state
  colorScheme?:    ColorScheme // --------------------------------------------- Color scheme of the tag (visual styling)
  clearAriaLabel?: string // -------------------------------------------------- Aria label for clear button
}>(), {
  size:           's',
  variant:        'filter',
  clearAriaLabel: 'Clear Filter',
})

const emit = defineEmits<{
  remove: [] // --------------------------------------------------------------- Emitted when tag is removed/cleared.
}>()

const availableColorSchemes = [ // -------------------------------------------- Define the available color schemes for random selection.
  'red',
  'blue',
  'cyan',
  'teal',
  'gray',
  'green',
  'purple',
  'magenta',
  'cool-gray',
  'warm-gray',
]

const isFilter      = computed(() => props.variant === 'filter' )
const isSelectable  = computed(() => props.variant === 'selectable' )
const isOperational = computed(() => props.variant === 'operational' )

const selected = defineModel( 'isSelected', { default: null })

const title = computed(() => {
  return isFilter.value ? props.clearAriaLabel : null
})

function getRandomColorScheme() {
  const randomIndex = Math.floor( Math.random() * availableColorSchemes.length )
  return availableColorSchemes[randomIndex]
}

const tagColorScheme = computed(() => {
  return props.colorScheme || getRandomColorScheme()
})

const tagClasses = computed(() => {

  const classes = [ 'tag' ] // ------------------- Base classes that always apply

  classes.push( `tag-size-${props.size}` ) // ---- Handle size variants

  if ( isSelectable.value ) { // ----------------- Handle tag selected states
    classes.push( 'tag-selectable' )

    if ( props.isSelected ) {
      classes.push( 'tag-selectable-selected' )
    }
  }

  else { // -------------------------------------- Handle non-selectable tag states

    if ( props.skeleton ) {
      classes.push( 'tag-skeleton' )
    }

    else { // ------------------------------------ Add visual styling based on colorScheme (always use tagColorScheme which may be random)

      classes.push( `tag-${tagColorScheme.value}` )

      if ( isOperational.value ) {
        classes.push( 'tag-operational' )
      }
    }

  }

  if ( props.disabled ) { // --------------------- Add disabled state if needed
    classes.push( 'tag-disabled' )
  }

  return classes
})

// Handle remove/clear tag action

function onRemove() {
  if ( !props.disabled ) {
    emit( 'remove' )
  }
}

</script>

<template>

  <div
    role="listitem"
    :class="tagClasses"
    :title="title"
    v-bind="isSelectable ? { tabindex: 0 } : {}"
    @click="isSelectable ? selected = !selected : null"
    @keydown.enter="isSelectable ? selected = !selected : null"
  >
    <!-- Custom icon (if provided) -->

    <Icon
      v-if="!skeleton && renderIcon"
      :name="renderIcon"
      size="s"
      class="tag-custom-icon"
    />

    <!-- Tag label -->

    <span class="tag-label">

      <slot>{{ label }}</slot>

    </span>

    <!-- Clear button (for filter tags) -->

    <button
      v-if="isFilter"
      class="tag-close-button"
      :title="clearAriaLabel"
      :disabled="disabled || null"
      :aria-label="clearAriaLabel"
      @click.stop.prevent="onRemove"
      @keydown.enter.stop.prevent="onRemove"
    >
      <Icon
        v-if="!skeleton"
        name="close-thin"
        size="s"
      />
    </button>

  </div>

</template>
