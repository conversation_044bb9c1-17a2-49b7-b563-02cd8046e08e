/* Default tag styles */
.default-design-system {

    .tag {
        @apply text-xs flex items-center rounded-full focus-visible:ring-2 focus-visible:ring-focus focus-visible:ring-offset-1 focus-visible:outline-none;
    }
    .tag-label {
        @apply select-none;
    }
    /* ================ Tag Sizes ================ */
    .tag-size-s {
        @apply px-2 h-[1.125rem];

        .tag-close-button {
            @apply w-[1.125rem] h-[1.125rem];
        }

    }

    .tag-size-m {
        @apply px-2 h-6;

        .tag-close-button {
            @apply w-[1.5rem] h-[1.5rem];
        }
    }

    .tag-size-l {
        @apply px-3 h-8;

        .tag-close-button {
            @apply w-[2rem] h-[2rem];
        }
    }

    /* ================ Tag Elements & Types ================ */

    .tag:has(.tag-close-button) {
        @apply pr-0;
    }

    .tag:has(.tag-custom-icon) {
        @apply pl-2;

        .tag-custom-icon {
            @apply mr-[0.625rem];
        }

    }

    .tag-close-button {
        @apply rounded-full flex justify-center items-center cursor-pointer ml-[2px] focus-visible:ring-1 focus-visible:ring-focus focus-visible:outline-none;
    }

    .tag-operational {
        @apply cursor-pointer;
    }

    /* ================ Color Schemes ================ */
    .tag-red {
        @apply text-tag-color-red bg-tag-background-red;

        .tag-close-button {
            @apply hover:bg-tag-hover-red;
        }

        &.tag-operational {
            @apply border border-tag-border-red hover:bg-tag-hover-red;
        }
    }

    .tag-blue {
        @apply text-tag-color-blue bg-tag-background-blue;

        .tag-close-button {
            @apply hover:bg-tag-hover-blue;
        }

        &.tag-operational {
            @apply border border-tag-border-blue hover:bg-tag-hover-blue;
        }
    }

    .tag-cyan {
        @apply text-tag-color-cyan bg-tag-background-cyan;

        .tag-close-button {
            @apply hover:bg-tag-hover-cyan;
        }

        &.tag-operational {
            @apply border border-tag-border-cyan hover:bg-tag-hover-cyan;
        }
    }

    .tag-teal {
        @apply text-tag-color-teal bg-tag-background-teal;

        .tag-close-button {
            @apply hover:bg-tag-hover-teal;
        }

        &.tag-operational {
            @apply border border-tag-border-teal hover:bg-tag-hover-teal;
        }
    }

    .tag-gray {
        @apply text-tag-color-gray bg-tag-background-gray;

        .tag-close-button {
            @apply hover:bg-tag-hover-gray;
        }

        &.tag-operational {
            @apply border border-tag-border-gray hover:bg-tag-hover-gray;
        }
    }

    .tag-green {
        @apply text-tag-color-green bg-tag-background-green;

        .tag-close-button {
            @apply hover:bg-tag-hover-green;
        }

        &.tag-operational {
            @apply border border-tag-border-green hover:bg-tag-hover-green;
        }
    }

    .tag-purple {
        @apply text-tag-color-purple bg-tag-background-purple;

        .tag-close-button {
            @apply hover:bg-tag-hover-purple;
        }

        &.tag-operational {
            @apply border border-tag-border-purple hover:bg-tag-hover-purple;
        }
    }

    .tag-magenta {
        @apply text-tag-color-magenta bg-tag-background-magenta;

        .tag-close-button {
            @apply hover:bg-tag-hover-magenta;
        }

        &.tag-operational {
            @apply border border-tag-border-magenta hover:bg-tag-hover-magenta;
        }
    }

    .tag-cool-gray {
        @apply text-tag-color-cool-gray bg-tag-background-cool-gray;

        .tag-close-button {
            @apply hover:bg-tag-hover-cool-gray;
        }

        &.tag-operational {
            @apply border border-tag-border-cool-gray hover:bg-tag-hover-cool-gray;
        }
    }

    .tag-warm-gray {
        @apply text-tag-color-warm-gray bg-tag-background-warm-gray;

        .tag-close-button {
            @apply hover:bg-tag-hover-warm-gray;
        }

        &.tag-operational {
            @apply border border-tag-border-warm-gray hover:bg-tag-hover-warm-gray;
        }
    }

    /* ================ States ================ */
    .tag-selectable {
        @apply text-tag-color-enabled bg-tag-selectable-background border border-tag-selectable-border hover:bg-tag-selectable-background-hover cursor-pointer;

        &.tag-selectable-selected {
            @apply bg-tag-selected-background text-tag-color-selected;
        }
    }

    /* High-specificity selectors for states to avoid !important */
    .tag.tag-disabled {
        @apply bg-tag-background-disabled text-text-disabled border-border-disabled pointer-events-none;
    }

    .tag.tag-skeleton {
        @apply bg-skeleton-background border border-skeleton-background min-w-[1rem];

        * {
            @apply bg-skeleton-background text-skeleton-background pointer-events-none;
        }
    }

    /* Override padding for tag with icon. Selector is specific to avoid important */
    .tag:not(.tag-disabled):not(.tag-skeleton).tag-icon {
        @apply pl-2;
    }

}
