<script setup lang="ts">

import { notificationQueue } from '@lib/store/snackbar'

import Snackbar from '@lib/components/utils/snackbar/Snackbar.vue'

</script>

<template>

  <div class="notification">

    <TransitionGroup name="notification">

      <Snackbar
        v-for="notification in notificationQueue"
        :key="notification.id"
        modifier="inverse"
        :severity="notification.severity || 'success'"
        type="notification"
        v-bind="notification"
        :strict="!!notification.inlineAction || !!notification.action || notification.strict"
      />

    </TransitionGroup>

  </div>

</template>
