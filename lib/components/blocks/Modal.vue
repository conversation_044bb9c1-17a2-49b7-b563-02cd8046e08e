<script setup lang="ts">

import { vOnClickOutside } from '@vueuse/components'

interface Props {
  strict?: boolean
}

interface Emits {
  ( eventName: 'close' ): void
}

const props = withDefaults( defineProps<Props>(), {
  strict: true
})

const emits = defineEmits<Emits>()

function handleClose() {
  emits( 'close' )
}

function onClickOutside() {
  if ( !props.strict )
    handleClose()
}

</script>

<template>
  <Teleport to="#app">
    <div
      class="fixed top-0 left-0 w-full h-full bg-core/50 z-70 font-sans backdrop-blur-[0.094rem]"
    >
      <Transition name="route" appear>
        <div v-on-click-outside="onClickOutside" class="w-full h-full relative ignore-outside">
          <slot :close="handleClose" />
        </div>
      </Transition>
    </div>
  </Teleport>
</template>
