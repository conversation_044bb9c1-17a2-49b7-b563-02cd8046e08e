/**
 * StatusBadge Styles:
 * - This file contains the styles for the StatusBadge component.
 * - The styles are organized into base styles, icon styles, and color schemes.
 */

 /* Base styles for the StatusBadge component */

 .default-design-system {

    .status-badge {
        @apply pr-2 grid grid-cols-[max-content_1fr] items-center border rounded-full text-xs;

        /* Styles for the icon container */
        .status-badge-icon {
            @apply size-[1.125rem] grid place-content-center;

            /* Styles for the icon shape */
            .status-badge-icon-shape {
                @apply size-2 rounded-full;
            }

        }

        /* Color schemes for the StatusBadge component */
        &.status-badge-gray {
            @apply bg-tag-background-gray border-tag-border-gray;

            .status-badge-icon-shape {
                @apply bg-tag-color-gray;
            }
        }

        &.status-badge-purple {
            @apply bg-tag-background-purple border-tag-border-purple;

            .status-badge-icon-shape {
                @apply bg-tag-color-purple;
            }
        }

        &.status-badge-blue {
            @apply bg-tag-background-blue border-tag-border-blue;

            .status-badge-icon-shape {
                @apply bg-tag-color-blue;
            }
        }

        &.status-badge-red {
            @apply bg-tag-background-red border-tag-border-red;

            .status-badge-icon-shape {
                @apply bg-tag-color-red;
            }
        }

        &.status-badge-green {
            @apply bg-tag-background-green border-tag-border-green;

            .status-badge-icon-shape {
                @apply bg-tag-color-green;
            }
        }

    }

 }
