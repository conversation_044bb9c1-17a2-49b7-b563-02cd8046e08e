<script setup lang="ts">

const props = defineProps<{
  colorScheme: 'gray' | 'purple' | 'blue' | 'red' | 'green' // --- Defines the visual color scheme of the status badge.
  label:       string // ----------------------------------------- The text label displayed inside the status badge.
}>()

</script>

<template>

  <div
    class="status-badge"
    :class="`status-badge-${props.colorScheme}`"
  >

    <!-- Icon Section -->
    <div class="status-badge-icon">
      <div class="status-badge-icon-shape" />
    </div>

    <!-- Label Section -->
    <p>{{ label }}</p>

  </div>

</template>
