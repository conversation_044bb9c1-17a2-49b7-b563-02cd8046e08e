<script setup lang="ts">

import { computed } from 'vue'
import { createIcon } from '@lib/store/icon'

import type { IconProps, IconSize } from '@lib/store/icon'

const props = withDefaults( defineProps<IconProps>(), { size: 'm', color: 'currentColor' })

const iconSizes: IconSize = {
  s:  12,
  m:  16,
  l:  24,
  xl: 32
}

const icon      = computed(() => createIcon( props ))
const view      = computed(() => iconSizes[props.size] ?? 0 )
const fill      = computed(() => [ 'loading' ].includes( props.name as string ))
const iconSize  = computed(() => iconSizes[props.size] )

</script>

<template>

  <svg
    v-if="name === 'loading'"
    :width="iconSizes[props.size]"
    :height="iconSizes[props.size]"
    :fill="color ?? 'currentColor'"
    viewBox="0 0 105 105"
  >
    <circle cx="12.5" cy="12.5" r="12.5"><animate attributeName="fill-opacity" begin="0s" dur="1s" values="1;.2;1" calcMode="linear" repeatCount="indefinite" /></circle><circle cx="12.5" cy="52.5" r="12.5" fill-opacity=".5"><animate attributeName="fill-opacity" begin="100ms" dur="1s" values="1;.2;1" calcMode="linear" repeatCount="indefinite" /></circle><circle cx="52.5" cy="12.5" r="12.5"><animate attributeName="fill-opacity" begin="300ms" dur="1s" values="1;.2;1" calcMode="linear" repeatCount="indefinite" /></circle><circle cx="52.5" cy="52.5" r="12.5"><animate attributeName="fill-opacity" begin="600ms" dur="1s" values="1;.2;1" calcMode="linear" repeatCount="indefinite" /></circle><circle cx="92.5" cy="12.5" r="12.5"><animate attributeName="fill-opacity" begin="800ms" dur="1s" values="1;.2;1" calcMode="linear" repeatCount="indefinite" /></circle><circle cx="92.5" cy="52.5" r="12.5"><animate attributeName="fill-opacity" begin="400ms" dur="1s" values="1;.2;1" calcMode="linear" repeatCount="indefinite" /></circle><circle cx="12.5" cy="92.5" r="12.5"><animate attributeName="fill-opacity" begin="700ms" dur="1s" values="1;.2;1" calcMode="linear" repeatCount="indefinite" /></circle><circle cx="52.5" cy="92.5" r="12.5"><animate attributeName="fill-opacity" begin="500ms" dur="1s" values="1;.2;1" calcMode="linear" repeatCount="indefinite" /></circle><circle cx="92.5" cy="92.5" r="12.5"><animate attributeName="fill-opacity" begin="200ms" dur="1s" values="1;.2;1" calcMode="linear" repeatCount="indefinite" /></circle>
  </svg>

  <svg
    v-else
    :width="iconSize"
    :height="iconSize"
    :viewBox="`0 0 ${view} ${view}`"
    :fill="fill ? color ?? 'currentColor' : 'none'"
    v-html="icon"
  />

</template>
