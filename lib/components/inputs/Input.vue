<script setup lang="ts">

import { vMaska } from 'maska/vue'
import { checkValue } from '@lib/scripts/utils'
import { computed, ref, watch } from 'vue'
import { CurrencyDisplay, useCurrencyInput } from 'vue-currency-input'

import Icon from '@lib/components/blocks/Icon.vue'
import Label from '@lib/components/utils/Label.vue'
import ValidationWrapper from '@lib/components/utils/ValidationWrapper.vue'

import type { TextInputProps } from '@lib/types/inputTypes'

defineOptions({
  name: 'TextInput'
})

const props = withDefaults( defineProps<TextInputProps<string | number>>(), {
  size:     'l',
  type:     'text',
  mode:     'primary',
  align:    'left',
  padding:  true,
  required: true,
  currency: 'USD',
})

const emits = defineEmits<{
  'change':            [ value: Event ]
  'debounce':          [ value: string | number ]
  'update:modelValue': [ value: string | number ]
}>()

const inputMask       = computed(() => props.mask ? props.mask : props.type === 'phone' ? '(###) ###-####' : null )
const debouncePending = ref<boolean>( false )
const debounceTimeout = ref<NodeJS.Timeout>( null )

function debounceInput( value: string | number ) {

  clearTimeout( debounceTimeout.value )

  if ( !checkValue( value )) {
    emits( 'debounce', value )
    debouncePending.value = false
    return
  }

  debouncePending.value = true

  debounceTimeout.value = setTimeout(() => {

    emits( 'debounce', value )
    debouncePending.value = false

  }, props.debounce )

}

function mapCustomTypesToHTMLInputType( type: TextInputProps<string | number>['type'] ) {

  const numTypes: ( typeof type )[]   = [ 'number', 'strict-number' ]
  const textTypes: ( typeof type )[] = [ 'currency' ]

  if ( textTypes.includes( type ))
    return 'text'

  if ( numTypes.includes( type ))
    return 'number'

  if ( type === 'phone' )
    return 'tel'

  return type

}

function handleNumberInput( e: KeyboardEvent ) {

  // List of allowed input types

  const types = [ 'number', 'strict-number' ]

  // Return if the input type is not allowed

  if ( !types.includes( props.type ))
    return

  // Allow text selection with metaKey + KeyA

  if ( e.metaKey && e.code === 'KeyA' )
    return

  // Prevent input when shift key is pressed

  if ( e.shiftKey )
    e.preventDefault()

  // List of special keys for strict-number type

  const strictKeys = [ 'Period', 'Comma', 'Minus' ]

  // List of keys allowed for all types

  let allowedKeys = [ 'ControlLeft', 'ControlRight', 'AltLeft', 'AltRight', 'MetaLeft', 'MetaRight', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Backspace', 'Delete', 'Enter', 'Tab' ]

  // Add strict keys to allowedKeys if type is number

  if ( props.type === 'number' )
    allowedKeys = [ ...allowedKeys, ...strictKeys ]

  // Prevent input if not a digit or not an allowed key

  if (( !e.code.match( 'Digit' ) && !e.code.match( 'Numpad' )) && !allowedKeys.includes( e.code ))
    e.preventDefault()

  // Get the input target

  const target = e.target as HTMLInputElement

  // Calculate the value after input

  const nextValue = target.value + e.key

  // Check if a minimum value is set

  const hasMinValue = checkValue( props.min )

  // Check if a maximum value is set

  const hasMaxValue = checkValue( props.max )

  // Handle minimum value restriction

  if ( hasMinValue ) {

    // Prevent negative sign if min value is positive

    if ( props.min >= 0 && e.key === '-' )
      e.preventDefault()

    // Check if the next value is less than the minimum value

    const minValueLength = String( props.min ).length

    if ( nextValue.length === minValueLength && Number.parseInt( nextValue ) < props.min )
      e.preventDefault()

  }

  // Handle maximum value restriction

  if ( hasMaxValue && Number.parseInt( nextValue ) > props.max )
    e.preventDefault()

}

const input = computed<string | number>({

  get: () => {

    const trimmedValue  = typeof props.modelValue === 'string' ? props.modelValue.trim() : props.modelValue
    const returnedValue = checkValue( trimmedValue ) ? trimmedValue : null

    return returnedValue

  },

  set: ( value ) => {

    const trimmedValue  = typeof value === 'string' ? value.trim() : value
    const returnedValue = checkValue( trimmedValue ) ? trimmedValue : null

    emits( 'update:modelValue', returnedValue )

    if ( props.debounce )
      debounceInput( returnedValue )

  }

})

const { inputRef, setValue } = useCurrencyInput({
  currency:                           props.currency,
  useGrouping:                        true,
  accountingSign:                     false,
  currencyDisplay:                    CurrencyDisplay.narrowSymbol,
  autoDecimalDigits:                  true,
  hideCurrencySymbolOnFocus:          false,
  hideGroupingSeparatorOnFocus:       false,
  hideNegligibleDecimalDigitsOnFocus: false
})

watch( input, ( n ) => {
  setValue( n as number )
})

const focus = ref<boolean>( false )

const showPlaceholder = computed(() => ( !!props.label && props.size === 'l' && focus.value ) ? props.placeholder ?? props.label ?? props.name : null )

function trimEmail( e: any ) {

  if ( props.type !== 'email' )
    return

  const trimmedValue = e.target.value.trim() as string
  e.target.value = trimmedValue

}

</script>

<template>

  <ValidationWrapper v-bind="props">

    <div
      class="w-full flex items-center"
      :class="{
        'h-10': size === 'm',
        'h-auto': size === 'auto',
        'h-[3.375rem]': size === 'l',
        'h-[2rem] min-h-[2rem]': size === 'xs',
        'h-[2.25rem] min-h-[2.25rem]': size === 's',
        'input-disabled': disabled,
        'input-mode-naked': mode === 'naked' && !readonly,
        'input-mode-ghost': mode === 'ghost' && !readonly,
        'input-mode-primary': mode === 'primary' && !readonly,
      }"
    >

      <slot name="prefix" />

      <Label
        :size="size"
        :name="name"
        :mode="mode"
        :input="input"
        :label="label"
        :required="required"
        :placeholder="placeholder"
      >

        <input
          v-if="type === 'currency'"
          ref="inputRef"
          type="text"
          class="w-full h-full outline-hidden bg-transparent tabular-nums caret-main"
          :class="{ 'px-4': padding }"
          :style="{ textAlign: align }"
          :disabled="disabled"
          :readonly="readonly"
          :minlength="min"
          :maxlength="max"
          :placeholder="showPlaceholder"
          @blur="() => focus = false"
          @focus="() => focus = true"
          @change.prevent
        >

        <input
          v-else-if="!!mask"
          v-model="input"
          v-maska
          :data-maska="inputMask"
          data-maska-eager
          :min="min"
          :max="max"
          :type="mapCustomTypesToHTMLInputType(type)"
          :style="{ textAlign: align }"
          :disabled="disabled"
          :readonly="readonly"
          :minlength="min"
          :maxlength="max"
          :autocomplete="autocomplete ? '' : 'new-password'"
          :placeholder="showPlaceholder"
          class="w-full h-full outline-hidden bg-transparent caret-main"
          :class="{ 'px-4': padding }"
          @blur="(e) => { focus = false; trimEmail(e) }"
          @focus="() => focus = true"
          @change="(e) => $emit('change', e)"
          @keydown="(e) => handleNumberInput(e)"
        >

        <input
          v-else
          v-model="input"
          :min="min"
          :max="max"
          :type="mapCustomTypesToHTMLInputType(type)"
          :style="{ textAlign: align }"
          :disabled="disabled"
          :readonly="readonly"
          :minlength="min"
          :maxlength="max"
          :autocomplete="autocomplete ? '' : 'new-password'"
          :placeholder="showPlaceholder"
          class="w-full h-full outline-hidden bg-transparent caret-main"
          :class="{ 'px-4': padding }"
          @blur="(e) => { focus = false; trimEmail(e) }"
          @focus="() => focus = true"
          @change="(e) => $emit('change', e)"
          @keydown="(e) => handleNumberInput(e)"
        >

        <template #icon>

          <div v-if="debouncePending" class="h-full px-4 grid place-content-center">
            <Icon name="loading" />
          </div>

        </template>

      </Label>

      <slot name="suffix" />

    </div>

  </ValidationWrapper>

</template>
