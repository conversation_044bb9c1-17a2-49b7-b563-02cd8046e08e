<script setup lang="ts">

import { computed } from 'vue'

import type { CheckboxInputProps } from '@lib/types/inputTypes'

const props = defineProps<CheckboxInputProps<boolean>>()

const emits = defineEmits<{
  'update:modelValue': [ payload: boolean ]
}>()

const input = computed({
  get: () => props.modelValue,
  set: value => emits( 'update:modelValue', value )
})

function toggleOnEnter( e: KeyboardEvent ) {
  if ( e.key === 'Enter' )
    input.value = !input.value
}

</script>

<template>

  <label
    class="group/checkbox w-max max-w-full h-full grid grid-cols-[max-content_1fr] items-center cursor-pointer"
    :class="{
      'pointer-events-none opacity-50': disabled,
      'pointer-events-none opacity-70': readonly,
    }"
  >

    <div class="w-9 h-5 relative bg-core-30 rounded-xs" :class="{ 'bg-main-30': input }">

      <div
        class="w-1/2 h-full absolute transition-[left] after:absolute after:left-1/2 after:-translate-x-1/2 after:top-1/2 after:-translate-y-1/2 after:w-[66.69%] after:h-[60%] after:bg-core-50 after:rounded-[1px]"
        :class="{
          'left-0': !input,
          'left-1/2': input,
          'after:bg-main-70': input,
        }"
      />

    </div>

    <p class="truncate select-none"><slot /></p>

    <input
      v-model="input"
      type="checkbox"
      :disabled="disabled"
      :readonly="readonly"
      class="absolute top-0 left-0 opacity-0 pointer-events-none"
      @keydown="toggleOnEnter"
    >

  </label>

</template>
