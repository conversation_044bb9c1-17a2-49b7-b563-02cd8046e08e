<script setup lang="ts">

import { computed, ref, watch } from 'vue'

import Label from '@lib/components/utils/Label.vue'
import ValidationWrapper from '@lib/components/utils/ValidationWrapper.vue'

import type { TextInputProps } from '@lib/types/inputTypes'

defineOptions({
  name: 'TextInput'
})

const props = withDefaults( defineProps<TextInputProps<string | number>>(), {
  size:     'l',
  type:     'text',
  mode:     'primary',
  required: true
})

const emits = defineEmits<{
  'update:modelValue': [ value: string | number ]
}>()

const input = computed<string | number>({
  get: () => typeof props.modelValue === 'string' ? props.modelValue.trim() : props.modelValue,
  set: value => emits( 'update:modelValue', typeof value === 'string' ? value.trim() : value )
})

const focus     = ref<boolean>( false )
const textBoxEl = ref<HTMLTextAreaElement>( null )

watch( [ textBoxEl, input ], ( n ) => {

  if ( n[0] && n[1] && props.readonly ) {

    setTimeout(() => {
      n[0].style.height = ''
      n[0].style.height = `${n[0].scrollHeight}px`
    }, 100 )

  }

}, { immediate: true })

</script>

<template>

  <ValidationWrapper v-bind="props">

    <div
      class="w-full grid grid-flow-col"
      :class="{
        'input-disabled': disabled,
        'input-mode-naked': mode === 'naked' && !readonly,
        'input-mode-ghost': mode === 'ghost' && !readonly,
        'input-mode-primary': mode === 'primary' && !readonly,
      }"
    >

      <slot name="prefix" />

      <Label
        :size="size"
        :push="false"
        :name="name"
        :mode="mode"
        :input="input"
        :label="label"
        :required="required"
        :placeholder="placeholder"
      >

        <textarea
          ref="textBoxEl"
          v-model="input"
          :min="min"
          :max="max"
          :disabled="disabled"
          :readonly="readonly"
          :minlength="min"
          :maxlength="max"
          rows="1"
          class="w-full p-4 outline-hidden bg-transparent caret-main"
          :class="{ 'mt-6 pt-0': !!label }"
          @blur="() => focus = false"
          @focus="() => focus = true"
        />

      </Label>

      <slot name="suffix" />

    </div>

  </ValidationWrapper>

</template>
