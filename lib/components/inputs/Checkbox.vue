<script setup lang="ts">

import { computed } from 'vue'

import Icon from '@lib/components/blocks/Icon.vue'

import type { CheckboxInputProps } from '@lib/types/inputTypes'

const props = defineProps<CheckboxInputProps<boolean>>()

const emits = defineEmits<{
  'update:modelValue': [ payload: boolean ]
}>()

const input = computed({
  get: () => props.modelValue,
  set: value => emits( 'update:modelValue', value )
})

function toggleOnEnter( e: KeyboardEvent ) {
  if ( e.key === 'Enter' )
    input.value = !input.value
}

</script>

<template>

  <div class="truncate flex items-center">

    <slot name="prefix" />

    <label
      class="group/checkbox w-max max-w-full h-5 grid grid-cols-[max-content_1fr] gap-x-2 items-center cursor-pointer"
      :class="{
        'pointer-events-none opacity-50': disabled,
      }"
    >

      <div v-if="!reverse" class="h-full w-5 grid place-content-center pointer-events-none">

        <div
          class="size-[0.938rem] flex items-center justify-center group-focus-within/checkbox:outline-dashed group-focus-within/checkbox:outline-1 group-focus-within/checkbox:outline-main-60 group-focus-within/checkbox:outline-offset-1"
        >

          <div v-if="!radio">
            <Icon v-if="input" size="m" name="checkbox-checked" />
            <Icon v-else-if="partial" size="m" name="checkbox-partial" />
            <Icon v-else size="m" name="checkbox-unchecked" />
          </div>

          <div v-if="radio">
            <Icon v-if="input" size="m" name="radio-checked" />
            <Icon v-else size="m" name="radio-unchecked" />
          </div>

        </div>

      </div>

      <p class="truncate select-none"><slot>{{ label }}</slot></p>

      <div v-if="reverse" class="h-full pl-2 grid place-content-center pointer-events-none">
        <div
          class="text-main w-5 h-5 flex items-center justify-center border border-core-120 group-focus-within/checkbox:outline-dashed group-focus-within/checkbox:outline-1 group-focus-within/checkbox:outline-main-60 group-focus-within/checkbox:outline-offset-2"
        >

          <div v-if="!radio">
            <Icon v-if="input" size="m" name="checkbox-checked" />
            <Icon v-else-if="partial" size="m" name="checkbox-partial" />
            <Icon v-else size="m" name="checkbox-unchecked" />
          </div>

          <div v-if="radio">
            <Icon v-if="input" size="m" name="radio-checked" />
            <Icon v-else size="m" name="radio-unchecked" />
          </div>

        </div>
      </div>

      <input
        v-model="input"
        type="checkbox"
        :disabled="disabled"
        class="absolute top-0 left-0 opacity-0 pointer-events-none"
        @keydown="toggleOnEnter"
      >

    </label>

    <slot name="suffix" />

  </div>

</template>
