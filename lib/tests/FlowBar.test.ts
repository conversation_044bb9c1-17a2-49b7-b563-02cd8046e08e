import { mount } from '@vue/test-utils'
import { describe, expect, it } from 'vitest'

import Icon from '@lib/components/blocks/Icon.vue'
import FlowBar from '@lib/components/utils/FlowBar.vue'

import type { FlowStep } from '@lib/types/flowBarTypes'

enum Flow {
  STEP1 = 1,
  STEP2 = 2,
  STEP3 = 3,
  STEP4 = 4
}

const steps: FlowStep<Flow>[] = [
  {
    id:            Flow.STEP1,
    isGroup:       true,
    stepName:      'Step 1',
    groupName:     'Group 1',
    inProgress:    false,
    stepComplete:  true,
    groupComplete: true
  },
  {
    id:            Flow.STEP2,
    stepName:      'Step 2',
    groupName:     'Group 1',
    inProgress:    false,
    stepComplete:  true,
    groupComplete: false
  },
  {
    id:            Flow.STEP3,
    isGroup:       true,
    stepName:      'Step 3',
    groupName:     'Group 2',
    inProgress:    true,
    stepComplete:  false,
    groupComplete: false
  },
  {
    id:            Flow.STEP4,
    hidden:        true,
    isGroup:       true,
    stepName:      'Step 4',
    groupName:     'Group 3',
    inProgress:    false,
    stepComplete:  false,
    groupComplete: false
  }
]

describe( 'components::FlowBar', () => {

  const wrapper = mount( FlowBar, { props: { steps } })

  it( 'renders the correct number of steps.', () => {
    expect( wrapper.vm.flowSteps.length ).toBe( steps.filter( s => !s.hidden ).length )
    expect( wrapper.findAll( '[data-type="desktop"]' )).toHaveLength( wrapper.vm.flowSteps.filter( s => s.isGroup ).length )
  })

  it( 'sets the correct active step.', () => {
    expect( wrapper.vm.index ).toBe( 2 )
    expect( wrapper.vm.currStep.id ).toBe( Flow.STEP3 )
  })

  it ( 'sets the next step correct.', () => {
    expect( wrapper.vm.nextStep ).toBe( undefined )
  })

  it ( 'sets the previous step correct.', () => {
    expect( wrapper.vm.prevStep.id ).toBe( Flow.STEP2 )
  })

  it ( 'sets the correct state.', () => {

    let stepElement = wrapper.find( `[data-step="${Flow.STEP1}"]` )
    expect( stepElement.text()).toBe( 'Group 1' )

    let icon = stepElement.findComponent( Icon )

    expect( icon.props().name ).toBe( 'checkmark-outline' )

    stepElement = wrapper.find( `[data-step="${Flow.STEP2}"]` )
    expect( stepElement.exists()).toBe( false )

    stepElement = wrapper.find( `[data-step="${Flow.STEP3}"]` )
    expect( stepElement.text()).toBe( 'Group 2' )

    icon = stepElement.findComponent( Icon )
    expect( icon.props().name ).toBe( 'incomplete-normal' )

    stepElement = wrapper.find( `[data-step="${Flow.STEP4}"]` )
    expect( stepElement.exists()).toBe( false )

  })

  it ( 'changes the active step when clicked on a step button.', async () => {

    const stepElement = wrapper.find( `[data-step="${Flow.STEP1}"]` )
    await stepElement.trigger( 'click' )

    const emit: FlowStep<Flow>[][] = wrapper.emitted( 'setActiveStep' )

    expect( wrapper.emitted( 'setActiveStep' )).toBeTruthy()
    expect( emit[0][0].id ).toBe( Flow.STEP1 )

  })

  it ( 'renders the correct step name on mobile.', () => {

    expect( wrapper.vm.prevStep.stepName ).toBe( 'Step 2' )
    expect( wrapper.vm.currStep.stepName ).toBe( 'Step 3' )
    expect( wrapper.vm.nextStep ).toBeUndefined()

  })

})
