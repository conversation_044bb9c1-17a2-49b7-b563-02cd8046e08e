/**
 * Sets up the droplist element positioning based on the parent element and screen dimensions.
 */
export function setupDroplist( element: HTMLDivElement, parentElement: HTMLDivElement | HTMLButtonElement, keepContentWidth: boolean = false ) {

  // Get the bounding rectangle of the element

  const dlRect: DOMRect = element.getBoundingClientRect()

  // Get the screen dimensions

  const screenW: number = window.innerWidth
  const screenH: number = window.innerHeight

  // Get the bounding rectangle of the parent element

  const { bottom, left, width, right } = parentElement.getBoundingClientRect()

  // If the width of the droplist element is bigger than the parent element width,
  // make droplist element width same as the parent. Else keep the min width of the droplist.

  if ( dlRect.width <= width ) {
    if ( !keepContentWidth )
      element.style.width = `${width}px`
  }

  // Set the top position of the droplist element

  element.style.top = `${bottom + 1}px`

  // Calculate the space available below the droplist element

  const spaceBot = screenH - bottom

  // Calculate the remainder space on the sides of the droplist element

  const remainder = ( element.clientWidth - width ) / 2

  // Check if there is enough space on the left side, right side, and bottom

  const hasSpaceLeft = left > remainder
  const hasSpaceRight = ( screenW - right ) > remainder
  const hasSpaceBot = spaceBot > element.clientHeight

  // If there is enough space below the droplist element

  if ( hasSpaceBot ) {

    // Adjust the left position of the droplist element

    if ( hasSpaceLeft && hasSpaceRight )
      element.style.left = `${left - remainder}px`

    else if ( hasSpaceLeft && !hasSpaceRight )
      element.style.left = `${left - ( element.clientWidth - width )}px`

    else if ( !hasSpaceLeft && hasSpaceRight )
      element.style.left = `${left}px`

  }

  else {

    // If there is not enough space below the droplist element

    // Check if there is enough space on both sides

    if ( left < element.clientWidth && ( screenW - right ) < element.clientWidth ) {

      // Adjust the top and left position of the droplist element

      element.style.top = `${bottom + ( spaceBot - element.clientHeight ) - 10}px`

      if ( hasSpaceLeft && hasSpaceRight )
        element.style.left = `${left - remainder}px`

      if ( hasSpaceLeft && !hasSpaceRight )
        element.style.left = `${left - ( element.clientWidth - width )}px`

      if ( !hasSpaceLeft && hasSpaceRight )
        element.style.left = `${left}px`

    }

    // If there is enough space on the left side

    if ( left > element.clientWidth ) {

      // Adjust the left and top position of the droplist element

      element.style.left = `${left - element.clientWidth}px`
      element.style.top = `${bottom + ( spaceBot - element.clientHeight ) - 10}px`

    }

    // If there is enough space on the right side

    if ( left < element.clientWidth && ( screenW - right ) > element.clientWidth ) {

      // Adjust the left and top position of the droplist element

      element.style.left = `${left + width}px`
      element.style.top = `${bottom + ( spaceBot - element.clientHeight ) - 10}px`

    }
  }
}

/**
 * Focuses on the first option when the ArrowDown key is pressed, or when Tab is pressed and the droplist is open.
 *
 * @param {KeyboardEvent} event - The keyboard event object.
 * @param {HTMLDivElement | HTMLButtonElement} parentElement - The parent element.
 * @param {boolean} isDroplistOpen - Indicates if the droplist is open.
 * @returns { open: boolean } - If the ArrowDown key is pressed and the droplist is not open, returns { open: true }.
 */
export function focusFirstOptionOnKeyDown( event: KeyboardEvent, parentElement: HTMLDivElement | HTMLButtonElement, isDroplistOpen: boolean ): { open: boolean } {

  if ( event.key === 'ArrowDown' ) {

    event.preventDefault()

    const focus = document.activeElement === parentElement

    if ( !focus )
      return { open: false }

    if ( isDroplistOpen ) {

      const firstOption = document.getElementsByName( 'search-option' )[0] ?? document.getElementsByName( 'option-0' )[0]

      if ( firstOption )
        firstOption.focus()

      return { open: false }

    }

    return { open: true }

  }

  if ( event.key === 'Tab' && isDroplistOpen ) {

    event.preventDefault()

    const firstOption = document.getElementsByName( 'search-option' )[0] ?? document.getElementsByName( 'option-0' )[0]

    if ( firstOption )
      firstOption.focus()

    return { open: false }

  }

  return { open: false }

}

/**
 * A function to close the list on the escape key press.
 *
 * @param {KeyboardEvent} event - The keyboard event.
 * @param {HTMLDivElement | HTMLButtonElement} parentElement - The parent element.
 * @param {boolean} isDroplistOpen - A boolean indicating if the droplist is open.
 * @return {boolean} A boolean indicating if the list was closed.
 */
export function closeListOnEscapeKey( event: KeyboardEvent, parentElement: HTMLDivElement | HTMLButtonElement, isDroplistOpen: boolean ) {

  if ( !isDroplistOpen )
    return false

  if ( event.key === 'Escape' ) {
    parentElement.focus()
    return true
  }

  return false

}
