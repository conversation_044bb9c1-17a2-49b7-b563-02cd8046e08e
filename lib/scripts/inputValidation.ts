import { checkValue } from '@lib/scripts/utils'

import type { InputType, InputValidationOptions } from '@lib/types/inputTypes'

function handleErrors( entries: string | string[] ): string | null {

  if ( Array.isArray( entries )) {

    if ( entries.some( e => e ))
      return entries.filter( e => e !== null && e !== undefined ).join( ' ' )

    else
      return null

  }

  else if ( entries ) {
    return entries
  }

  else { return null }

}

/**
 * Checks for valid property in entry properties, returns true if all are true.
 * @param entry The Validatable Object
 */

export function validateModel<T>( entry: T ): boolean {

  if ( entry ) {

    const list = []
    let valid = false

    const entries = Object.keys( entry )

    entries.forEach(( key ) => {

      if (( entry[key] !== undefined && entry[key] !== null ) && entry[key].hasOwnProperty( 'valid' ))
        list.push( entry[key].valid )

    })

    valid = list.every( e => e === true )

    return valid

  }

  else { return false }

}

function validateTextLength( input: string, name: string, min: number, max: number ) {

  if ( min ) {
    if ( input?.length < min ) {
      return {
        valid: false,
        error: `${name} must be ${min} or more characters long.`
      }
    }
  }

  if ( max ) {
    if ( input?.length > max ) {
      return {
        valid: false,
        error: `${name} must be ${max} or less characters long.`
      }
    }
  }

  return {
    valid: true,
    error: null
  }

}

function validateNumberRange( input: number, name: string, min: number, max: number ) {

  if ( min ) {
    if ( input < min ) {
      return {
        valid: false,
        error: `${name} must be greater than or equal to ${min}.`
      }
    }
  }

  if ( max ) {
    if ( input > max ) {
      return {
        valid: false,
        error: `${name} must be less than or equal to ${max}.`
      }
    }
  }

  return {
    valid: true,
    error: null
  }

}

function validateArrayLength( input: any[], name: string, min: number, max: number ) {

  if ( min ) {
    if ( input.length < min ) {
      return {
        valid: false,
        error: `${name} must have ${min} or more items.`
      }
    }
  }

  if ( max ) {
    if ( input.length > max ) {
      return {
        valid: false,
        error: `${name} can't have more than ${max} items.`
      }
    }
  }

  return {
    valid: true,
    error: null
  }

}

function validateRegex( input: string, name: string, regex: RegExp ) {

  if ( input.match( regex )) {
    return {
      valid: true,
      error: null
    }
  }

  else {
    return {
      valid: false,
      error: `${name} is invalid.`
    }
  }

}

function matchInput( input: string | number, name: string, match: string ) {

  if ( input === match ) {
    return {
      valid: true,
      error: null
    }
  }

  else {
    return {
      valid: false,
      error: `${name} doesn\'t match.`
    }
  }

}

export function validateInput( input: any, options: InputValidationOptions ): { error: string, valid: boolean } {

  const name = checkValue( options.name ) ? options.name : 'Input'

  // If Input is of type boolean, return valid result.

  if ( typeof input === 'boolean' )
    return { error: null, valid: true }

  // If Input is not required, return valid result.

  if ( !options.required )
    return { error: null, valid: true }

  // If there is no input, return required error.

  const hasInput = typeof input === 'string' ? !!input : input !== null && input !== undefined

  if ( !hasInput )
    return { error: `${name} is required.`, valid: false }

  // If the input is string, validate the input based on the validation props.

  const stringInputsList: InputType[] = [ 'text', 'email', 'password', 'phone' ]

  if ( stringInputsList.includes( options.type )) {

    let emailResult: { valid: boolean, error: string } = { valid: true, error: null }
    let regexResult: { valid: boolean, error: string } = { valid: true, error: null }
    let matchResult: { valid: boolean, error: string } = { valid: true, error: null }

    const lengthResult = validateTextLength( input, name, options.min, options.max )

    if ( options.type === 'email' )
      emailResult = validateRegex( input, name, /^(([^<>()[\].,;:\s@"]+(\.[^<>()[\].,;:\s@"]+)*)|(".+"))@(([^<>()[\].,;:\s@"]+\.)+[^<>()[\].,;:\s@"]{2,})$/ )

    if ( options.regex )
      regexResult = validateRegex( input, name, options.regex )

    if ( options.match )
      matchResult = matchInput( input, name, options.match )

    return {
      error: handleErrors( [ lengthResult.error, emailResult.error, regexResult.error, matchResult.error ] ),
      valid: emailResult.valid && lengthResult.valid && regexResult.valid && matchResult.valid
    }

  }

  // If the input is number, validate the input based on the validation props.

  if ( options.type === 'number' || typeof input === 'number' )
    return validateNumberRange( input, name, options.min, options.max )

  // If the input is array, validate the array based on the validation props.

  if ( Array.isArray( input )) {

    if ( input.length > 0 )
      return validateArrayLength( input, name, options.min, options.max )

    else return { error: `${name} is required.`, valid: false }

  }

  return {
    error: null,
    valid: true
  }

}
