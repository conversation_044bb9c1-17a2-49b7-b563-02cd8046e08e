import { pageSize } from '@lib/store/table'
import { onMounted, onUnmounted, ref, shallowRef, unref, watch } from 'vue'
import { checkValue, compareObjects, generateUniqueId, removeEmptyKeysFromObject, sanitizeQueryParams } from '@lib/scripts/utils'

import type { Ref, WatchSource } from 'vue'
import type { LocationQueryRaw, Router } from 'vue-router'

type UpdatePageType = 'normal' | 'silent'

interface UpdatePageCallback<Params, PayloadType> {
  ignored:  boolean
  callback: ( p: Params ) => Promise<void | Payload<PayloadType>>
}

interface PendingState {
  id:          string
  silent:      Ref<boolean>
  pending:     Ref<boolean>
  lpInstance?: NodeJS.Timeout
}

interface MapCallBackToListPayload<Params, PayloadType = any> {
  cbList:          UpdatePageCallback<Params, PayloadType>[]
  hasIgnoreParams: boolean
}

interface ViewSetupLongPoolingOptions {
  silent:   boolean
  interval: number
}

type ViewSetupLongPooling = boolean | ViewSetupLongPoolingOptions

interface ViewSetupCallbackModel<Params, PayloadType> {
  callback:      UpdatePageCallback<Params, PayloadType>['callback']
  ignoreType?:   'pick' | 'omit'
  ignoreParams?: ( keyof Params )[] | 'all'
}

type ViewSetupCallback<Params, PayloadType> =
  | UpdatePageCallback<Params, PayloadType>['callback']
  | ViewSetupCallbackModel<Params, PayloadType>
  | ViewSetupCallbackModel<Params, PayloadType>[]

interface ViewSetupPayload {
  silent:          Ref<boolean>
  pending:         Ref<boolean>
  updateView:      ( silent?: UpdatePageType ) => Promise<{ error: boolean }>
  blockPageUpdate: ( block?: boolean ) => void
}

/**
 * List of active pending states for the view setup.
 */
const pendingList = shallowRef<PendingState[]>( [] )

/**
 * Updates the page with the provided callback functions.
 *
 * @param router ----------- Instance of the vue router.
 * @param cbList ----------- Callback functions array to manage the state.
 * @param viewParams ------- Current page query params as reactive object.
 * @param updatePageId ----- The unique id to identify the pending state.
 * @param silentRequest ---- If set to "silent", the pending state will not be triggered. default is "normal".
 * @param longPooling ------ The long pooling options.
 * @returns ---------------- the view params.
 */
async function updatePage<Params extends BaseParams, PayloadType>(

  router:         Router,
  cbList:         UpdatePageCallback<Params, PayloadType>[],
  viewParams:     Params,
  updatePageId:   string,
  silentRequest:  UpdatePageType = 'normal',
  longPooling?:   ViewSetupLongPooling

): Promise<{ error: boolean }> {

  // Handle page size

  const urlPageSize = router.currentRoute.value?.query?.pageSize as string // -------- Check if there is a pageSize query param in the URL.

  if ( urlPageSize ) // -------------------------------------------------------------- If there is a pageSize query param in the URL,
    pageSize.value = Number.parseInt( urlPageSize ) // ------------------------------- Set the pageSize value to the parsed value of the query param.

  const updatedViewParams = { ...viewParams, pageSize: pageSize.value } // ----------- Create a copy of the viewParams with the updated page size.

  // Update Page [ Globals ]

  let error: boolean      = false // ------------------------------------------------- Set the error state to false by default.
  let LPSilent: boolean   = true // -------------------------------------------------- Set the long pooling silent state to true by default.
  let LPEnable: boolean   = false // ------------------------------------------------- Set the long pooling state to false by default.
  let LPTimeout: number   = 60000 // ------------------------------------------------- Set the long pooling interval to 1 minute by default.

  if ( longPooling ) {

    LPEnable = true

    if ( typeof longPooling === 'object' ) { // -------------------------------------- Check if long pooling options are provided.

      LPSilent = longPooling.silent // ----------------------------------------------- Set the long pooling silent state to the provided value.
      LPTimeout = longPooling.interval // -------------------------------------------- Set the long pooling interval to the provided value.

    }

  }

  // Update the page

  const pendingState = pendingList.value.find( p => p.id === updatePageId ) // ------- Find the pending state object by the updateId.

  pendingState.silent.value = silentRequest === 'silent' // -------------------------- Set the silent state to true if the request is silent.
  pendingState.pending.value = silentRequest === 'normal' // ------------------------- Set the pending state to true if the request is normal.

  const data = await Promise.all( // ------------------------------------------------- Call all of the non ignored callback functions with the merged params object.

    cbList
      .filter( cb => !cb.ignored ) // ------------------------------------------------ Filter out the ignored callback functions.
      .map( cb => cb.callback( updatedViewParams )) // ------------------------------- Map the callback functions into requests list.

  ) as Payload<PayloadType>[]

  error = data.some( d => !!d?.error ) // -------------------------------------------- Set the error state to true if any of the requests have an error.

  pendingState.silent.value = false // ----------------------------------------------- Reset the silent state to false.
  pendingState.pending.value = false // ---------------------------------------------- Reset the pending state to false.

  if ( data.some( d => d?.status === 404 )) // --------------------------------------- If any of the requests have a 404 status, redirect to the 404 page.
    router.push( '/404' )

  // LONG POOLING
  // If long pooling is enabled, updatePage will call all the callbacks
  // on the interval of 1 minute or the provided interval by the user.
  // Whenever the original request is called, the long pooling will be reset.

  if ( LPEnable ) { // --------------------------------------------------------------- If long pooling is enabled, set the interval to update the page.

    clearTimeout( pendingState.lpInstance ) // --------------------------------------- Clear the previous interval if it exists.

    pendingState.lpInstance = setInterval( async () => {

      pendingState.silent.value = LPSilent // ---------------------------------------- Set the lp silent state to true if the request is silent.
      pendingState.pending.value = !LPSilent // -------------------------------------- Set the lp pending state to true if the request is normal.

      const data = await Promise.all( // --------------------------------------------- Call all of the lp callback functions with the merged params object.
        cbList.map( cb => cb.callback( updatedViewParams ))
      ) as Payload<PayloadType>[]

      error = data.some( d => !!d?.error )

      pendingState.silent.value = false // ------------------------------------------- Reset the lp silent state to false.
      pendingState.pending.value = false // ------------------------------------------ Reset the lp pending state to false.

    }, LPTimeout )

  }

  return { error } // ---------------------------------------------------------------- Return error if there is any.

}

/**
 * Filters the callback functions by the ignored params.
 *
 * @param callBack - Callback object or array of callback objects with possible ignore params to manage the state.
 * @param nParams - New params object.
 * @param oParams - Old params object.
 * @returns - filtered array of callback functions.
 */
function filterCallbackByIgnoredParams<Params, PayloadType = any>(

  callBack: ViewSetupCallback<Params, PayloadType>,
  nParams:  Params,
  oParams:  Params

): UpdatePageCallback<Params, PayloadType>[] {

  const list: UpdatePageCallback<Params, PayloadType>[] = []

  // If the callback is an array of callback objects,
  // check if the params are changed and push the callback to the list.

  if ( Array.isArray( callBack )) {

    callBack.forEach(( cb ) => {

      let ignored = false
      const hasIgnoreList = cb?.ignoreParams && cb?.ignoreParams?.length > 0

      if ( hasIgnoreList ) {

        const ignoreType = cb?.ignoreType ?? 'omit'

        for ( const param in nParams ) {

          const validChange = checkValue( nParams[param], { checkBoolean: false }) || checkValue( oParams[param], { checkBoolean: false })

          if ( validChange && ( nParams[param] !== oParams[param] )) {

            ignored
              = cb.ignoreParams === 'all'
                ? true
                : ignoreType === 'pick'
                  ? !cb.ignoreParams.includes( param )
                  : cb.ignoreParams.includes( param )

            if ( ignored )
              break

          }

        }

      }

      list.push({ callback: cb.callback, ignored })

    })
  }

  // If the callback is a single callback object,
  // check if the params are changed and push the callback to the list.

  else if ( typeof callBack === 'object' && callBack.hasOwnProperty( 'callback' )) {

    let ignored = false
    const hasIgnoreList = callBack?.ignoreParams && callBack?.ignoreParams?.length > 0

    if ( hasIgnoreList ) {

      const ignoreType = callBack?.ignoreType ?? 'omit'

      for ( const param in nParams ) {

        const validChange = checkValue( nParams[param], { checkBoolean: false }) || checkValue( oParams[param], { checkBoolean: false })

        if ( validChange && ( nParams[param] !== oParams[param] )) {

          ignored
            = callBack.ignoreParams === 'all'
              ? true
              : ignoreType === 'pick'
                ? !callBack.ignoreParams.includes( param )
                : callBack.ignoreParams.includes( param )

          if ( ignored )
            break

        }

      }

    }

    list.push({ callback: callBack.callback, ignored })
  }

  // If the callback is a single function, push it to the list.

  else if ( typeof callBack === 'function' ) {
    list.push({ callback: callBack, ignored: false })
  }

  return list

}

/**
 * Maps the callback functions to a list of functions.
 *
 * @param callBack ---- Callback object or array of callback objects with possible ignore params to manage the state.
 * @returns ----------- List of callback functions and ignore params check.
 */
function mapCallbackToList<Params, PayloadType = any>( callBack: ViewSetupCallback<Params, PayloadType> ): MapCallBackToListPayload<Params> {

  let cbList: UpdatePageCallback<Params, PayloadType>[] = []
  let hasIgnoreParams: boolean = false

  if ( typeof callBack === 'function' ) { // ----------------------------------------- Check if the callback is a single function

    cbList = [ { callback: callBack, ignored: false } ]
    hasIgnoreParams = false

  }

  else if ( Array.isArray( callBack )) { // ------------------------------------------ Check if the callback is an array of callback objects

    hasIgnoreParams = callBack.some( cb => cb.ignoreParams )
    cbList = callBack.map( cb => ({ callback: cb.callback, ignored: false }))

  }

  else if ( typeof callBack === 'object' ) { // -------------------------------------- The callback is an object

    hasIgnoreParams = callBack?.ignoreParams && callBack?.ignoreParams?.length > 0
    cbList = [ { callback: callBack.callback, ignored: false } ]

  }

  return {
    cbList, // ----------------------------------------------------------------------- List of callback functions.
    hasIgnoreParams // --------------------------------------------------------------- True if there are ignore params in some of the callbacks.
  }

}

/**
 * Blocks the page update in view setup.
 * The view setup will reset this to false if set to true,
 * so the page can be updated again on the next change.
 */
const blockUpdate = ref<boolean>( false )

/**
 * Blocks the page update in view setup.
 * param block - True by default, it can be conditionally set from outside.
 */
export function blockPageUpdate( block: boolean = true ): void {
  blockUpdate.value = block
}

/**
 * Automates the whole view setup, auto route and params updates
 *
 * @param routeName ------ The name of the view route as defined in the vue router. If there are no query params in the view, this can be null.
 * @param queryParams ---- Current page query params as reactive object.
 * @param router --------- Instance of the vue router.
 * @param callBack ------- Callback function or Callback objects array to manage the state.
 * @param watchSource ---- Watched source to trigger the view setup on change.
 * @param longPooling ---- boolean or long pooling options object.
 * @returns -------------- updatePage and blockUpdate functions.
 */
export function viewSetup<Params extends BaseParams, WatchedSource, Callback extends ViewSetupCallback<Params, any>>(

  routeName:    string,
  queryParams:  Params,
  router:       Router,
  callBack:     Callback,
  watchSource?: WatchSource<WatchedSource>,
  longPooling?: ViewSetupLongPooling,
  stateOnly?:   boolean

): ViewSetupPayload {

  queryParams = queryParams ?? {} as Params // --------------------------------------- Set the query params to an empty object if they aren't defined.

  // Map the callback to the callback list, depending on the type of the
  // provided callBack param and check early if there are any ignore queryParams.

  let { cbList, hasIgnoreParams } = mapCallbackToList<Params, Callback extends ViewSetupCallback<Params, infer PayloadType> ? PayloadType : any>( callBack )

  // VIEW SETUP :: [ globals ]

  const updateId: string          = generateUniqueId() // ---------------------------- Unique id for the view update.
  let blockParamsWatcher: boolean = false // ----------------------------------------- Block the params watcher if the state is updated from the route.

  pendingList.value.push({ // -------------------------------------------------------- Create a pending object for this view setup instance.
    id:      updateId,
    silent:  ref( false ),
    pending: ref( false )
  })

  // WATCH :: [ queryParams ]
  // push queryParams to current route if some of the queryParams are changed.

  watch(() => ({ ...queryParams }), ( newParams, oldParams ) => {

    if ( blockParamsWatcher ) { // --------------------------------------------------- If the params watcher is blocked
      blockParamsWatcher = false // -------------------------------------------------- Reset the blockParamsWatcher to false.
      return
    }

    // If there are ignored queryParams in the callback,
    // check if the queryParams are changed and push the callback to the list.

    if ( hasIgnoreParams && !compareObjects( newParams, oldParams )) {

      cbList = [] // ----------------------------------------------------------------- Reset the callback list
      cbList = filterCallbackByIgnoredParams( callBack, newParams, oldParams )

    }

    const route = unref( router.currentRoute ) // ------------------------------------ Get the plain route value without the reactivity.

    // Check if the page param is changed and update the view with the new page,
    // If other queryParams are changed then reset page to 1.

    let page: number = 1

    if ( newParams?.page && ( newParams?.page !== oldParams?.page ))
      page = newParams?.page

    // Remove queryParams that have no value.

    const viewParams = removeEmptyKeysFromObject<Params>({
      ...sanitizeQueryParams({ ...route.query }),
      ...newParams,
      page
    })

    if ( stateOnly ) // -------------------------------------------------------------- If stateOnly is true, update the queryParams without changing the route.
      updatePage( router, cbList, viewParams, updateId, 'normal', longPooling )

    else // -------------------------------------------------------------------------- If stateOnly is false, push the queryParams to the route.
      router.push({ path: route.path, query: viewParams as LocationQueryRaw })

  })

  // WATCH :: [ route ]
  // Update the view only on route change.
  // Skip update if user navigates to another route.

  watch(() => router.currentRoute.value, async ( newRoute, oldRoute ) => {

    const isOnRoute: boolean = newRoute.name === routeName

    if ( isOnRoute ) { // ------------------------------------------------------------ View Setup will update the page only if is on the specified route.

      if ( blockUpdate.value ) { // -------------------------------------------------- If blockUpdate is true skip the update
        blockUpdate.value = false
        return
      }

      const cleanParams   = removeEmptyKeysFromObject({ ...queryParams }) // --------- Remove empty keys from the queryParams.
      const currURLParams = sanitizeQueryParams<Params>({ ...newRoute.query }) // ---- Get the current URL params.
      const prevURLParams = sanitizeQueryParams<Params>({ ...oldRoute.query }) // ---- Get the previous URL params.

      // Check if the change is coming from the route and not from the state params.
      // If the change is coming from the route, update the state params to reflect the url params.

      const isRouteChange = !compareObjects( currURLParams, cleanParams )

      // Create a copy of the current and previous URL params without the page param.
      // This is necessary to check if any of the other params are changed.

      const noPageCurrURLParams = { ...currURLParams }
      delete noPageCurrURLParams.page

      const noPagePrevURLParams = { ...prevURLParams }
      delete noPagePrevURLParams.page

      const hasCurrURLParams = Object.keys( currURLParams ).length > 0
      const hasPrevURLParams = Object.keys( prevURLParams ).length > 0

      // Check if any of the parameters except the page are changed,
      // if so, reset the page to 1 and update the view.

      const shouldResetPage = hasCurrURLParams && hasPrevURLParams && !compareObjects( noPageCurrURLParams, noPagePrevURLParams )

      if ( isRouteChange ) {

        for ( const key in queryParams ) {

          // Check if the current URL params have the key and the value is not null or undefined,
          // if so, update the state queryParams with the new value.

          if ( checkValue( currURLParams[key], { checkBoolean: false }))
            queryParams[key] = currURLParams[key]

          // If the key is not in the current URL params ( page and pageSize excluded ), set the state queryParams to null.
          // This is necessary to remove the queryParams that are not in the URL.

          if ( ![ 'page', 'pageSize' ].includes( key ) && !currURLParams.hasOwnProperty( key ))
            queryParams[key] = null

        }

        // If the pageSize is not in the current URL params,
        // set the state pageSize to the default value.

        if ( currURLParams?.pageSize ) {
          pageSize.value = currURLParams.pageSize
          queryParams.pageSize = currURLParams.pageSize
        }

        // If the page is not in the current URL params, set the state page to 1,
        // otherwise set the state page to the current URL page.

        queryParams.page = currURLParams.page ?? 1

        if ( shouldResetPage ) { // -------------------------------------------------- If any of the other params are changed,

          queryParams.page = 1 // ---------------------------------------------------- Reset the page to 1.

          history.replaceState( // --------------------------------------------------- Replace the current URL state with the new page.
            history.state,
            null,
            history.state.current.replace(
              `page=${currURLParams.page}`,
              `page=1`
            )
          )

        }

        // If there is a route change and the previous view was on another route,
        // update all the callbacks in the list.

        if ( newRoute.name !== oldRoute.name )
          cbList = mapCallbackToList( callBack ).cbList

        blockParamsWatcher = true // ------------------------------------------------- Block the params watcher to prevent the double route update.

      }

      const viewParams = removeEmptyKeysFromObject({ ...queryParams }) // ------------ Remove empty keys from the updated query params.

      await updatePage( // ----------------------------------------------------------- Update the page with the new query queryParams.
        router,
        cbList,
        viewParams,
        updateId,
        'normal',
        longPooling
      )

      blockParamsWatcher = false // -------------------------------------------------- Reset the blockParamsWatcher to false.

    }

  })

  // WATCH :: [ watchSource ]
  // If there is a watchSource, update the page on the source change.

  if ( watchSource ) {

    watch( watchSource, ( newSourceValue, oldSourceValue ) => {

      // Update the page only if the source has value and the value is changed.

      if ( !!newSourceValue && ( newSourceValue !== oldSourceValue ))
        updatePage( router, cbList, queryParams, updateId, 'normal', longPooling )

    }, { immediate: true })

  }

  // MOUNTED
  // Update the pageSize value if there is a query param.
  // Update the view on mount if there is no watchSource.
  // If there is a watchSource, the view will be initially updated when its value change.

  onMounted(() => {

    if ( !watchSource )
      updatePage( router, cbList, queryParams, updateId, 'normal', longPooling )

  })

  // UNMOUNTED :: [ cleanup ]
  // Clear the long pooling timeout instance if there is any on unmount.
  // Remove the view setup instance from the pending list on unmount.
  // This is necessary to prevent memory leaks and to keep the pending list clean.

  onUnmounted(() => {

    clearTimeout( pendingList.value.find( p => p.id === updateId )?.lpInstance )
    pendingList.value = pendingList.value.filter( p => p.id !== updateId )

  })

  // UpdateView function to manually update the page.

  const updateView = async ( silent?: UpdatePageType ) => {

    return await updatePage(
      router,
      cbList,
      queryParams,
      updateId,
      silent,
      longPooling
    )

  }

  const pendingState = pendingList.value.find( p => p.id === updateId ) // ----------- Get the current pending state.
  const { pending, silent } = pendingState // ---------------------------------------- Get the pending and silent refs.

  return {
    silent, // ----------------------------------------------------------------------- Return true if there is an silent update.
    pending, // ---------------------------------------------------------------------- Return current pending state.
    updateView, // ------------------------------------------------------------------- Update the page manually.
    blockPageUpdate // --------------------------------------------------------------- Blocks the next page update.
  }

}
