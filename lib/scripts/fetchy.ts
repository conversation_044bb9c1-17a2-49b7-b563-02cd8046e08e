import Axios from 'axios'

import { confirmOptions } from '@lib/store/confirm'
import { setAlertOptions } from '@lib/store/snackbar'
import { loginRequest, msalInstance } from '@lib/auth/scripts/authConfig'
import { BrowserAuthError, InteractionRequiredAuthError } from '@azure/msal-browser'
import { createPagination, generateRandomNumber, generateUniqueId } from '@lib/scripts/utils'

import type { AxiosInstance, AxiosRequestConfig } from 'axios'

const axios: AxiosInstance = Axios.create({
  baseURL:        import.meta.env.VITE_BASE_URL,
  validateStatus: () => true
})

interface PossibleResponseErrorType {
  message?:      string
  details?:      string
  errorMessage?: string
  errorDetails?: string
}

interface CustomAxiosRequestConfig extends AxiosRequestConfig {
  socket?:           boolean
  showError?:        boolean
  useHeaders?:       boolean
  generateMockData?: boolean | MockGeneratorOptions
}

interface MockGeneratorOptions {
  error?:      'never' | 'always' | 'random'
  model?:      Record<string, any>
  total?:      number
  params?:     BaseParams
  pagination?: boolean
}

export async function generateMockData( options?: MockGeneratorOptions ): Promise<Payload<any>> {

  // Set the default values for the mock generator options

  const defaultOptions: MockGeneratorOptions = {
    error: 'never',
    total: 200,
    model: {
      id:          generateUniqueId(),
      name:        'Record 1',
      isPublished: true,
      description: 'Record 1 description',
      dateCreated: new Date().toISOString()
    },
    params: {
      page:     1,
      pageSize: 25
    }
  }

  const error           = options?.error ?? defaultOptions.error
  const total           = options?.total ?? defaultOptions.total
  const model           = options?.model ?? defaultOptions.model
  const params          = options?.params ?? defaultOptions.params
  const pagination      = options?.pagination ?? false
  const requestDuration = generateRandomNumber( 500, 2000 )

  const errorMockResponse: Payload<any> = {
    payload: null,
    status:  500,
    error:   [
      {
        message: 'Oops! Something went wrong while generating mock data.',
        details: 'This Error is generated by the mockDataGenerator and is used for testing purpose.',
      }
    ]
  }

  if ( error === 'always' ) {

    errorMockResponse.error.forEach(( err ) => {
      setAlertOptions({
        message:  err.message,
        details:  err.details,
        severity: 'error',
      })
    })

    return new Promise( resolve => setTimeout(() => resolve( errorMockResponse ), requestDuration ))

  }

  const generatedData = []

  for ( let i = 0; i < total; i++ ) {

    Object.keys( model ).forEach(( key ) => {

      const isKeyId = key.toLowerCase().match( 'id' )
      const isKeyDate = key.toLowerCase().match( 'date' )
      const isKeyCost = key.toLowerCase().match( 'cost' )
      const isKeyAmount = key.toLowerCase().match( 'amount' )

      if ( !isKeyId && !isKeyDate && !isKeyCost && !isKeyAmount && typeof model[key] === 'string' ) {

        const findSpacedNumber = model[key].split( ' ' )

        findSpacedNumber.forEach(( entry, index ) => {

          if ( /^\d+$/.test( entry ))
            findSpacedNumber[index] = String( i + 1 )

        })

        model[key] = findSpacedNumber.join( ' ' )

        const findLineSeparatedNumber = model[key].split( '-' )

        findLineSeparatedNumber.forEach(( entry, index ) => {

          if ( /^\d+$/.test( entry ))
            findLineSeparatedNumber[index] = i + 1

        })

        model[key] = findLineSeparatedNumber.join( '-' )

        const findUnderlineSeparatedNumber = model[key].split( '_' )

        findUnderlineSeparatedNumber.forEach(( entry, index ) => {

          if ( /^\d+$/.test( entry ))
            findUnderlineSeparatedNumber[index] = i + 1

        })

        model[key] = findUnderlineSeparatedNumber.join( '_' )

      }

    })

    generatedData.push({ ...model, id: generateUniqueId() })

  }

  const mockResponse: Payload<any> = {
    payload: pagination
      ? {
        records:  createPagination( generatedData, params ),
        total,
        maxPages: params.pageSize ? Math.ceil( total / params?.pageSize ) : 0
      }
      : generatedData,
    error:  null,
    status: 200
  }

  if ( error === 'random' ) {

    const isError = Math.random() > 0.5

    if ( isError ) {

      errorMockResponse.error.forEach(( err ) => {
        setAlertOptions({
          message:  err.message,
          details:  err.details,
          severity: 'error',
        })
      })

      return new Promise( resolve => setTimeout(() => resolve( errorMockResponse ), requestDuration ))

    }

    return new Promise( resolve => setTimeout(() => resolve( mockResponse ), requestDuration ))

  }

  return new Promise( resolve => setTimeout(() => resolve( mockResponse ), requestDuration ))

}

/**
 * Creates a fetchy function that sends HTTP requests using Axios.
 * @param generateHeadersFunction A function that generates headers for the requests.
 * @returns The fetchy function.
 */
export function createFetchy( generateHeadersFunction?: () => AxiosRequestConfig['headers'] | Promise<AxiosRequestConfig['headers']> ) {

  /**
   * Sends an HTTP request.
   * @template T The type of the response payload.
   * @param config The request configuration.
   * @returns An object containing the response data, error information, and headers.
   */

  return async function<T>( config: CustomAxiosRequestConfig ): Promise<Payload<T>> {

    if ( config?.generateMockData )
      return generateMockData( typeof config.generateMockData === 'boolean' ? {} : config.generateMockData )

    // Set the default values for the request configuration

    config = {
      baseURL:    config.socket ? import.meta.env.VITE_SOCKET_BASE_URL : import.meta.env.VITE_BASE_URL,
      showError:  true,
      useHeaders: true,
      ...config,
    }

    let error: PayloadError[] = null
    let payload: T = null
    let headers: any = null

    const genericError: PayloadError = {
      message: 'Something went wrong.',
      details: 'We couldn\'t process your request. Please try again at a later time.',
    }

    // If the response status is 401 with no message, show the generic unauthorized error

    const genericUnauthorizedError: PayloadError = {
      message: 'Unauthorized.',
      details: 'You are not authorized to access this resource.',
    }

    try {

      // Generate headers if the generateHeadersFunction is provided
      // and the useHeaders flag is set to true

      if ( generateHeadersFunction && config.useHeaders ) {

        const generatedHeaders = await generateHeadersFunction()

        config.headers = {
          ...config.headers,
          ...generatedHeaders,
        }

      }

      const response          = await axios( config )
      const status            = response.status
      const statusOk          = Number.parseInt( String( status / 100 )) === 2 // ---- True if the status code is in the 200 range
      const serverError       = status >= 500 && status < 600 // --------------------- True if the status code is in the 500 range
      const hideErrorsOnProd  = import.meta.env.PROD ? serverError || [ 404 ].includes( status ) : false

      if ( !statusOk ) {

        // Process error responses

        if ( response.data.errorResponses ) {

          error = response.data.errorResponses.map(( err: PossibleResponseErrorType ) => ({
            message: err?.errorMessage,
            details: err?.errorDetails,
          }))

        }

        // Process errors

        else if ( response.data.errors ) {

          error = response.data.errors.map(( err: PossibleResponseErrorType ) => ({
            message: err?.message,
            details: err?.details,
          }))

        }

        // Use generic error if no specific error data is available

        else {

          if ( status === 401 )
            error = [ genericUnauthorizedError ]

          else error = [ genericError ]

        }

        // Display error message if showError flag is true and no
        // confirmation dialog is open, or if the hideErrorsOnProd flag is false.

        if ( config.showError && !confirmOptions.value.open && !hideErrorsOnProd ) {

          error.forEach(( err ) => {

            setAlertOptions({
              strict:   serverError,
              message:  err.message,
              details:  err.details,
              severity: 'error'
            })

          })

        }

        payload = null
        headers = response.headers

      }

      else {

        // Process successful response

        error = null
        payload = status === 204 ? null : response.data
        headers = response.headers

      }

      return {
        error,
        status,
        headers,
        payload
      }

    }

    catch ( err ) {

      // Return if the user is offline.

      if ( err.code === 'ERR_NETWORK' )
        return

      // Return if the error is auth msal error, redirect to login.

      if ( err instanceof BrowserAuthError || err instanceof InteractionRequiredAuthError ) {
        msalInstance.acquireTokenRedirect( loginRequest )
        return
      }

      // Display error message if showError flag is true and no confirmation dialog is open

      if ( config.showError && !confirmOptions.value.open ) {

        setAlertOptions({
          message:  err.message,
          details:  err.details,
          severity: 'error',
          strict:   true
        })

      }

      return {
        error:  [ { message: err.message, details: err.details } ],
        status: 401,
        headers,
        payload
      }

    }

  }

}
