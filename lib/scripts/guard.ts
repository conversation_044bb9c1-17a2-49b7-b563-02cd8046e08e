import { scopes } from '@lib/auth/scripts/guard'
import { defineComponent } from 'vue'

import type { PropType } from 'vue'

/**
 * @description - Guard factory function.
 * @param accessGuard - Function that handles the access control logic from the scope of the module.
 * @returns - Guard component and guard function.
 */

export function createGuard<ScopeModel, AccessScope extends string>( accessGuard: AccessGuard<ScopeModel, AccessScope> ) {

  // Create Vue functional guard component

  const Guard = defineComponent({

    name: 'Access Guard',

    props: { scope: { type: [ String, Array ] as PropType<AccessScope | AccessScope[]> } },

    setup( props, ctx ) {

      // Return only the default slot if the access is granted
      // without wrapping it in a div or any other element.

      return () => {

        if ( accessGuard( props.scope as AccessScope | AccessScope[], scopes.value ))
          return ctx.slots.default ? ctx.slots.default() : null

        return null

      }

    }

  })

  // Create guard function

  const guard = function ( scope: AccessScope | AccessScope[] ) {
    return accessGuard( scope, scopes.value )
  }

  return {
    component: Guard,
    function:  guard
  }

}
