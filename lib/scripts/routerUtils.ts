/**
 * Base routes model.
 * This routes are created by default and are always present in the application.
 */

const baseRoutes: RouteModel<any>[] = [
  {
    name:      'Auth Success',
    path:      '/auth-success',
    component: () => import( '@lib/auth/components/Success.vue' ),
  },
  {
    path:      '/404',
    name:      'NotFound',
    component: () => import( '@lib/components/pages/NotFound.vue' ),
  },
  {
    path:      '/access-denied',
    name:      'Access Denied',
    component: () => import( '@lib/components/pages/AccessDenied.vue' ),
  },
  {
    path:     '/:pathMatch(.*)*',
    redirect: { name: 'NotFound' },
  }
]

/**
 * Creates the final list of routes by combining the base routes with additional routes.
 * The base routes include a template success route, 404 route and a match all route that redirects to the 404 route.
 * @param routes - The additional routes to be added.
 * @returns The final list of routes.
 */

export function createRoutes<Scope>( routes: RouteModel<any, Scope>[] ): RouteModel<any, Scope>[] {

  // Combine the base routes with additional routes.

  return [
    ...baseRoutes,
    ...routes
  ]
}
