import { getScrollableParent } from '@lib/scripts/utils'

import type { TooltipArg, TooltipMods, TooltipProps } from '@lib/types/tooltipTypes'

interface TooltipBindings {
  arg?:       TooltipArg
  value:      TooltipProps
  modifiers?: TooltipMods
}

function ttElementStyle( type: TooltipArg ): Partial<CSSStyleDeclaration> {
  return {

    width:           'auto',
    height:          'auto',
    zIndex:          '5000',
    border:          !type ? '1px solid rgba( 232 232 232 / 1 )' : 'none',
    padding:         '8px 14px',
    maxWidth:        '340px',
    position:        'fixed',
    boxShadow:       '0px 10px 15px rgba(0 0 0 / 0.1)',
    borderRadius:    '2px',
    pointerEvents:   'none',
    backgroundColor: type === 'primary' ? 'rgb(35 86 246)' : type === 'warning' ? 'rgb(239 138 67)' : type === 'error' ? 'rgb(255 67 67)' : 'white'

  }
}

function arrowElementStyle( type: TooltipArg, position: string, changeDirection: boolean ): Partial<CSSStyleDeclaration> {
  return {

    top:             changeDirection ? 'none' : [ 'left', 'right' ].includes( position ) ? '50%' : '-6px',
    left:            position === 'left' ? 'none' : position === 'right' ? '-6px' : '50%',
    right:           [ 'right', 'center' ].includes( position ) ? 'none' : '-6px',
    width:           '12px',
    height:          '12px',
    bottom:          changeDirection ? [ 'left', 'right' ].includes( position ) ? '50%' : '-6px' : 'none',
    position:        'absolute',
    transform:       `${[ 'left', 'right' ].includes( position ) ? 'translateY(-50%)' : 'translateX(-50%)'} ${changeDirection ? 'rotate(-135deg)' : 'rotate(45deg)'}`,
    borderTop:       position === 'right' ? 'none' : !type ? '1px solid rgba( 232 232 232 / 1 )' : 'none',
    borderLeft:      position === 'left' ? 'none' : !type ? '1px solid rgba( 232 232 232 / 1 )' : 'none',
    borderRight:     position === 'left' ? !type ? '1px solid rgba( 232 232 232 / 1 )' : 'none' : 'none',
    borderBottom:    position === 'right' ? !type ? '1px solid rgba( 232 232 232 / 1 )' : 'none' : 'none',
    pointerEvents:   'none',
    backgroundColor: type === 'primary' ? 'rgb(35 86 246)' : type === 'warning' ? 'rgb(239 138 67)' : type === 'error' ? 'rgb(255 67 67)' : 'white'

  }
}

function titleElementStyle( type: TooltipArg ): Partial<CSSStyleDeclaration> {
  return {

    color:         [ 'primary', 'error' ].includes( type ) ? 'white' : 'black',
    fontSize:      '18px',
    fontWeight:    '500',
    pointerEvents: 'none'

  }
}

function contentElementStyle( type: TooltipArg ): Partial<CSSStyleDeclaration> {
  return {

    color:         [ 'primary', 'error' ].includes( type ) ? 'rgba( 255 255 255 / 0.8 )' : 'rgba( 0 0 0 / 0.8 )',
    fontSize:      '14px',
    pointerEvents: 'none'

  }
}

function createTooltip( el: HTMLElement, bindings: TooltipBindings ) {

  if ( !bindings?.value?.content )
    return

  if ( !el )
    return

  const { top, bottom, left, width, height } = el.getBoundingClientRect()

  const tooltipBindings = bindings as TooltipBindings

  if ( tooltipBindings.arg === 'hidden' )
    return

  const title           = bindings.value.title ?? null
  const position        = ( tooltipBindings.modifiers.left ?? tooltipBindings.value.position === 'left' ) ? 'left' : ( tooltipBindings.modifiers.right ?? tooltipBindings.value.position === 'right' ) ? 'right' : 'center'
  const ttElement       = document.createElement( 'div' )
  const arrowElement    = document.createElement( 'div' )
  const titleElement    = document.createElement( 'p' )
  const contentElement  = document.createElement( 'p' )

  if ( title ) {

    titleElement.textContent = title
    Object.assign( titleElement.style, titleElementStyle( bindings.arg || bindings.value?.type ))
    ttElement.append( titleElement )

  }

  contentElement.innerHTML = bindings.value.content
  Object.assign( contentElement.style, contentElementStyle( bindings.arg || bindings.value?.type ))

  ttElement.append( contentElement )

  ttElement.id = 'tooltip-container'

  Object.assign( ttElement.style, ttElementStyle( bindings.arg || bindings.value?.type ))

  document.body.append( ttElement )

  const ttBox = ttElement.getBoundingClientRect()
  const changeDirection = window.innerHeight - ( bottom + 10 ) < ttBox.height

  const positionLeft
    = position === 'right'
      ? ( left + width ) + 10
      : position === 'left'
        ? ( left - ttElement.offsetWidth ) - 10
        : ( left + ( width / 2 )) - ttElement.offsetWidth / 2

  const positionTop
    = position === 'center'
      ? changeDirection ? top - ( ttElement.clientHeight + 10 ) : bottom + 10
      : position === 'right' || position === 'left'
        ? ( top + ( height / 2 )) - ttElement.offsetHeight / 2
        : 0

  ttElement.style.left = `${positionLeft}px`
  ttElement.style.top = `${positionTop}px`

  Object.assign( arrowElement.style, arrowElementStyle( bindings.arg || bindings.value?.type, position, changeDirection ))
  ttElement.append( arrowElement )

}

let mountTimeout: NodeJS.Timeout = null

function mountTooltip( el: HTMLElement, binding: TooltipBindings ) {

  clearTimeout( mountTimeout )

  if ( !binding.value.content )
    return

  const scrollableParent = getScrollableParent( el )

  el.onmouseenter = () => {
    clearTimeout( mountTimeout )
    mountTimeout = setTimeout(() => createTooltip( el, binding ), binding.value.wait ?? 0 )
  }

  el.onmouseleave = () => {

    clearTimeout( mountTimeout )
    const ttElement: HTMLElement = document.getElementById( 'tooltip-container' )

    if ( ttElement )
      ttElement.remove()

  }

  scrollableParent.onscroll = () => {

    const ttElement: HTMLElement = document.getElementById( 'tooltip-container' )

    if ( ttElement )
      ttElement.remove()

  }

}

export const tooltip = {

  mounted( el: HTMLElement, binding: any ) { mountTooltip( el, binding ) },

  unmounted() {

    clearTimeout( mountTimeout )
    const ttElement: HTMLElement = document.getElementById( 'tooltip-container' )

    if ( ttElement )
      ttElement.remove()

  },

  beforeUpdate( el: HTMLElement, binding: any ) { mountTooltip( el, binding ) }

}
