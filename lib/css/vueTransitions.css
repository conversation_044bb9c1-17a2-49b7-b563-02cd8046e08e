.route-enter-active,
.route-leave-active {
	transition: all 0.3s ease;
}

.route-enter-from {
	@apply translate-y-5 opacity-0;
}

.route-leave-to {
	@apply -translate-y-5 opacity-0;
}

.droplist-enter-active,
.droplist-leave-active {
	transition: all 0.15s ease;
}

.droplist-enter-from {
	@apply -translate-x-full;
}

.droplist-leave-to {
	@apply translate-x-full;
}

.row-menu-enter-active,
.row-menu-leave-active {
	transition: all 0.15s ease;
}

.row-menu-enter-from {
	@apply scale-x-50 origin-right opacity-0 overflow-hidden;
}

.row-menu-leave-to {
	@apply scale-x-50 origin-right opacity-0 overflow-hidden;
}

.view-enter-active,
.view-leave-active {
	transition: all 0.2s ease;
}

.view-enter-from {
	@apply translate-y-2 opacity-0;
}

.view-leave-to {
	@apply -translate-y-2 opacity-0;
}

.calendar-enter-active,
.calendar-leave-active {
	transition: all 0.15s ease;
}

.calendar-enter-from {
	@apply translate-y-2 opacity-0;
}

.calendar-leave-to {
	@apply -translate-y-2 opacity-0;
}

.snackbar-enter-active,
.snackbar-leave-active {
  transition: all 0.6s ease;
}

.snackbar-enter-from,
.snackbar-leave-to {
  @apply translate-y-5 opacity-0;
}

.notification-enter-active,
.notification-leave-active {
  transition: all 0.6s ease;
}

.notification-enter-from,
.notification-leave-to {
  @apply translate-x-5 opacity-0;
}

.source-form-enter-active, .source-form-leave-active {
	transition: all 0.1s ease;
}

.source-form-enter-from {
	@apply translate-x-10 opacity-0;
}

.source-form-leave-to {
	@apply -translate-x-10 opacity-0;
}

.sidebar-modal-right-enter-active, 
.sidebar-modal-right-leave-active, 
.sidebar-modal-left-enter-active, 
.sidebar-modal-left-leave-active,
.sidebar-modal-bottom-enter-active, 
.sidebar-modal-bottom-leave-active {
	transition: all 0.3s ease;
}

.sidebar-modal-right-enter-from, .sidebar-modal-right-leave-to {
	@apply translate-x-full scale-x-50;
}

.sidebar-modal-left-enter-from, .sidebar-modal-left-leave-to {
	@apply -translate-x-full scale-x-50;
}

.sidebar-modal-bottom-enter-from, .sidebar-modal-bottom-leave-to {
	@apply translate-y-full scale-y-50;
}

.source-menu-enter-active, .source-menu-leave-active {
	transition: all 0.1s ease;
}

.source-menu-enter-from {
	@apply translate-x-4 opacity-0;
}

.source-menu-leave-to {
	@apply -translate-x-4 opacity-0;
}

.drawer-menu-enter-active {
	transition: all 0.5s ease;
}

.drawer-menu-leave-active {
  transition: all 0s ease;
}

.drawer-menu-enter-from {
	@apply translate-y-20 opacity-0;
}

.drawer-menu-leave-to {
	@apply -translate-x-4 opacity-0;
}

.notification-bar-enter-active, .notification-bar-leave-active {
  transition: all 0.1s ease;
}

.notification-bar-enter-from, .notification-bar-leave-to {
  @apply translate-x-[18.75rem];
}

.mobile-side-bar-enter-active, .mobile-side-bar-leave-active {
  transition: all 0.2s ease;
}

.mobile-side-bar-enter-from, .mobile-side-bar-leave-to {
  @apply -translate-x-full;
}

.scale-in-enter-active, .scale-in-leave-active {
  transition: all 0.1s ease;
}

.scale-in-enter-from, .scale-in-leave-to {
  @apply scale-90 opacity-0;
}

.mobile-view-right-enter-active, .mobile-view-right-leave-active, .mobile-view-left-enter-active, .mobile-view-left-leave-active {
	transition: all 0.5s ease;
}

.mobile-view-right-enter-from {
	@apply absolute translate-x-full;
}

.mobile-view-right-leave-to {
	@apply absolute -translate-x-full;
}

.mobile-view-left-enter-from {
	@apply absolute -translate-x-full;
}

.mobile-view-left-leave-to {
	@apply absolute translate-x-full;
}

.publish-bar-enter-active, .publish-bar-leave-active {
	transition: all 0.2s ease;
	transform-origin: top;
}

.publish-bar-enter-from, .publish-bar-leave-to {
	@apply scale-y-0;
}

