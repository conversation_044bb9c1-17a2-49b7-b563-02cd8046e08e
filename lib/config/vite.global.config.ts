import { loadEnv } from 'vite'
import { cwd, env } from 'node:process'
import { fileURLToPath, URL } from 'node:url'

import vue from '@vitejs/plugin-vue'
import tailwindcss from '@tailwindcss/vite'
import vueDevTools from 'vite-plugin-vue-devtools'

import type { UserConfig } from 'vite'

const suffix = loadEnv( env.NODE_ENV || 'development', cwd())?.VITE_URL_SUFFIX ?? '/'

export const globalConfig: UserConfig = {
  base:    env.NODE_ENV === 'production' ? suffix : '/',
  server:  { port: 3000 },
  plugins: [
    vue(),
    vueDevTools(),
    tailwindcss()
  ],
  resolve: {
    alias: {
      '@':    fileURLToPath( new URL( '../../src', import.meta.url )),
      '@lib': fileURLToPath( new URL( '../../lib', import.meta.url )),
    }
  }
}
