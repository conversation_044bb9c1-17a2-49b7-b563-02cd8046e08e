{"extends": "@vue/tsconfig/tsconfig.dom.json", "compilerOptions": {"tsBuildInfoFile": "../../node_modules/.tmp/tsconfig.tsbuildinfo", "target": "ES2020", "moduleDetection": "force", "module": "ESNext", "moduleResolution": "bundler", "paths": {"@/*": ["../../src/*"], "@lib/*": ["../../lib/*"]}, "allowImportingTsExtensions": true, "strict": false, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "noEmit": true, "plugins": [{"name": "@vue/typescript-plugin"}], "isolatedModules": true, "skipLibCheck": true, "noUncheckedSideEffectImports": true}, "files": [], "include": ["../../vite.config.ts", "../../src/**/*.ts", "../../lib/**/*.ts", "../../src/**/*.vue", "../../lib/**/*.vue", "../../src/**/*.d.ts", "../../lib/**/*.d.ts", "../../lib/types/global.d.ts"]}