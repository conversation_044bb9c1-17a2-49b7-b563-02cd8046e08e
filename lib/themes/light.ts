import type { Theme } from '@lib/types/themeTypes'

export const theme: Theme = {

  // ---- [ BACKGROUND ]

  background: {
    rgb:         { r: 255, g: 255, b: 255 },
    hex:         '#FFFFFF',
    value:       'rgb( 255 255 255 )',
    opacity:     null,
    description: 'Default page background, UI Shell base color'
  },
  backgroundHover: {
    rgb:         { r: 141, g: 141, b: 141 },
    hex:         '#8d8d8d',
    value:       'rgba( 141 141 141 / 12% )',
    opacity:     12,
    description: 'Hover state color for background'
  },
  backgroundActive: {
    rgb:         { r: 141, g: 141, b: 141 },
    hex:         '#8d8d8d',
    value:       'rgba( 141 141 141 / 50% )',
    opacity:     50,
    description: 'Active state color for background'
  },
  backgroundSelected: {
    rgb:         { r: 141, g: 141, b: 141 },
    hex:         '#8d8d8d',
    value:       'rgba( 141 141 141 / 20% )',
    opacity:     20,
    description: 'Selected state color for background'
  },
  backgroundSelectedHover: {
    rgb:         { r: 141, g: 141, b: 141 },
    hex:         '#8d8d8d',
    value:       'rgba( 141 141 141 / 32% )',
    opacity:     32,
    description: 'Hover state color for background-selected'
  },
  backgroundInverse: {
    rgb:         { r: 57, g: 57, b: 57 },
    hex:         '#393939',
    value:       'rgb( 57 57 57 )',
    opacity:     null,
    description: 'High contrast backgrounds, High contrast elements'
  },
  backgroundHoverInverse: {
    rgb:         { r: 76, g: 76, b: 76 },
    hex:         '#4c4c4c',
    value:       'rgb( 76 76 76 )',
    opacity:     null,
    description: 'Hover state color for background-inverse'
  },
  backgroundBrand: {
    rgb:         { r: 15, g: 98, b: 254 },
    hex:         '#0f62fe',
    value:       'rgb( 15 98 254 )',
    opacity:     null,
    description: 'Brand color for backgrounds'
  },

  // ---- [ LAYER ]

  layer01: {
    rgb:         { r: 244, g: 244, b: 244 },
    hex:         '#f4f4f4',
    value:       'rgb( 244 244 244 )',
    opacity:     null,
    description: 'Background color for layers on top of background'
  },
  layerHover01: {
    rgb:         { r: 232, g: 232, b: 232 },
    hex:         '#e8e8e8',
    value:       'rgb( 232 232 232 )',
    opacity:     null,
    description: 'Hover state color for layer01'
  },
  layerActive01: {
    rgb:         { r: 198, g: 198, b: 198 },
    hex:         '#c6c6c6',
    value:       'rgb( 198 198 198 )',
    opacity:     null,
    description: 'Active state color for layer01'
  },
  layerSelected01: {
    rgb:         { r: 224, g: 224, b: 224 },
    hex:         '#e0e0e0',
    value:       'rgb( 224 224 224 )',
    opacity:     null,
    description: 'Selected state color for layer01'
  },
  layerSelectedHover01: {
    rgb:         { r: 202, g: 202, b: 202 },
    hex:         '#cacaca',
    value:       'rgb( 202 202 202 )',
    opacity:     null,
    description: 'Hover state color for layer-selected01'
  },

  layer02: {
    rgb:         { r: 255, g: 255, b: 255 },
    hex:         '#FFFFFF',
    value:       'rgb( 255 255 255 )',
    opacity:     null,
    description: 'Background color for layers on top of layer-01'
  },
  layerHover02: {
    rgb:         { r: 232, g: 232, b: 232 },
    hex:         '#e8e8e8',
    value:       'rgb( 232 232 232 )',
    opacity:     null,
    description: 'Hover state color for layer-02'
  },
  layerActive02: {
    rgb:         { r: 198, g: 198, b: 198 },
    hex:         '#c6c6c6',
    value:       'rgb( 198 198 198 )',
    opacity:     null,
    description: 'Active state color for layer-02'
  },
  layerSelected02: {
    rgb:         { r: 224, g: 224, b: 224 },
    hex:         '#e0e0e0',
    value:       'rgb( 224 224 224 )',
    opacity:     null,
    description: 'Selected state color for layer-02'
  },
  layerSelectedHover02: {
    rgb:         { r: 202, g: 202, b: 202 },
    hex:         '#cacaca',
    value:       'rgb( 202 202 202 )',
    opacity:     null,
    description: 'Hover state color for layer-selected-02'
  },

  layer03: {
    rgb:         { r: 244, g: 244, b: 244 },
    hex:         '#f4f4f4',
    value:       'rgb( 244 244 244 )',
    opacity:     null,
    description: 'Background color for layers on top of layer-02'
  },
  layerHover03: {
    rgb:         { r: 232, g: 232, b: 232 },
    hex:         '#e8e8e8',
    value:       'rgb( 232 232 232 )',
    opacity:     null,
    description: 'Hover state color for layer03'
  },
  layerActive03: {
    rgb:         { r: 198, g: 198, b: 198 },
    hex:         '#c6c6c6',
    value:       'rgb( 198 198 198 )',
    opacity:     null,
    description: 'Active state color for layer03'
  },
  layerSelected03: {
    rgb:         { r: 224, g: 224, b: 224 },
    hex:         '#e0e0e0',
    value:       'rgb( 224 224 224 )',
    opacity:     null,
    description: 'Selected state color for layer03'
  },
  layerSelectedHover03: {
    rgb:         { r: 202, g: 202, b: 202 },
    hex:         '#cacaca',
    value:       'rgb( 202 202 202 )',
    opacity:     null,
    description: 'Hover state color for layer-selected-03'
  },

  layerSelectedInverse: {
    rgb:         { r: 22, g: 22, b: 22 },
    hex:         '#161616',
    value:       'rgb( 22 22 22 )',
    opacity:     null,
    description: 'High contrast elements; 4.5:1 AA element contrast'
  },
  layerSelectedDisabled: {
    rgb:         { r: 141, g: 141, b: 141 },
    hex:         '#8d8d8d',
    value:       'rgb( 141 141 141 )',
    opacity:     null,
    description: 'Disabled state color for selected layer'
  },

  // ---- [ BORDER ]

  borderInteractive: {
    rgb:         { r: 15, g: 98, b: 254 },
    hex:         '#0f62fe',
    value:       'rgb( 15 98 254 )',
    opacity:     null,
    description: 'Interactive or selected border color'
  },
  borderSubtle00: {
    rgb:         { r: 224, g: 224, b: 224 },
    hex:         '#e0e0e0',
    value:       'rgb( 224 224 224 )',
    opacity:     null,
    description: 'Subtle border color on background'
  },
  borderSubtle01: {
    rgb:         { r: 198, g: 198, b: 198 },
    hex:         '#c6c6c6',
    value:       'rgb( 198 198 198 )',
    opacity:     null,
    description: 'Subtle border color on layer-01'
  },
  borderSubtle02: {
    rgb:         { r: 224, g: 224, b: 224 },
    hex:         '#e0e0e0',
    value:       'rgb( 224 224 224 )',
    opacity:     null,
    description: 'Subtle border color on layer-02'
  },
  borderSubtle03: {
    rgb:         { r: 198, g: 198, b: 198 },
    hex:         '#c6c6c6',
    value:       'rgb( 198 198 198 )',
    opacity:     null,
    description: 'Subtle border color on layer-03'
  },
  borderSubtleSelected01: {
    rgb:         { r: 198, g: 198, b: 198 },
    hex:         '#c6c6c6',
    value:       'rgb( 198 198 198 )',
    opacity:     null,
    description: 'Selected state color for border-subtle-01'
  },
  borderSubtleSelected02: {
    rgb:         { r: 198, g: 198, b: 198 },
    hex:         '#c6c6c6',
    value:       'rgb( 198 198 198 )',
    opacity:     null,
    description: 'Selected state color for border-subtle-02'
  },
  borderSubtleSelected03: {
    rgb:         { r: 198, g: 198, b: 198 },
    hex:         '#c6c6c6',
    value:       'rgb( 198 198 198 )',
    opacity:     null,
    description: 'Selected state color for border-subtle-03'
  },
  borderStrong01: {
    rgb:         { r: 141, g: 141, b: 141 },
    hex:         '#8d8d8d',
    value:       'rgb( 141 141 141 )',
    opacity:     null,
    description: '3:1 AA color contrast border on layer-01, Border paired with field-01'
  },
  borderStrong02: {
    rgb:         { r: 141, g: 141, b: 141 },
    hex:         '#8d8d8d',
    value:       'rgb( 141 141 141 )',
    opacity:     null,
    description: '3:1 AA color contrast border on layer-02, Border paired with field-02'
  },
  borderStrong03: {
    rgb:         { r: 141, g: 141, b: 141 },
    hex:         '#8d8d8d',
    value:       'rgb( 141 141 141 )',
    opacity:     null,
    description: '3:1 AA color contrast border on layer-03, Border paired with field-03'
  },
  borderTitle01: {
    rgb:         { r: 198, g: 198, b: 198 },
    hex:         '#c6c6c6',
    value:       'rgb( 198 198 198 )',
    opacity:     null,
    description: 'Border color on layer-01 tile (background background)'
  },
  borderTitle02: {
    rgb:         { r: 168, g: 168, b: 168 },
    hex:         '#a8a8a8',
    value:       'rgb( 168 168 168 )',
    opacity:     null,
    description: 'Border color on field-02 tile (layer-01 background)'
  },
  borderTitle03: {
    rgb:         { r: 198, g: 198, b: 198 },
    hex:         '#c6c6c6',
    value:       'rgb( 198 198 198 )',
    opacity:     null,
    description: 'Border color on field-03 tile (layer-02 background)'
  },
  borderInverse: {
    rgb:         { r: 22, g: 22, b: 22 },
    hex:         '#161616',
    value:       'rgb( 22 22 22 )',
    opacity:     null,
    description: '4.5:1 AA color contrast border on all layers, Highest contrast border.'
  },
  borderDisabled: {
    rgb:         { r: 198, g: 198, b: 198 },
    hex:         '#c6c6c6',
    value:       'rgb( 198 198 198 )',
    opacity:     null,
    description: 'Disabled state color for borders (excluding subtle borders)'
  },

  // ---- [ TEXT ]

  textError: {
    rgb:         { r: 218, g: 30, b: 40 },
    hex:         '#da1e28',
    value:       'rgb( 218 30 40 )',
    opacity:     null,
    description: 'Error message text'
  },
  textHelper: {
    rgb:         { r: 111, g: 111, b: 111 },
    hex:         '#6f6f6f',
    value:       'rgb( 111 111 111 )',
    opacity:     null,
    description: 'Tertiary text, Help text'
  },
  textPrimary: {
    rgb:         { r: 22, g: 22, b: 22 },
    hex:         '#161616',
    value:       'rgb( 22 22 22 )',
    opacity:     null,
    description: 'Primary body copy, Headers, Hover text color for text-secondary, Selected text color'
  },
  textInverse: {
    rgb:         { r: 255, g: 255, b: 255 },
    hex:         '#ffffff',
    value:       'rgb( 255 255 255 )',
    opacity:     null,
    description: 'Text color on background-inverse'
  },
  textDisabled: {
    rgb:         { r: 22, g: 22, b: 22 },
    hex:         '#16161640',
    value:       'rgba( 22 22 22 / 25% )',
    opacity:     25,
    description: 'Disabled state color for text'
  },
  textSecondary: {
    rgb:         { r: 82, g: 82, b: 82 },
    hex:         '#525252',
    value:       'rgb( 82 82 82 )',
    opacity:     null,
    description: 'Secondary text color, Input labels color, Unselected text color'
  },
  textPlaceholder: {
    rgb:         { r: 168, g: 168, b: 168 },
    hex:         '#a8a8a8',
    value:       'rgb( 168 168 168 )',
    opacity:     null,
    description: 'Placeholder text color'
  },
  textHero: {
    rgb:         { r: 244, g: 244, b: 244 },
    hex:         '#F4F4F4',
    value:       'rgb( 244 244 244 )',
    opacity:     null,
    description: 'Hero text color'
  },
  textOnColor: {
    rgb:         { r: 255, g: 255, b: 255 },
    hex:         '#ffffff',
    value:       'rgb( 255 255 255 )',
    opacity:     null,
    description: 'Text on interactive colors'
  },
  textOnColorDisabled: {
    rgb:         { r: 141, g: 141, b: 141 },
    hex:         '#8d8d8d',
    value:       'rgb( 141 141 141 )',
    opacity:     null,
    description: 'Disabled state color for text-on-color'
  },

  // ---- [ LINK ]

  linkPrimary: {
    rgb:         { r: 15, g: 98, b: 254 },
    hex:         '#0f62fe',
    value:       'rgb( 15 98 254 )',
    opacity:     null,
    description: 'Primary link color, Ghost Button'
  },
  linkPrimaryHover: {
    rgb:         { r: 5, g: 74, b: 218 },
    hex:         '#054ada',
    value:       'rgb( 5 74 218 )',
    opacity:     null,
    description: 'Hover state color for link-primary'
  },
  linkSecondary: {
    rgb:         { r: 0, g: 67, b: 206 },
    hex:         '#0043ce',
    value:       'rgb( 0 67 206 )',
    opacity:     null,
    description: 'Secondary link color for lower contrast backgrounds'
  },
  linkInverse: {
    rgb:         { r: 120, g: 169, b: 255 },
    hex:         '#78a9ff',
    value:       'rgb( 120 169 255 )',
    opacity:     null,
    description: 'Links on inverse-02 backgrounds'
  },
  linkVisited: {
    rgb:         { r: 138, g: 63, b: 252 },
    hex:         '#8a3ffc',
    value:       'rgb( 138 63 252 )',
    opacity:     null,
    description: 'Visited link color'
  },

  // ---- [ ICON ]

  iconPrimary: {
    rgb:         { r: 22, g: 22, b: 22 },
    hex:         '#161616',
    value:       'rgb( 22 22 22 )',
    opacity:     null,
    description: 'Primary icon color'
  },
  iconSecondary: {
    rgb:         { r: 82, g: 82, b: 82 },
    hex:         '#525252',
    value:       'rgb( 82 82 82 )',
    opacity:     null,
    description: 'Secondary icon color'
  },
  iconInverse: {
    rgb:         { r: 255, g: 255, b: 255 },
    hex:         '#ffffff',
    value:       'rgb( 255 255 255 )',
    opacity:     null,
    description: 'Icon color on background-inverse'
  },
  iconDisabled: {
    rgb:         { r: 141, g: 141, b: 141 },
    hex:         '#16161640',
    value:       'rgba( 22 22 22 / 25% )',
    opacity:     25,
    description: 'Disabled state color for icons'
  },
  iconInteractive: {
    rgb:         { r: 15, g: 98, b: 254 },
    hex:         '#0f62fe',
    value:       'rgb( 15 98 254 )',
    opacity:     null,
    description: 'Interactive icon color'
  },
  iconOnColor: {
    rgb:         { r: 255, g: 255, b: 255 },
    hex:         '#ffffff',
    value:       'rgb( 255 255 255 )',
    opacity:     null,
    description: 'Icon color on interactive colors'
  },
  iconOnColorDisabled: {
    rgb:         { r: 141, g: 141, b: 141 },
    hex:         '#8d8d8d',
    value:       'rgb( 141 141 141 )',
    opacity:     null,
    description: 'Disabled state color for icon-on-color'
  },

  // ---- [ LAYER ACCENT ]

  layerAccent01: {
    rgb:         { r: 224, g: 224, b: 224 },
    hex:         '#e0e0e0',
    value:       'rgb( 224 224 224 )',
    opacity:     null,
    description: 'Accent background color used along side layer-01'
  },
  layerAccentHover01: {
    rgb:         { r: 202, g: 202, b: 202 },
    hex:         '#cacaca',
    value:       'rgb( 202 202 202 )',
    opacity:     null,
    description: 'Hover background color for layer-accent-01'
  },
  layerAccentActive01: {
    rgb:         { r: 168, g: 168, b: 168 },
    hex:         '#a8a8a8',
    value:       'rgb( 168 168 168 )',
    opacity:     null,
    description: 'Active background color for layer-accent-01'
  },

  layerAccent02: {
    rgb:         { r: 224, g: 224, b: 224 },
    hex:         '#e0e0e0',
    value:       'rgb( 224 224 224 )',
    opacity:     null,
    description: 'Accent background color used along side layer-02'
  },
  layerAccentHover02: {
    rgb:         { r: 202, g: 202, b: 202 },
    hex:         '#cacaca',
    value:       'rgb( 202 202 202 )',
    opacity:     null,
    description: 'Hover background color for layer-accent-02'
  },
  layerAccentActive02: {
    rgb:         { r: 168, g: 168, b: 168 },
    hex:         '#a8a8a8',
    value:       'rgb( 168 168 168 )',
    opacity:     null,
    description: 'Active background color for layer-accent-02'
  },

  layerAccent03: {
    rgb:         { r: 224, g: 224, b: 224 },
    hex:         '#e0e0e0',
    value:       'rgb( 224 224 224 )',
    opacity:     null,
    description: 'Accent background color used along side layer-03'
  },
  layerAccentHover03: {
    rgb:         { r: 202, g: 202, b: 202 },
    hex:         '#cacaca',
    value:       'rgb( 202 202 202 )',
    opacity:     null,
    description: 'Hover background color for layer-accent-03'
  },
  layerAccentActive03: {
    rgb:         { r: 168, g: 168, b: 168 },
    hex:         '#a8a8a8',
    value:       'rgb( 168 168 168 )',
    opacity:     null,
    description: 'Active background color for layer-accent-03'
  },

  // ---- [ SUPPORT ]

  supportUndefined: {
    rgb:         { r: 138, g: 63, b: 252 },
    hex:         '#8a3ffc',
    value:       'rgb( 138 63 252 )',
    opacity:     null,
    description: 'Undefined state color'
  },
  supportInfo: {
    rgb:         { r: 0, g: 67, b: 206 },
    hex:         '#0043ce',
    value:       'rgb( 0 67 206 )',
    opacity:     null,
    description: 'Info state color'
  },
  supportInfoInverse: {
    rgb:         { r: 69, g: 137, b: 255 },
    hex:         '#4589ff',
    value:       'rgb( 69 137 255 )',
    opacity:     null,
    description: 'Inverse text color for info state'
  },
  supportError: {
    rgb:         { r: 218, g: 30, b: 40 },
    hex:         '#da1e28',
    value:       'rgb( 218 30 40 )',
    opacity:     null,
    description: 'Error state color'
  },
  supportErrorInverse: {
    rgb:         { r: 250, g: 77, b: 86 },
    hex:         '#fa4d56',
    value:       'rgb( 250 77 86 )',
    opacity:     null,
    description: 'Inverse text color for error state'
  },
  supportSuccess: {
    rgb:         { r: 36, g: 161, b: 72 },
    hex:         '#24a148',
    value:       'rgb( 36 161 72 )',
    opacity:     null,
    description: 'Success state color'
  },
  supportSuccessInverse: {
    rgb:         { r: 66, g: 190, b: 101 },
    hex:         '#42be65',
    value:       'rgb( 66 190 101 )',
    opacity:     null,
    description: 'Inverse text color for success state'
  },
  supportWarning: {
    rgb:         { r: 241, g: 194, b: 27 },
    hex:         '#f1c21b',
    value:       'rgb( 241 194 27 )',
    opacity:     null,
    description: 'Warning state color'
  },
  supportWarningInverse: {
    rgb:         { r: 241, g: 194, b: 27 },
    hex:         '#f1c21b',
    value:       'rgb( 241 194 27 )',
    opacity:     null,
    description: 'Inverse text color for warning state'
  },
  supportCautionMinor: {
    rgb:         { r: 241, g: 194, b: 27 },
    hex:         '#f1c21b',
    value:       'rgb( 241 194 27 )',
    opacity:     null,
    description: 'Caution minor state color'
  },
  supportCautionMajor: {
    rgb:         { r: 255, g: 131, b: 43 },
    hex:         '#ff832b',
    value:       'rgb( 255 131 43 )',
    opacity:     null,
    description: 'Caution major state color'
  },

  // ---- [ FIELD ]

  field01: {
    rgb:         { r: 244, g: 244, b: 244 },
    hex:         '#f4f4f4',
    value:       'rgb( 244 244 244 )',
    opacity:     null,
    description: 'Input Field background color on background'
  },
  fieldHover01: {
    rgb:         { r: 232, g: 232, b: 232 },
    hex:         '#e8e8e8',
    value:       'rgb( 232 232 232 )',
    opacity:     null,
    description: 'Hover state color for field-01'
  },
  field02: {
    rgb:         { r: 255, g: 255, b: 255 },
    hex:         '#ffffff',
    value:       'rgb( 255 255 255 )',
    opacity:     null,
    description: 'Input Field background color on layer-01'
  },
  fieldHover02: {
    rgb:         { r: 232, g: 232, b: 232 },
    hex:         '#e8e8e8',
    value:       'rgb( 232 232 232 )',
    opacity:     null,
    description: 'Hover state color for field-02'
  },
  field03: {
    rgb:         { r: 244, g: 244, b: 244 },
    hex:         '#f4f4f4',
    value:       'rgb( 244 244 244 )',
    opacity:     null,
    description: 'Input Field background color on layer-02'
  },
  fieldHover03: {
    rgb:         { r: 232, g: 232, b: 232 },
    hex:         '#e8e8e8',
    value:       'rgb( 232 232 232 )',
    opacity:     null,
    description: 'Hover state color for field-03'
  },

  // ---- [ BUTTON ]

  buttonPrimary: {
    rgb:         { r: 15, g: 98, b: 254 },
    hex:         '#0f62fe',
    value:       'rgb( 15 98 254 )',
    opacity:     null,
    description: 'Primary button color'
  },
  buttonPrimaryHover: {
    rgb:         { r: 3, g: 83, b: 233 },
    hex:         '#0353e9',
    value:       'rgb( 3 83 233 )',
    opacity:     null,
    description: 'Hover state color for button-primary'
  },
  buttonPrimaryActive: {
    rgb:         { r: 0, g: 45, b: 156 },
    hex:         '#002d9c',
    value:       'rgb( 0 45 156 )',
    opacity:     null,
    description: 'Active state color for button-primary'
  },
  buttonSecondary: {
    rgb:         { r: 57, g: 57, b: 57 },
    hex:         '#393939',
    value:       'rgb( 57 57 57 )',
    opacity:     null,
    description: 'Secondary button color'
  },
  buttonSecondaryHover: {
    rgb:         { r: 76, g: 76, b: 76 },
    hex:         '#4c4c4c',
    value:       'rgb( 76 76 76 )',
    opacity:     null,
    description: 'Hover state color for button-secondary'
  },
  buttonSecondaryActive: {
    rgb:         { r: 111, g: 111, b: 111 },
    hex:         '#6f6f6f',
    value:       'rgb( 111 111 111 )',
    opacity:     null,
    description: 'Active state color for button-secondary'
  },
  buttonTertiary: {
    rgb:         { r: 15, g: 98, b: 254 },
    hex:         '#0f62fe',
    value:       'rgb( 15 98 254 )',
    opacity:     null,
    description: 'Tertiary button color'
  },
  buttonTertiaryHover: {
    rgb:         { r: 3, g: 83, b: 233 },
    hex:         '#0353e9',
    value:       'rgb( 3 83 233 )',
    opacity:     null,
    description: 'Hover state color for button-tertiary'
  },
  buttonTertiaryActive: {
    rgb:         { r: 0, g: 45, b: 156 },
    hex:         '#002d9c',
    value:       'rgb( 0 45 156 )',
    opacity:     null,
    description: 'Active state color for button-tertiary'
  },
  buttonDangerHover: {
    rgb:         { r: 186, g: 27, b: 35 },
    hex:         '#ba1b23',
    value:       'rgb( 186 27 35 )',
    opacity:     null,
    description: 'Hover state color for button-danger'
  },
  buttonDangerActive: {
    rgb:         { r: 117, g: 14, b: 19 },
    hex:         '#750e13',
    value:       'rgb( 117 14 19 )',
    opacity:     null,
    description: 'Active state color for button-danger'
  },
  buttonDangerPrimary: {
    rgb:         { r: 218, g: 30, b: 40 },
    hex:         '#da1e28',
    value:       'rgb( 218 30 40 )',
    opacity:     null,
    description: 'Primary danger button background color, 3:1 AA color contrast'
  },
  buttonDangerSecondary: {
    rgb:         { r: 218, g: 30, b: 40 },
    hex:         '#da1e28',
    value:       'rgb( 218 30 40 )',
    opacity:     null,
    description: 'Tertiary danger button border and text, Ghost danger button text, 4.5:1 AA color contrast'
  },
  buttonSuccessPrimary: {
    rgb:         { r: 14, g: 96, b: 39 },
    hex:         '#0e6027',
    value:       'rgb( 14 96 39 )',
    opacity:     null,
    description: 'Success primary button background color'
  },
  buttonSuccessSecondary: {
    rgb:         { r: 14, g: 96, b: 39 },
    hex:         '#0e6027',
    value:       'rgb( 14 96 39 )',
    opacity:     null,
    description: 'Success secondary button border and text color'
  },
  buttonSuccessHover: {
    rgb:         { r: 4, g: 67, b: 23 },
    hex:         '#044317',
    value:       'rgb( 4 67 23 )',
    opacity:     null,
    description: 'Hover state color for button-success'
  },
  buttonSuccessActive: {
    rgb:         { r: 2, g: 45, b: 13 },
    hex:         '#022d0d',
    value:       'rgb( 2 45 13 )',
    opacity:     null,
    description: 'Active state color for button-success'
  },
  buttonNeutralPrimary: {
    rgb:         { r: 105, g: 41, b: 196 },
    hex:         '#6929c4',
    value:       'rgb( 105 41 196 )',
    opacity:     null,
    description: 'Neutral primary button background color'
  },
  buttonNeutralSecondary: {
    rgb:         { r: 105, g: 41, b: 196 },
    hex:         '#6929c4',
    value:       'rgb( 105 41 196 )',
    opacity:     null,
    description: 'Neutral secondary button border and text color'
  },
  buttonNeutralHover: {
    rgb:         { r: 73, g: 29, b: 139 },
    hex:         '#491d8b',
    value:       'rgb( 73 29 139 )',
    opacity:     null,
    description: 'Hover state color for button-neutral'
  },
  buttonNeutralActive: {
    rgb:         { r: 49, g: 19, b: 94 },
    hex:         '#31135e',
    value:       'rgb( 49 19 94 )',
    opacity:     null,
    description: 'Active state color for button-neutral'
  },
  buttonDisabled: {
    rgb:         { r: 198, g: 198, b: 198 },
    hex:         '#c6c6c6',
    value:       'rgb( 198 198 198 )',
    opacity:     null,
    description: 'Disabled state color for buttons'
  },
  buttonSeparator: {
    rgb:         { r: 224, g: 224, b: 224 },
    hex:         '#e0e0e0',
    value:       'rgb( 224 224 224 )',
    opacity:     null,
    description: '3:1 AA element contrast, Fluid button separator'
  },

  // ---- [ TAG ]

  tagColorGray: {
    rgb:         { r: 22, g: 22, b: 22 },
    hex:         '#161616',
    value:       'rgb( 22 22 22 )',
    opacity:     null,
    description: 'Gray tag text, Gray tag icon'
  },
  tagHoverGray: {
    rgb:         { r: 209, g: 209, b: 209 },
    hex:         '#d1d1d1',
    value:       'rgb( 209 209 209 )',
    opacity:     null,
    description: 'Hover state color for tag-color-gray'
  },
  tagColorEnabled: {
    rgb:         { r: 22, g: 22, b: 22 },
    hex:         '#161616',
    value:       'rgb( 22 22 22 )',
    opacity:     null,
    description: 'Selectable tag text color'
  },
  tagColorSelected: {
    rgb:         { r: 255, g: 255, b: 255 },
    hex:         '#ffffff',
    value:       'rgb( 255 255 255 )',
    opacity:     null,
    description: 'Selected tag text color'
  },
  tagSelectableBackgroundHover: {
    rgb:         { r: 232, g: 232, b: 232 },
    hex:         '#e8e8e8',
    value:       'rgb(232 232 232)',
    opacity:     null,
    description: 'Hover state color for tag-selectable-background'
  },
  tagSelectableBackground: {
    rgb:         { r: 244, g: 244, b: 244 },
    hex:         '#4f4f4f',
    value:       'rgb( 244 244 244 )',
    opacity:     null,
    description: 'Selectable tag background color'
  },
  tagSelectedBackground: {
    rgb:         { r: 22, g: 22, b: 22 },
    hex:         '#161616',
    value:       'rgb( 22 22 22 )',
    opacity:     null,
    description: 'Selected tag background color'
  },
  tagSelectableBorder: {
    rgb:         { r: 22, g: 22, b: 22 },
    hex:         '#161616',
    value:       'rgb( 22 22 22 )',
    opacity:     null,
    description: 'Selectable tag border color'
  },
  tagBorderGray: {
    rgb:         { r: 168, g: 168, b: 168 },
    hex:         '#a8a8a8',
    value:       'rgb( 168 168 168 )',
    opacity:     null,
    description: 'Gray tag border for operational tag'
  },
  tagBackgroundGray: {
    rgb:         { r: 224, g: 224, b: 224 },
    hex:         '#e0e0e0',
    value:       'rgb( 224 224 224 )',
    opacity:     null,
    description: 'Tag background color for gray tags'
  },

  tagColorCoolGray: {
    rgb:         { r: 18, g: 22, b: 25 },
    hex:         '#121619',
    value:       'rgb( 18 22 25 )',
    opacity:     null,
    description: 'Cool gray tag text, Cool gray tag icon'
  },
  tagHoverCoolGray: {
    rgb:         { r: 205, g: 211, b: 218 },
    hex:         '#cdd3da',
    value:       'rgb( 205 211 218 )',
    opacity:     null,
    description: 'Hover state color for tag-color-cool-gray'
  },
  tagBorderCoolGray: {
    rgb:         { r: 162, g: 169, b: 176 },
    hex:         '#a2a9b0',
    value:       'rgb( 162 169 176 )',
    opacity:     null,
    description: 'Cool gray tag border for operational tag'
  },
  tagBackgroundCoolGray: {
    rgb:         { r: 221, g: 225, b: 230 },
    hex:         '#dde1e6',
    value:       'rgb( 221 225 230 )',
    opacity:     null,
    description: 'Tag background color for cool gray tags'
  },

  tagColorWarmGray: {
    rgb:         { r: 23, g: 20, b: 20 },
    hex:         '#171414',
    value:       'rgb( 23 20 20 )',
    opacity:     null,
    description: 'Warm gray tag text, Warm gray tag icon'
  },
  tagHoverWarmGray: {
    rgb:         { r: 216, g: 208, b: 207 },
    hex:         '#d8d0cf',
    value:       'rgb( 216 208 207 )',
    opacity:     null,
    description: 'Hover state color for tag-color-warm-gray'
  },
  tagBorderWarmGray: {
    rgb:         { r: 173, g: 168, b: 168 },
    hex:         '#ada8a8',
    value:       'rgb( 173 168 168 )',
    opacity:     null,
    description: 'Warm gray tag border for operational tag'
  },
  tagBackgroundWarmGray: {
    rgb:         { r: 229, g: 224, b: 223 },
    hex:         '#e5e0df',
    value:       'rgb( 229 224 223 )',
    opacity:     null,
    description: 'Tag background color for warm gray tags'
  },

  tagColorRed: {
    rgb:         { r: 162, g: 25, b: 31 },
    hex:         '#a2191f',
    value:       'rgb( 162 25 31 )',
    opacity:     null,
    description: 'Red tag text, Red tag icon'
  },
  tagHoverRed: {
    rgb:         { r: 255, g: 194, b: 197 },
    hex:         '#ffc2c5',
    value:       'rgb( 255 194 197 )',
    opacity:     null,
    description: 'Hover state color for tag-color-red'
  },
  tagBorderRed: {
    rgb:         { r: 255, g: 131, b: 137 },
    hex:         '#ff8389',
    value:       'rgb( 255 131 137 )',
    opacity:     null,
    description: 'Red tag border for operational tag'
  },
  tagBackgroundRed: {
    rgb:         { r: 255, g: 215, b: 217 },
    hex:         '#ffd7d9',
    value:       'rgb( 255 215 217 )',
    opacity:     null,
    description: 'Tag background color for red tags'
  },

  tagColorMagenta: {
    rgb:         { r: 159, g: 24, b: 83 },
    hex:         '#9f1853',
    value:       'rgb( 159 24 83 )',
    opacity:     null,
    description: 'Magenta tag text, Magenta tag icon'
  },
  tagHoverMagenta: {
    rgb:         { r: 255, g: 189, b: 218 },
    hex:         '#ffbddb',
    value:       'rgb( 255 189 218 )',
    opacity:     null,
    description: 'Hover state color for tag-color-magenta'
  },
  tagBorderMagenta: {
    rgb:         { r: 255, g: 126, b: 182 },
    hex:         '#ff7eb6',
    value:       'rgb( 255 126 182 )',
    opacity:     null,
    description: 'Magenta tag border for operational tag'
  },
  tagBackgroundMagenta: {
    rgb:         { r: 255, g: 214, b: 232 },
    hex:         '#ffd6e8',
    value:       'rgb( 255 214 232 )',
    opacity:     null,
    description: 'Tag background color for magenta tags'
  },

  tagColorPurple: {
    rgb:         { r: 105, g: 41, b: 196 },
    hex:         '#6929c4',
    value:       'rgb( 105 41 196 )',
    opacity:     null,
    description: 'Purple tag text, Purple tag icon'
  },
  tagHoverPurple: {
    rgb:         { r: 220, g: 199, b: 255 },
    hex:         '#dcc7ff',
    value:       'rgb( 220 199 255 )',
    opacity:     null,
    description: 'Hover state color for tag-color-purple'
  },
  tagBorderPurple: {
    rgb:         { r: 190, g: 149, b: 255 },
    hex:         '#be95ff',
    value:       'rgb( 190 149 255 )',
    opacity:     null,
    description: 'Purple tag border for operational tag'
  },
  tagBackgroundPurple: {
    rgb:         { r: 232, g: 218, b: 255 },
    hex:         '#e8daff',
    value:       'rgb( 232 218 255 )',
    opacity:     null,
    description: 'Tag background color for purple tags'
  },

  tagColorBlue: {
    rgb:         { r: 0, g: 67, b: 206 },
    hex:         '#0043ce',
    value:       'rgb( 0 67 206 )',
    opacity:     null,
    description: 'Blue tag text, Blue tag icon'
  },
  tagHoverBlue: {
    rgb:         { r: 184, g: 211, b: 255 },
    hex:         '#b8d3ff',
    value:       'rgb( 184 211 255 )',
    opacity:     null,
    description: 'Hover state color for tag-color-blue'
  },
  tagBorderBlue: {
    rgb:         { r: 120, g: 169, b: 255 },
    hex:         '#78a9ff',
    value:       'rgb( 120 169 255 )',
    opacity:     null,
    description: 'Blue tag border for operational tag'
  },
  tagBackgroundBlue: {
    rgb:         { r: 208, g: 226, b: 255 },
    hex:         '#d0e2ff',
    value:       'rgb( 208 226 255 )',
    opacity:     null,
    description: 'Tag background color for blue tags'
  },

  tagColorCyan: {
    rgb:         { r: 0, g: 83, b: 154 },
    hex:         '#00539a',
    value:       'rgb( 0 83 154 )',
    opacity:     null,
    description: 'Cyan tag text, Cyan tag icon'
  },
  tagHoverCyan: {
    rgb:         { r: 153, g: 218, b: 255 },
    hex:         '#99daff',
    value:       'rgb( 153 218 255 )',
    opacity:     null,
    description: 'Hover state color for tag-color-cyan'
  },
  tagBorderCyan: {
    rgb:         { r: 51, g: 177, b: 255 },
    hex:         '#33b1ff',
    value:       'rgb( 51 177 255 )',
    opacity:     null,
    description: 'Cyan tag border for operational tag'
  },
  tagBackgroundCyan: {
    rgb:         { r: 186, g: 230, b: 255 },
    hex:         '#bae6ff',
    value:       'rgb( 186 230 255 )',
    opacity:     null,
    description: 'Tag background color for cyan tags'
  },

  tagColorTeal: {
    rgb:         { r: 0, g: 93, b: 93 },
    hex:         '#005d5d',
    value:       'rgb( 0 93 93 )',
    opacity:     null,
    description: 'Teal tag text, Teal tag icon'
  },
  tagHoverTeal: {
    rgb:         { r: 87, g: 229, b: 229 },
    hex:         '#57e5e5',
    value:       'rgb( 87 229 229 )',
    opacity:     null,
    description: 'Hover state color for tag-color-teal'
  },
  tagBorderTeal: {
    rgb:         { r: 8, g: 189, b: 186 },
    hex:         '#08bdbd',
    value:       'rgb( 8 189 186 )',
    opacity:     null,
    description: 'Teal tag border for operational tag'
  },
  tagBackgroundTeal: {
    rgb:         { r: 158, g: 240, b: 240 },
    hex:         '#9ef0f0',
    value:       'rgb( 158 240 240 )',
    opacity:     null,
    description: 'Tag background color for teal tags'
  },

  tagColorGreen: {
    rgb:         { r: 14, g: 96, b: 39 },
    hex:         '#0e6027',
    value:       'rgb( 14 96 39 )',
    opacity:     null,
    description: 'Green tag text, Green tag icon'
  },
  tagHoverGreen: {
    rgb:         { r: 116, g: 231, b: 146 },
    hex:         '#74e792',
    value:       'rgb( 116 231 146 )',
    opacity:     null,
    description: 'Hover state color for tag-color-green'
  },
  tagBorderGreen: {
    rgb:         { r: 66, g: 190, b: 101 },
    hex:         '#42be65',
    value:       'rgb( 66 190 101 )',
    opacity:     null,
    description: 'Green tag border for operational tag'
  },
  tagBackgroundGreen: {
    rgb:         { r: 167, g: 240, b: 186 },
    hex:         '#a7f0ba',
    value:       'rgb( 167 240 186 )',
    opacity:     null,
    description: 'Tag background color for green tags'
  },
  tagColorOrange: {
    rgb:         { r: 138, g: 56, b: 0 },
    hex:         '#8A3800',
    value:       'rgb(138 56 0)',
    opacity:     null,
    description: 'Tag text color for orange tags'
  },
  tagBackgroundOrange: {
    rgb:         { r: 255, g: 131, b: 43 },
    hex:         '#FF832B',
    value:       'rgb(255 131 43)',
    opacity:     null,
    description: 'Tag background color for orange tags'
  },
  tagBorderOrange: {
    rgb:         { r: 255, g: 217, b: 190 },
    hex:         '#FFD9BE',
    value:       'rgb(255 217 190)',
    opacity:     null,
    description: 'Tag border for orange operational tags'
  },
  tagBackgroundDisabled: {
    rgb:         { r: 244, g: 244, b: 244 },
    hex:         '#0043cdF4F4F4',
    value:       'rgb(244 244 244)',
    opacity:     null,
    description: 'Disabled tag background color'
  },

  // ---- [ FOCUS ]

  focus: {
    rgb:         { r: 15, g: 98, b: 254 },
    hex:         '#0f62fe',
    value:       'rgb( 15 98 254 )',
    opacity:     null,
    description: 'Focus border, Focus underline'
  },
  focusInset: {
    rgb:         { r: 255, g: 255, b: 255 },
    hex:         '#ffffff',
    value:       'rgb( 255 255 255 )',
    opacity:     null,
    description: 'Inset border between focus element and the component'
  },
  focusInverse: {
    rgb:         { r: 255, g: 255, b: 255 },
    hex:         '#ffffff',
    value:       'rgb( 255 255 255 )',
    opacity:     null,
    description: 'Focus on high contrast moments'
  },

  // ---- [ MISC ]

  overlay: {
    rgb:         { r: 22, g: 22, b: 22 },
    hex:         '#161616',
    value:       'rgba( 22 22 22 / 50% )',
    opacity:     50,
    description: 'Overlay color'
  },
  highlight: {
    rgb:         { r: 208, g: 226, b: 255 },
    hex:         '#d0e2ff',
    value:       'rgb( 208 226 255 )',
    opacity:     null,
    description: 'Highlight color'
  },
  toggleOff: {
    rgb:         { r: 141, g: 141, b: 141 },
    hex:         '#8d8d8d',
    value:       'rgb( 141 141 141 )',
    opacity:     null,
    description: 'Toggle background color in the off state'
  },
  interactive: {
    rgb:         { r: 15, g: 98, b: 254 },
    hex:         '#0f62fe',
    value:       'rgb( 15 98 254 )',
    opacity:     null,
    description: '3:1 AA contrast for emphasized selected or active elements.'
  },
  skeletonElement: {
    rgb:         { r: 198, g: 198, b: 198 },
    hex:         '#c6c6c6',
    value:       'rgb( 198 198 198 )',
    opacity:     null,
    description: 'Skeleton state of text'
  },
  skeletonBackground: {
    rgb:         { r: 229, g: 229, b: 229 },
    hex:         '#e5e5e5',
    value:       'rgb( 229 229 229 )',
    opacity:     null,
    description: 'Skeleton state of graphics'
  },

  // ---- [ NOTIFICATIONS ]

  notificationInfoBorder: {
    rgb:         { r: 0, g: 67, b: 206 },
    hex:         '#0043CE4D',
    value:       'rgb(0 67 206 / 30%)',
    opacity:     null,
    description: 'Info notification border'
  },
  notificationInfoBackground: {
    rgb:         { r: 237, g: 245, b: 255 },
    hex:         '#EDF5FF',
    value:       'rgb(237 245 255)',
    opacity:     null,
    description: 'Info notification background'
  },
  notificationSuccessBorder: {
    rgb:         { r: 36, g: 161, b: 72 },
    hex:         '#24A1484D',
    value:       'rgb(36 161 72 / 30%)',
    opacity:     null,
    description: 'Success notification border'
  },
  notificationSuccessBackground: {
    rgb:         { r: 222, g: 251, b: 230 },
    hex:         '#DEFBE6',
    value:       'rgb(222 251 230)',
    opacity:     null,
    description: 'Success notification background'
  },
  notificationWarningBorder: {
    rgb:         { r: 241, g: 194, b: 27 },
    hex:         '#F1C21B4D',
    value:       'rgb(241 194 27 / 30%)',
    opacity:     null,
    description: 'Warning notification border'
  },
  notificationWarningBackground: {
    rgb:         { r: 252, g: 244, b: 214 },
    hex:         '#FCF4D6',
    value:       'rgb(252 244 214)',
    opacity:     null,
    description: 'Warning notification background'
  },
  notificationErrorBorder: {
    rgb:         { r: 218, g: 30, b: 40 },
    hex:         '#DA1E284D',
    value:       'rgb(218 30 40 / 30%)',
    opacity:     null,
    description: 'Error notification border'
  },
  notificationErrorBackground: {
    rgb:         { r: 255, g: 241, b: 241 },
    hex:         '#FFF1F1',
    value:       'rgb(255 241 241)',
    opacity:     null,
    description: 'Error notification background'
  },

  // ---- [ NOTIFICATIONS - INVERSE COLORS]

  notificationInfoBorderInverse: {
    rgb:         { r: 57, g: 57, b: 57 },
    hex:         '#393939',
    value:       'rgb(57 57 57)',
    opacity:     null,
    description: 'Info notification border'
  },
  notificationInfoBackgroundInverse: {
    rgb:         { r: 57, g: 57, b: 57 },
    hex:         '#393939',
    value:       'rgb(57 57 57)',
    opacity:     null,
    description: 'Info notification background'
  },
  notificationSuccessBorderInverse: {
    rgb:         { r: 57, g: 57, b: 57 },
    hex:         '#393939',
    value:       'rgb(57 57 57)',
    opacity:     null,
    description: 'Success notification border'
  },
  notificationSuccessBackgroundInverse: {
    rgb:         { r: 57, g: 57, b: 57 },
    hex:         '#393939',
    value:       'rgb(57 57 57)',
    opacity:     null,
    description: 'Success notification background'
  },
  notificationWarningBorderInverse: {
    rgb:         { r: 57, g: 57, b: 57 },
    hex:         '#393939',
    value:       'rgb(57 57 57)',
    opacity:     null,
    description: 'Warning notification border'
  },
  notificationWarningBackgroundInverse: {
    rgb:         { r: 57, g: 57, b: 57 },
    hex:         '#393939',
    value:       'rgb(57 57 57)',
    opacity:     null,
    description: 'Warning notification background'
  },
  notificationErrorBorderInverse: {
    rgb:         { r: 57, g: 57, b: 57 },
    hex:         '#393939',
    value:       'rgb(57 57 57)',
    opacity:     null,
    description: 'Error notification border'
  },
  notificationErrorBackgroundInverse: {
    rgb:         { r: 57, g: 57, b: 57 },
    hex:         '#393939',
    value:       'rgb(57 57 57)',
    opacity:     null,
    description: 'Error notification background'
  },

}
