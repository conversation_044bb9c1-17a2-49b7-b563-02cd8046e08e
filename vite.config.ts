/// <reference types="vitest" />

import { VitePWA } from 'vite-plugin-pwa'
import { globalConfig } from './lib/config/vite.global.config'
import { defineConfig, mergeConfig } from 'vite'

const config = mergeConfig(

  defineConfig( globalConfig ),
  defineConfig({
    resolve: {
      alias: {
        vue: 'vue/dist/vue.esm-bundler.js'
      },
    },
    test: {
      setupFiles:  './vitest.setup.ts',
      environment: 'happy-dom'
    },
    plugins: [
      VitePWA({
        registerType: 'autoUpdate',
        strategies:   'generateSW',
        manifest:     {
          name:  'OWD Client Portal',
          icons: [
            {
              src:   'pwa-64x64.png',
              sizes: '64x64',
              type:  'image/png'
            },
            {
              src:   'pwa-192x192.png',
              sizes: '192x192',
              type:  'image/png'
            },
            {
              src:   'pwa-512x512.png',
              sizes: '512x512',
              type:  'image/png'
            },
            {
              src:     'maskable-icon-512x512.png',
              sizes:   '512x512',
              type:    'image/png',
              purpose: 'maskable'
            }
          ],
          short_name:       'CP',
          description:      'OWD Client Portal App.',
          theme_color:      '#000000',
          background_color: '#000000'
        },
        workbox: {
          disableDevLogs: true,
          // Use network first instead of cache first
          runtimeCaching: [
            {
              urlPattern: /.*/,
              handler:    'NetworkFirst',
              options:    {
                cacheName:  'owd-client-portal-cache',
                expiration: {
                  maxEntries:    100,
                  maxAgeSeconds: 60 * 60 * 24 // 24 hours
                }
              }
            }
          ]
        },
        devOptions: {
          enabled: true
        }
      })
    ]
  })

)

export default config
