name: Frontend Deployment - Production

on:
  push:
    branches:
      - main

env:
  NODE_VERSION: 22.x

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    environment: production

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          submodules: false
          fetch-depth: 0

      - name: Configure and update submodules
        run: |
          git config --global url."https://x-access-token:${{ secrets.GH_ACCESS_TOKEN }}@github.com/".insteadOf "**************:"
          git submodule update --init --recursive
          cd lib
          git checkout ${{ secrets.LIB_BRANCH_PROD }}
          git pull

      - name: Install dependencies
        run: npm install

      - name: Create environment file
        run: |
          echo "${{ secrets.ENV_CONTENT_PROD }}" > .env

      - name: Build application
        run: |
          npm run build
          echo "DEPLOY_PATH=$(pwd)/dist" >> $GITHUB_ENV

      - name: Azure Login
        uses: azure/login@v2
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS_PROD }}

      - name: Upload new build to Azure Blob Storage
        uses: azure/cli@v2
        with:
          inlineScript: |
            az storage blob upload-batch \
              --account-name ${{ vars.PROD_STORAGE_ACCOUNT }} \
              --source '${{ env.DEPLOY_PATH }}' \
              --destination '$web' \
              --auth-mode login \
              --overwrite true

      - name: Purge CDN cache
        uses: azure/cli@v2
        with:
          azcliversion: latest
          inlineScript: |
            az cdn endpoint purge \
              --content-paths "/*" \
              --profile-name ${{ vars.PROD_CDN_PROFILE }} \
              --name ${{ vars.PROD_CDN_ENDPOINT }} \
              --resource-group ${{ vars.PROD_RESOURCE_GROUP }}
