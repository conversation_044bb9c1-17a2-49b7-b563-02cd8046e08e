name: Vue Build PR Check

on:
  pull_request:
    types: [opened, synchronize, reopened]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          submodules: false

      - name: Git Submodule Update
        run: |
          git config --global url."https://x-access-token:${{ secrets.REPO_ACCESS_TOKEN }}@github.com/".insteadOf "**************:"
          git submodule update --init --recursive
        env:
          REPO_ACCESS_TOKEN: ${{ secrets.REPO_ACCESS_TOKEN }}

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 22.x

      - name: Install dependencies
        run: npm ci

      - name: Build Vue project
        run: npm run build
        env:
          CI: true

      - name: Run UI tests
        run: npm run test
        env:
          CI: true
