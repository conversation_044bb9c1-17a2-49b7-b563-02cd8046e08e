import { t } from './src/localization/t'
import { vi } from 'vitest'
import { ref } from 'vue'
import { Guard } from './src/plugins/guard'
import { config } from '@vue/test-utils'
import { tooltip } from './lib/directives/tooltip'
import { tGlobal } from './lib/localization/tGlobal'
import { createI18n } from 'vue-i18n'
import { mergeObjects } from './lib/scripts/utils'
import { createRouter, createWebHistory } from 'vue-router'

import * as originalRouter from './src/router'

vi.mock( './src/plugins/fetchy', async ( importOriginal ) => { // -------- Global fetchy mock setup.
  const original = await importOriginal() as any
  return {
    ...original,
    fetchy: vi.fn(( ) => {
      return Promise.resolve({
        status:  200,
        payload: {},
        error:   {},
        headers: {
          'content-type': 'application/json'
        }
      })
    })
  }
})

const locale = 'en' // --------------------------------------------------- Default Locale for i18n.
const translations = mergeObjects( t, tGlobal ) // ----------------------- Merge translations.

const i18n = createI18n({ // --------------------------------------------- Global i18n mock setup.
  legacy:          false,
  locale,
  messages:        translations,
  fallbackLocale:  'en',
  globalInjection: true
})

const mockRoute = ref({ // ----------------------------------------------- Mock route object for useRoute and useRouter.
  name:     'Dashboard',
  path:     '/dashboard',
  params:   {},
  fullPath: '/dashboard',
  query:    {}
})

vi.mock( 'vue-router', async ( importOriginal ) => { // ------------------ Global router mock setup
  const original = await importOriginal() as any
  return {
    ...original,
    useRoute:  vi.fn(() => mockRoute.value ),
    useRouter: vi.fn(() => ({
      currentRoute: { value: mockRoute.value },
      push:         vi.fn(),
      resolve:      vi.fn(() => ({ href: mockRoute.value.path })),
    }))
  }
})

vi.mock( './lib/auth/scripts/guard', async ( importOriginal ) => { // ---- Mocking scopes and guard script
  const original = await importOriginal() as any
  return {
    ...original,
    scopes: ref( [
      {
        id:          7,
        key:         501,
        value:       'Asn.Read',
        isActive:    true,
        description: 'Read or List ASN objects',
        createdBy:   'owdsqlsa',
        modifiedBy:  null,
        createdTs:   '2024-08-14T04:53:43.900583+00:00',
        modifiedTs:  null,
      },
      {
        id:          8,
        key:         502,
        value:       'Asn.Write',
        isActive:    true,
        description: 'Create or Update ASN objects',
        createdBy:   'owdsqlsa',
        modifiedBy:  null,
        createdTs:   '2024-08-14T04:53:43.900583+00:00',
        modifiedTs:  null,
      },
      {
        id:          9,
        key:         551,
        value:       'Order.Read',
        isActive:    true,
        description: 'Read or List Order objects',
        createdBy:   'owdsqlsa',
        modifiedBy:  null,
        createdTs:   '2024-08-14T04:53:43.900583+00:00',
        modifiedTs:  null,
      },
      {
        id:          10,
        key:         552,
        value:       'Order.Write',
        isActive:    true,
        description: 'Create or Update Order objects',
        createdBy:   'owdsqlsa',
        modifiedBy:  null,
        createdTs:   '2024-08-14T04:53:43.900583+00:00',
        modifiedTs:  null,
      },
      {
        id:          11,
        key:         601,
        value:       'Inventory.Read',
        isActive:    true,
        description: 'Read or List Inventory objects',
        createdBy:   'owdsqlsa',
        modifiedBy:  'owdsqlsa',
        createdTs:   '2024-08-14T04:53:43.900583+00:00',
        modifiedTs:  '2024-09-25T15:29:21.849631+00:00',
      },
      {
        id:          12,
        key:         602,
        value:       'Inventory.Write',
        isActive:    true,
        description: 'Create or Update Inventory objects',
        createdBy:   'owdsqlsa',
        modifiedBy:  'owdsqlsa',
        createdTs:   '2024-08-14T04:53:43.900583+00:00',
        modifiedTs:  '2024-09-25T15:29:21.8652435+00:00',
      },
    ] )
  }
})

const router = createRouter({ // ----------------------------------------- Initialize a simple router with no routes
  history: createWebHistory(),
  routes:  originalRouter.default.getRoutes()
})

config.global.mocks = { // ----------------------------------------------- Ensure `scopes` is mocked globally
  $t:     ( key: string ) => translations[locale][key],
  $route: {}
}

config.global.components = { // ------------------------------------------ Global components registration
  Guard
}

config.global.directives = { // ------------------------------------------ Global directives registration
  tooltip
}

config.global.plugins = [ i18n, router ] // ------------------------------ Global plugins registration
config.global.renderStubDefaultSlot = true // ---------------------------- Enable default slot rendering for stubs
